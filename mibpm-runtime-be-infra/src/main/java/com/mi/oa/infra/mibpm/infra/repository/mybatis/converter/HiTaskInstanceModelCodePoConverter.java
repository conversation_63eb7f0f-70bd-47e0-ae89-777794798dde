package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.domain.task.model.HiTaskInstanceModelCode;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.HiTaskInstanceModelCodePo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/4/27 11:24
 */
@Mapper(componentModel = "spring")
public interface HiTaskInstanceModelCodePoConverter {

    HiTaskInstanceModelCodePo doToPo(HiTaskInstanceModelCode hiTaskInstanceModelCode);

    HiTaskInstanceModelCode poToDo(HiTaskInstanceModelCodePo hiTaskInstanceModelCodePo);

    List<HiTaskInstanceModelCode> poToDo(List<HiTaskInstanceModelCodePo> hiTaskInstanceModelCodePo);
}
