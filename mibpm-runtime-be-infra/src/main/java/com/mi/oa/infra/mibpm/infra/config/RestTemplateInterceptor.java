package com.mi.oa.infra.mibpm.infra.config;

import com.mi.oa.infra.mibpm.common.enums.ApiCallProtocolEnum;
import com.mi.oa.infra.mibpm.infra.remote.sdk.WorkbenchRemoteService;
import com.mi.oa.infra.oaucf.newauth.autoconfig.AuthProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 服务调用拦截器
 * 用于添加认证信息
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/1 18:51
 */
@Slf4j
public class RestTemplateInterceptor implements ClientHttpRequestInterceptor {

    private AuthProperties authProperties;
    private WorkbenchRemoteService workbenchRemoteService;

    public RestTemplateInterceptor(AuthProperties authProperties, WorkbenchRemoteService workbenchRemoteService){
        this.authProperties = authProperties;
        this.workbenchRemoteService = workbenchRemoteService;
    }

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] bytes, ClientHttpRequestExecution execution) throws IOException {
        // 获取请求头
        HttpHeaders headers = request.getHeaders();

        // 加入请求头认证信息
        headers.add("Authorization","Bearer " + getAuth(request));
        headers.add("X-Request-ID", MDC.get("logId") == null ? UUID.randomUUID().toString() : MDC.get("logId"));

        StopWatch watch = StopWatch.createStarted();
        // 执行请求
        ClientHttpResponse execute = execution.execute(request, bytes);
        watch.stop();

        // 添加耗时响应头
        execute.getHeaders().add("cost", String.valueOf(watch.getTime(TimeUnit.MILLISECONDS)));

        return execute;
    }



    private String getAuth(HttpRequest request){

        List<String> headers = request.getHeaders().get(HttpHeaders.PRAGMA);

        // 非http请求不进行认证
        if(Objects.isNull(headers) || !headers.contains(ApiCallProtocolEnum.HTTP.getCode())){
            return "";
        }
        String appCode = headers.get(headers.size() - 1);
        request.getHeaders().remove(HttpHeaders.PRAGMA);
        try {
            return this.getRemoteAppAuthToken(appCode);
        }catch (Exception e){
            log.error("请求认证信息失败!", e);
            return "";
        }
    }

    /**
     * 通过工作台获取认证token
     *
     * @return
     */
    private String getRemoteAppAuthToken(String serverAppId){

        String secretToken = workbenchRemoteService
                .applyAppToken(authProperties.getAppId(), authProperties.getAppSecret(), serverAppId);
        return secretToken;
    }
}
