package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @FileName DashBoardPermissionAssigneePo
 * @Description
 * <AUTHOR>
 * @date 2024-11-29
 **/
@Data
@TableName(value = "mi_bpm_dashboard_permission_assignee", autoResultMap = true)
public class DashBoardPermissionAssigneePo {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键
    private String userOrpid; // 用户名
    private String permissionUserId; // 被授权用户名
    private String createUser; // 创建人
    private Long createTime; // 创建时间
    private String updateUser; // 最后更新人
    private Long updateTime; // 最后更新时间
    private Integer isDeleted; // 是否删除，0: 未删除，1: 已删除
}
