package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.HiProcInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.NotifiedTaskPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

@Mapper
@Component
public interface HiProcInstMapper extends BaseMapper<HiProcInstPo> {

    @Select("select n.ID_           id, " +
            "       n.PROCESS_KEY_  processKey, " +
            "       n.TASK_DEF_KEY_ taskDefinitionKey, " +
            "       n.PROC_INST_ID_ processInstanceId, " +
            "       n.ASSIGNEE_     assignee, " +
            "       n.REVIEWED_     reviewed, " +
            "       n.CREATE_TIME_  createTime, " +
            "       n.UPDATE_TIME_  updateTime " +
            "from act_hi_notified_task n " +
            "         inner join act_hi_procinst p on p.ID_ = n.PROC_INST_ID_ " +
            " ${ew.customSqlSegment}")
    Page<NotifiedTaskPo> getPage(Page<NotifiedTaskPo> iPage, @Param(Constants.WRAPPER) Wrapper wrapper);
}
