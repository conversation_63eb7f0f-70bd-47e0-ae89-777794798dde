package com.mi.oa.infra.mibpm.infra.lock;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;


@Target({ ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface Lockable {

  /**
   * 锁名称
   */
  String lockName();

  /**
   * 锁冲突，重试次数
   */
  int retryCount() default 3;

  /**
   * 重试间隔时间
   */
  long retryTime() default 2;

  /**
   * 重试间隔时间单位
   */
  TimeUnit retryUnit() default TimeUnit.SECONDS;

  /**
   * 锁时长
   */
  long time() default 5;

  /**
   * 时长单位
   */
  TimeUnit unit() default TimeUnit.SECONDS;

}
