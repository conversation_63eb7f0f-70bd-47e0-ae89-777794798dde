package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/16 16:27
 *
 */
@Data
@TableName("mi_bpm_require_permission")
public class RequirePermissionPo extends BasePO<RequirePermissionPo> {
    /**
     * 用户名
     */
    private String userOrpid;

    /**
     * 权限名
     */
    private String permissionId;

    /**
     * 权限类型
     */
    private PermissionIdType permissionIdType;
}
