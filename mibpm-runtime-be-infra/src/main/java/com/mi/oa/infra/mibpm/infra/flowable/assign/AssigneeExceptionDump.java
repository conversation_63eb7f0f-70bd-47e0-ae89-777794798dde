package com.mi.oa.infra.mibpm.infra.flowable.assign;

/**
 * 获取审批人异常信息转储类
 *
 * <AUTHOR>
 * @date 2020/9/24 22:21
 */
public class AssigneeExceptionDump {

    static final ThreadLocal<Exception> DUMP = new ThreadLocal<>();

    public static void push(Exception e) {
        DUMP.set(e);
    }

    public static Exception pop() {
        Exception exception = DUMP.get();
        if (exception != null) {
            DUMP.remove();
        }
        return exception;
    }
}
