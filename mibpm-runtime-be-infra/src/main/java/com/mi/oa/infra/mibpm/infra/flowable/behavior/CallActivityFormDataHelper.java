package com.mi.oa.infra.mibpm.infra.flowable.behavior;

import com.mi.oa.infra.mibpm.flowable.extension.model.SubProcessDataBind;
import com.mi.oa.infra.mibpm.flowable.extension.model.SubProcessDataBindTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.SubProcessProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/19 15:20
 **/
@Service
public class CallActivityFormDataHelper {

    public Map<String, Object> fillParentToChild(Map<String, Object> formData, SubProcessProperty subProcessProperty) {
        if (MapUtils.isEmpty(formData) || Objects.isNull(subProcessProperty)) {
            return new HashMap<>();
        }
        List<SubProcessDataBind> parentData = subProcessProperty.getParentData();
        List<SubProcessDataBind.SubProcessDataBindItem> parentDataBind =
                parentData.stream().map(SubProcessDataBind::getParent).filter(i -> SubProcessDataBindTypeEnum.MODEL.equals(i.getType()))
                        .collect(Collectors.toList());
        Map<String, Object> variableLocal = new HashMap<>();
        for (SubProcessDataBind.SubProcessDataBindItem dataBindItem : parentDataBind) {
            List<String> relations = dataBindItem.getRelations();
            if (relations.size() == 2) {
                Object o = formData.get(dataBindItem.getValue());
                variableLocal.put(dataBindItem.getValue(), o);
            } else if (relations.size() == 3) {
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) formData.get(relations.get(1));
                if (null != dataList) {
                    List<Object> collect = dataList.stream().map(i -> i.get(dataBindItem.getValue())).collect(Collectors.toList());
                    variableLocal.put(dataBindItem.getValue(), collect);
                }
            }
        }
        return variableLocal;
    }

    public Map<String, Object> fillChildToChild(Map<String, Object> variables, SubProcessProperty subProcessProperty,
                                                Integer loopCounter) {
        if (MapUtils.isEmpty(variables) || Objects.isNull(subProcessProperty)) {
            return new HashMap<>();
        }
        List<SubProcessDataBind> parentData = subProcessProperty.getParentData();
        Map<String, Object> childFormData = new HashMap<>();
        switch (subProcessProperty.getSubGenerationRules()) {
            case EVERY:
            case EVERY_TABLE:
            case FIRST_TABLE:
                // 生成多条流程，拆分明细表
                generateTableDataSplit(variables, loopCounter, parentData, childFormData);
                break;
            case ONE:
                // 生成单条流程，不拆分明细表
                generateTableDataNoSplit(variables, parentData, childFormData);
                break;
        }
        return childFormData;
    }

    private void generateTableDataSplit(Map<String, Object> variables, Integer loopCounter, List<SubProcessDataBind> parentData, Map<String, Object> childFormData) {
        for (SubProcessDataBind dataBind : parentData) {
            List<String> relations = dataBind.getParent().getRelations();
            List<String> childRelation = dataBind.getChild().getRelations();
            String key = dataBind.getChild().getValue();
            if (relations.size() == 3) {
                List<Object> list = (List<Object>) variables.get(key);
                if (CollectionUtils.isNotEmpty(list)) {
                    Object o = list.get(loopCounter);
                    if (childRelation.size() == 2) {
                        childFormData.put(key, o);
                    } else {
                        handleFormTable(childFormData, childRelation, key, o, 0);
                    }
                }
            } else {
                childFormData.put(key, variables.get(key));
            }
        }
    }

    private void generateTableDataNoSplit(Map<String, Object> variables, List<SubProcessDataBind> parentData, Map<String, Object> childFormData) {
        for (SubProcessDataBind dataBind : parentData) {
            List<String> parentRelations = dataBind.getParent().getRelations();
            List<String> childRelation = dataBind.getChild().getRelations();
            String key = dataBind.getChild().getValue();
            if (parentRelations.size() == 3) {
                List<Object> list = (List<Object>) variables.get(key);
                if (CollectionUtils.isNotEmpty(list)) {
                    if (childRelation.size() == 2) {
                        childFormData.put(key, list.get(0));
                    } else {
                        for (int i = 0; i < list.size(); i++) {
                            Object o = list.get(i);
                            handleFormTable(childFormData, childRelation, key, o, i);
                        }
                    }
                }
            } else {
                childFormData.put(key, variables.get(key));
            }
        }
    }

    public static void handleFormTable(Map<String, Object> childFormData, List<String> childRelation, String key,
                                        Object data, int i) {
        String tableName = childRelation.get(1);
        List<Map<String, Object>> tableData = (List<Map<String, Object>>) childFormData.get(tableName);
        if (CollectionUtils.isEmpty(tableData)) {
            ArrayList<Map<String, Object>> dataList = new ArrayList<>();
            HashMap<String, Object> subMap = new HashMap<>();
            childFormData.put(tableName, dataList);
            subMap.put(key, data);
            dataList.add(subMap);
        } else {
            if (i < tableData.size()) {
                Map<String, Object> tableMap = tableData.get(i);
                tableMap.put(key, data);
            } else {
                HashMap<String, Object> subMap = new HashMap<>();
                subMap.put(key, data);
                tableData.add(subMap);
            }
        }
    }
}
