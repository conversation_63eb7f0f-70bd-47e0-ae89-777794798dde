package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @FileName DashBoardPermissionFeiShuDeptPo
 * @Description
 * <AUTHOR>
 * @date 2024-11-29
 **/
@Data
@TableName(value = "mi_bpm_dashboard_permission_feishu_dept", autoResultMap = true)
public class DashBoardPermissionFeiShuDeptPo {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键
    private String username; // 账号
    private String departmentPermission; // 部门权限
    private String departmentLevel; // 部门级别
    private String type; // 类型
    private String comment; // 备注
    private String createUser; // 创建人
    private Long createTime; // 创建时间
    private String updateUser; // 最后更新人
    private Long updateTime; // 最后更新时间
    private Integer isDeleted; // 是否删除，0: 未删除，1: 已删除
}
