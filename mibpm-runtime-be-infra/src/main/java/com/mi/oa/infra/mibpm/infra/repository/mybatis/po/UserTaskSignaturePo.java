package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/6 11:09
 **/
@Data
@TableName("mi_bpm_user_task_signature")
public class UserTaskSignaturePo {
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    private String taskId;
    private String processInstId;
    private String signature;
    @TableField("is_default")
    private boolean saveAsDefault;
    private String createUser;
    @TableField(
            fill = FieldFill.INSERT,
            typeHandler = ZonedDateTimeBigIntTypeHandler.class
    )
    private ZonedDateTime createTime;
}
