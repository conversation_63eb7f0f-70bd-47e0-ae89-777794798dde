package com.mi.oa.infra.mibpm.infra.flowable.assign.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mi.flowable.dmn.engine.extend.Matrix;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRuleType;
import com.mi.oa.infra.mibpm.flowable.extension.model.MatrixAssigneeRule;
import com.mi.oa.infra.mibpm.infra.flowable.assign.AbstractAssigneeService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiErpProcessAssignee;
import com.mi.oa.infra.mibpm.utils.ApprovalContextSynchronizationManager;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.dmn.api.DmnRuleService;
import org.flowable.dmn.api.ExecuteDecisionBuilder;
import org.flowable.engine.delegate.DelegateExecution;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 关系矩阵获取审批人
 *
 * <AUTHOR>
 * @description
 * @date 2022/3/9 4:04 PM
 **/
@Slf4j
@Service
public class MatrixAssigneeServiceImpl extends AbstractAssigneeService {

    @Override
    public AssigneeRuleType getAssigneeServiceKey() {
        return AssigneeRuleType.MATRIX;
    }

    @Autowired
    private AccountRemoteService accountRemoteService;

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, DelegateExecution execution) {
        // 决策表执行前将执行上下文信息存入threadLocal，供决策表人员类型执行结果规则找人用
        ApprovalContextSynchronizationManager.bindDmnEngineExecution(execution);
        MatrixAssigneeRule matrixAssigneeRule;
        if (assigneeRule instanceof MatrixAssigneeRule) {
            matrixAssigneeRule = (MatrixAssigneeRule) assigneeRule;
        } else {
            return Collections.emptyList();
        }

        Map<String, Object> variables = execution.getVariables();
        return getAssigneeList(matrixAssigneeRule, variables);
    }

    @NotNull
    private List<String> getAssigneeList(MatrixAssigneeRule matrixAssigneeRule, Map<String, Object> variables) {
        DmnRuleService dmnRuleService = SpringContextUtil.getBean(DmnRuleService.class);
        ExecuteDecisionBuilder executeDecisionBuilder = dmnRuleService.createExecuteDecisionBuilder()
                .decisionKey(matrixAssigneeRule.getKey());
        Map<String, Object> decisionVariables = new HashMap<>(matrixAssigneeRule.getInput().size());

        for (Map.Entry<String, String> entry : matrixAssigneeRule.getInput().entrySet()) {
            String variableName = entry.getKey();
            Object value = variables.get(entry.getValue());

            if (value instanceof String) {
                value = StringUtils.trim(String.valueOf(value));
                if (BpmVariablesConstants.VARIABLE_INITIATOR.equals(entry.getValue())) {
                    BpmUser user = accountRemoteService.getUser((String) value);
                    value = Objects.nonNull(user) ? user.getUserName() : value;
                }
            } else if (value instanceof List) {
                value = ((List) value).stream().collect(Collectors.joining(","));
            }
            decisionVariables.put(variableName, value);
        }
        executeDecisionBuilder.variables(decisionVariables);

        // 为了避免关系矩阵忘记设置默认输出条件报错，这里做下兼容
        List<Map<String, Object>> decisionResult;
        try {
            log.info("开始执行决策表, dmnKey={}, variables={}", matrixAssigneeRule.getKey(),
                    GsonUtil.toJsonString(decisionVariables));
            decisionResult = executeDecisionBuilder.execute();
            log.info("执行决策表结束, dmnKey={}, variables={}, result={}", matrixAssigneeRule.getKey(),
                    GsonUtil.toJsonString(decisionVariables), GsonUtil.toJsonString(decisionResult));
        } catch (Exception e) {
            logger.error("execute decision failure. dmnKey={}, variables={}", matrixAssigneeRule.getKey(),
                    GsonUtil.toJsonString(decisionVariables), e);
            decisionResult = new ArrayList<>();
        }

        List<MiErpProcessAssignee> miErpProcessAssignees = parseDecisionResult(decisionResult,
                matrixAssigneeRule);
        // 没有从关系矩阵中获取到审批人，获取默认审批人
        if (CollectionUtils.isEmpty(miErpProcessAssignees) && StringUtils.isNotBlank(
                matrixAssigneeRule.getDefaultValue())) {
            log.info("没有从决策表中获取到审批人，取默认审批人={}", matrixAssigneeRule.getDefaultValue());
            MiErpProcessAssignee tmpProcessAssignee = new MiErpProcessAssignee();
            tmpProcessAssignee.setValue(matrixAssigneeRule.getDefaultValue());
            miErpProcessAssignees.add(tmpProcessAssignee);
        }

        List<String> result = miErpProcessAssignees.stream().map(MiErpProcessAssignee::getValue)
                .collect(Collectors.toList());
        log.info("决策表计算结果={}", GsonUtil.toJsonString(result));
        return result;
    }

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, String processDefinitionId, Map<String, Object> variables) {
        MatrixAssigneeRule matrixAssigneeRule;
        if (assigneeRule instanceof MatrixAssigneeRule) {
            matrixAssigneeRule = (MatrixAssigneeRule) assigneeRule;
        } else {
            return Collections.emptyList();
        }

        return getAssigneeList(matrixAssigneeRule, variables);
    }

    @Override
    public AssigneeRule convert(MiErpProcessAssignee miErpProcessAssignee) {
        Gson gson = new Gson();
        Matrix matrix = gson.fromJson(miErpProcessAssignee.getValue(), new TypeToken<Matrix>() {
        }.getType());

        MatrixAssigneeRule matrixAssigneeRule = new MatrixAssigneeRule();
        matrixAssigneeRule.setInput(matrix.getInput());
        matrixAssigneeRule.setOutput(matrix.getOutput());
        matrixAssigneeRule.setKey(matrix.getKey());
        matrixAssigneeRule.setName(matrix.getName());
        return matrixAssigneeRule;
    }

    private List<MiErpProcessAssignee> parseDecisionResult(List<Map<String, Object>> decisionResults, MatrixAssigneeRule matrix) {
        List<MiErpProcessAssignee> resultList = new ArrayList<>();
        for (Map<String, Object> decisionResult : decisionResults) {
            Object outputValue = decisionResult.get(matrix.getOutput());
            if (outputValue instanceof String) {
                String assignees = (String) outputValue;

                String[] split = assignees.split(",");
                for (String assignee : split) {
                    MiErpProcessAssignee tmpProcessAssignee = new MiErpProcessAssignee();
                    tmpProcessAssignee.setValue(assignee);
                    resultList.add(tmpProcessAssignee);
                }
            }
        }
        return resultList;
    }
}
