package com.mi.oa.infra.mibpm.infra.flowable.behavior;

import org.apache.commons.collections4.CollectionUtils;
import org.flowable.bpmn.model.Activity;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior;
import org.flowable.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义并行多实例行为解释器
 *
 * <AUTHOR>
 * @date 2021/11/29 10:41
 */
public class CustomParallelCallActivityMultiInstanceBehavior extends ParallelMultiInstanceBehavior {

    private final CustomMultiInstanceBehaviorHelper customMultiInstanceBehaviorHelper;

    public CustomParallelCallActivityMultiInstanceBehavior(Activity activity,
                                                           AbstractBpmnActivityBehavior originalActivityBehavior,
                                                           CustomMultiInstanceBehaviorHelper customMultiInstanceBehaviorHelper) {
        super(activity, originalActivityBehavior);
        this.customMultiInstanceBehaviorHelper = customMultiInstanceBehaviorHelper;
    }

    @Override
    protected int createInstances(DelegateExecution multiInstanceRootExecution) {
        customMultiInstanceBehaviorHelper.createMultiInstances(multiInstanceRootExecution);
        return super.createInstances(multiInstanceRootExecution);
    }

    @Override
    protected void cleanupMiRoot(DelegateExecution execution) {
        ExecutionEntity multiInstanceRootExecution = (ExecutionEntity) getMultiInstanceRootExecution(execution);
        FlowElement flowElement = multiInstanceRootExecution.getCurrentFlowElement();
        ExecutionEntity parentExecution = multiInstanceRootExecution.getParent();

        ExecutionEntityManager executionEntityManager = CommandContextUtil.getExecutionEntityManager();
        Collection<String> executionIdsNotToSendCancelledEventsFor = execution.isMultiInstanceRoot() ? null : Collections.singletonList(execution.getId());
        List<ExecutionEntity> executionWithoutChildren = new ArrayList<>();
        this.collectChildrenWithoutSubProcess(multiInstanceRootExecution, executionWithoutChildren);
        List<String> executionIdsWithoutChildren = executionWithoutChildren.stream().map(ExecutionEntity::getId).collect(Collectors.toList());
        List<ExecutionEntity> allExecutions = executionEntityManager.collectChildren(multiInstanceRootExecution);
        List<String> allExecutionIds = allExecutions.stream().map(ExecutionEntity::getId).collect(Collectors.toList());
        Collection<String> excludeEntities = CollectionUtils.removeAll(allExecutionIds, executionIdsWithoutChildren);
        executionEntityManager.deleteChildExecutions(multiInstanceRootExecution, excludeEntities, executionIdsNotToSendCancelledEventsFor, DELETE_REASON_END, true, flowElement);
        executionEntityManager.deleteRelatedDataForExecution(multiInstanceRootExecution, DELETE_REASON_END);
        executionEntityManager.delete(multiInstanceRootExecution);

        ExecutionEntity newExecution = executionEntityManager.createChildExecution(parentExecution);
        newExecution.setCurrentFlowElement(flowElement);
        super.bpmnActivityBehavior.performDefaultOutgoingBehavior(newExecution);
    }

    protected void collectChildrenWithoutSubProcess(ExecutionEntity executionEntity,
                                                    List<ExecutionEntity> collectedChildExecution) {
        List<ExecutionEntity> childExecutions = (List<ExecutionEntity>) executionEntity.getExecutions();
        if (childExecutions != null && childExecutions.size() > 0) {
            // Have a fixed ordering of child executions (important for the order in which events are sent)
            Collections.sort(childExecutions, ExecutionEntity.EXECUTION_ENTITY_START_TIME_ASC_COMPARATOR);

            for (ExecutionEntity childExecution : childExecutions) {
                if (!childExecution.isDeleted()) {
                    collectedChildExecution.add(childExecution);
                }
                collectChildrenWithoutSubProcess(childExecution, collectedChildExecution);
            }
        }
    }
}
