package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.FormStoragReq;
import com.mi.oa.infra.mibpm.infra.procinst.repository.FormContentCacheRepository;
import com.mi.oa.infra.mibpm.utils.FormContentRedisKeyGenerator;
import com.mi.oa.infra.mibpm.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis implementation of FormContentCacheRepository
 *
 * <AUTHOR>
 * @date 2023/7/10
 */
@Slf4j
@Repository
public class FormContentCacheRepositoryImpl implements FormContentCacheRepository {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FormContentRedisKeyGenerator keyGenerator;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean cacheFormContent(FormStoragReq req, long expirationTimeInSeconds) {
        if (req == null || StringUtils.isBlank(req.getModelCode()) || StringUtils.isBlank(req.getUserName())) {
            throw new IllegalArgumentException("无法缓存表单，原因可能是模型编码为空  " +
                    "或者用户名缺失");
        }
        try {
            // 生成缓存键
            String key = keyGenerator.generateFormContentKey(
                    req.getModelCode(),
                    req.getUserName()
            );
            // 缓存表单数据(formData)
            String value = objectMapper.writeValueAsString(req.getFormData());
            redisUtil.set(key, value);
            redisUtil.expire(key, expirationTimeInSeconds, TimeUnit.SECONDS);
            log.info("表单缓存成功，缓存键为: {}", key);
            return true;
        } catch (JsonProcessingException e) {
            log.error("无法序列化表单内容", e);
            return false;
        } catch (Exception e) {
            log.error("缓存表单失败", e);
            return false;
        }
    }

    @Override
    public  Map<String, Object> getCachedFormContent(String modelCode, String userName) {
        if (StringUtils.isBlank(modelCode) || StringUtils.isBlank(userName)) {
            throw new IllegalArgumentException("无法获取表单缓存，模型编码或者用户名缺失");
        }
        try {
            String key = keyGenerator.generateFormContentKey(modelCode, userName);
            String value = redisUtil.get(key);
            if (StringUtils.isBlank(value)) {
                log.info("暂无缓存内容: {}", key);
                return null;
            }
            // 将缓存的JSON字符串反序列化为Map<String, Object>
            Map<String, Object> formData = objectMapper.readValue(value, Map.class);
            return formData;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("反序列化表单内容失败", e);
        } catch (Exception e) {
            throw new RuntimeException("获取表单内容失败", e);
        }
    }

    @Override
    public boolean removeCachedFormContent(String modelCode, String userId) {
        if (StringUtils.isBlank(modelCode) || StringUtils.isBlank(userId)) {
            log.warn("无法作废表单缓存，模型编码或者用户名缺失");
            return false;
        }
        try {
            String key = keyGenerator.generateFormContentKey(modelCode, userId);
            redisUtil.delete(key);
            log.info("表单缓存作废成功: {}", key);
            return true;
        } catch (Exception e) {
            log.error("作废表单内容失败", e);
            return false;
        }
    }
}
