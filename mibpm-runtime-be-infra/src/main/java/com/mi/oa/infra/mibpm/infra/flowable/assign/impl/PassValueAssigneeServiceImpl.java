package com.mi.oa.infra.mibpm.infra.flowable.assign.impl;

import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRuleType;
import com.mi.oa.infra.mibpm.flowable.extension.model.PassValueAssigneeRule;
import com.mi.oa.infra.mibpm.infra.flowable.assign.AbstractAssigneeService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiErpProcessAssignee;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 指定控制参数传入
 *
 * <AUTHOR>
 * @description
 * @date 2022/3/9 4:09 PM
 **/
@Service
public class PassValueAssigneeServiceImpl extends AbstractAssigneeService {
    @Override
    public AssigneeRuleType getAssigneeServiceKey() {
        return AssigneeRuleType.PASS_VALUE;
    }

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, DelegateExecution execution) {
        PassValueAssigneeRule passValueAssigneeRule;
        if (assigneeRule instanceof PassValueAssigneeRule) {
            passValueAssigneeRule = (PassValueAssigneeRule) assigneeRule;
        } else {
            return Collections.emptyList();
        }

        Map<String, Object> variables = execution.getVariables();
        return getAssigneeList(passValueAssigneeRule, variables);
    }

    @NotNull
    private List<String> getAssigneeList(PassValueAssigneeRule passValueAssigneeRule, Map<String, Object> variables) {
        List<String> resultList = new ArrayList<>();
        String variable = passValueAssigneeRule.getValue();
        Object o = variables.get(variable);
        if (o instanceof String) {
            String assignees = (String) o;
            if (StringUtils.isNotBlank(assignees)) {
                for (String assignee : assignees.split(ASSIGNEE_SEPARATOR)) {
                    resultList.add(assignee);
                }
            }
        } else if (o instanceof Collection) {
            Collection c = (Collection) o;
            if (CollectionUtils.isNotEmpty(c)) {
                Object[] objects = c.toArray();
                if (objects[0] instanceof String) {
                    for (Object object : objects) {
                        resultList.add((String) object);
                    }
                }
            }
        }
        return resultList;
    }

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, String processDefinitionId, Map<String, Object> variables) {
        PassValueAssigneeRule passValueAssigneeRule;
        if (assigneeRule instanceof PassValueAssigneeRule) {
            passValueAssigneeRule = (PassValueAssigneeRule) assigneeRule;
        } else {
            return Collections.emptyList();
        }

        return getAssigneeList(passValueAssigneeRule, variables);
    }

    @Override
    public AssigneeRule convert(MiErpProcessAssignee miErpProcessAssignee) {
        PassValueAssigneeRule passValueAssigneeRule = new PassValueAssigneeRule();
        passValueAssigneeRule.setValue(miErpProcessAssignee.getValue());
        return passValueAssigneeRule;
    }
}
