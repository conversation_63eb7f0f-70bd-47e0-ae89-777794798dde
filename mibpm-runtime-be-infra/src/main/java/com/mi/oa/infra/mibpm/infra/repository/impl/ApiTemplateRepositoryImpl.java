package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.infra.mibpm.common.enums.TemplateTypeEnum;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiTemplateDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.infra.apicall.repository.ApiTemplateRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.ApiTemplatePoConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.ApiTemplateMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.ProcessTemplateSettingMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ApiTemplatePo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ProcessTemplateSettingPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @author: qiuzhipeng
 * @Date: 2022/1/14 16:25
 */

@Repository
public class ApiTemplateRepositoryImpl implements ApiTemplateRepository {

    @Autowired
    private ApiTemplateMapper apiTemplateMapper;
    @Autowired
    private ApiTemplatePoConverter apiTemplatePoConverter;
    @Autowired
    private ProcessTemplateSettingMapper processTemplateSettingMapper;
    @Value("${mibpm.api-call.use-prod-config:false}")
    private Boolean useProdConfig;

    @Override
    public void saveApiTemplate(ApiTemplateDo apiTemplateDo) {
        //do -> po
        ApiTemplatePo apiTemplatePo = apiTemplatePoConverter.doToPo(apiTemplateDo);

        ApiTemplatePo apiTemplateInDb = apiTemplateMapper.selectOne(Wrappers.<ApiTemplatePo>lambdaQuery()
                .eq(ApiTemplatePo::getTemplateCode, apiTemplatePo.getTemplateCode()));
        if (Objects.isNull(apiTemplateInDb)) {
            apiTemplateMapper.insert(apiTemplatePo);
        } else {
            apiTemplatePo.setId(apiTemplateInDb.getId());
            apiTemplateMapper.updateById(apiTemplatePo);
        }
    }

    @Override
    public ApiTemplateDo queryApiTemplateByTemplateCode(String code) {

        ApiTemplatePo apiTemplatePo = apiTemplateMapper.selectOne(Wrappers.<ApiTemplatePo>lambdaQuery()
                .eq(ApiTemplatePo::getTemplateCode, code));
        if (apiTemplatePo == null) {
            return null;
        }
        ApiTemplateDo apiTemplateDo = apiTemplatePoConverter.poToDo(apiTemplatePo);
        apiTemplateDo.setTemplateType(TemplateTypeEnum.CUSTOM);
        if (Boolean.TRUE.equals(useProdConfig)) {
            if (StringUtils.isNotBlank(apiTemplatePo.getProdAppId())) {
                apiTemplateDo.setAppId(apiTemplatePo.getProdAppId());
            }
            if (StringUtils.isNotBlank(apiTemplatePo.getProdAppKey())) {
                apiTemplateDo.setAppKey(apiTemplatePo.getProdAppKey());
            }
            if (StringUtils.isNotBlank(apiTemplatePo.getProdUrl())) {
                apiTemplateDo.setUrl(apiTemplatePo.getProdUrl());
            }
        }
        return apiTemplateDo;
    }

    @Override
    public ApiTemplateDo queryDefaultTemplateByModelCode(String modelCode) {
        // 查询流程配置
        ProcessTemplateSettingPo processTemplateSettingPo = processTemplateSettingMapper
                .selectOne(Wrappers.<ProcessTemplateSettingPo>lambdaQuery()
                        .eq(ProcessTemplateSettingPo::getProcessKey, modelCode));
        return apiTemplatePoConverter.processTemplateSettingToApiTemplate(processTemplateSettingPo);
    }

    @Override
    public List<UserTaskOperation> queryCallBackOperation(String modelCode) {
        // 查询流程配置
        ProcessTemplateSettingPo processTemplateSettingPo = processTemplateSettingMapper
                .selectOne(Wrappers.<ProcessTemplateSettingPo>lambdaQuery()
                        .eq(ProcessTemplateSettingPo::getProcessKey, modelCode));

        List<UserTaskOperation> callbackTypes = new ArrayList<>();
        if (Objects.nonNull(processTemplateSettingPo)) {
            List<String> callbackType = Arrays.asList(processTemplateSettingPo.getCallbackType().split(","));
            for (String type : callbackType) {
                callbackTypes.add(UserTaskOperation.findByCode(type));
            }
        }
        return callbackTypes;
    }
}
