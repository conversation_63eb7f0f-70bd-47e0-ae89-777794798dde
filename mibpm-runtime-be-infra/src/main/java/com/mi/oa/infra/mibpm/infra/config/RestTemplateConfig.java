package com.mi.oa.infra.mibpm.infra.config;

import com.google.gson.reflect.TypeToken;
import com.mi.oa.infra.mibpm.infra.remote.sdk.WorkbenchRemoteService;
import com.mi.oa.infra.oaucf.newauth.autoconfig.AuthProperties;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.net.URI;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;

/**
 *
 * 服务调用 配置类
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/1 18:48
 */

@Slf4j
@Configuration
public class RestTemplateConfig {


    @Bean
    public RestTemplate getBeanRstTemplate(AuthProperties authProperties, WorkbenchRemoteService workbenchService){

        RestTemplate restTemplate = new RestTemplate(this.httpComponentsClientHttpRequestFactory());

        // 添加拦截器 主要用于鉴权
        restTemplate.getInterceptors().add(new RestTemplateInterceptor(authProperties, workbenchService));
        // 设置自定义异常处理器
        restTemplate.setErrorHandler(new CustomResponseErrorHandler());

        return restTemplate;
    }


    /**
     * 构造request factory
     * 使用 http client
     *
     * @return ClientHttpRequestFactory
     */
    private  HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory() {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        poolingConnectionManager.setMaxTotal(100);

        CloseableHttpClient client = HttpClientBuilder.create()
                .setConnectionManager(poolingConnectionManager)
                .setRedirectStrategy(new LaxRedirectStrategy())
                .build();

        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory =
                new HttpComponentsClientHttpRequestFactory(client);

        // 连接池中获取连接的超时时间
        clientHttpRequestFactory.setConnectionRequestTimeout(5000);
        // 与服务端建立连接的超时时间
        clientHttpRequestFactory.setConnectTimeout(5000);
        // SocketTimeOut
        clientHttpRequestFactory.setReadTimeout(8000);

        return clientHttpRequestFactory;
    }

}

@Slf4j
class CustomResponseErrorHandler extends DefaultResponseErrorHandler {

    @Override
    public void handleError(URI url, HttpMethod method, ClientHttpResponse response) throws IOException {
        HttpStatus statusCode = HttpStatus.resolve(response.getRawStatusCode());
        if (statusCode == null) {
            byte[] body = getResponseBody(response);
            String message = getCustomErrorMessage(response.getRawStatusCode(),
                    response.getStatusText(), body, getCharset(response));
            throw new UnknownHttpStatusCodeException(message,
                    response.getRawStatusCode(), response.getStatusText(),
                    response.getHeaders(), body, getCharset(response));
        }
        customHandleError(response, statusCode);
    }

    private void customHandleError(ClientHttpResponse response, HttpStatus statusCode) throws IOException {
        String statusText = response.getStatusText();
        HttpHeaders headers = response.getHeaders();
        byte[] body = getResponseBody(response);
        Charset charset = getCharset(response);
        String message = getCustomErrorMessage(statusCode.value(), statusText, body, charset);

        switch (statusCode.series()) {
            case CLIENT_ERROR:
                throw HttpClientErrorException.create(message, statusCode, statusText, headers, body, charset);
            case SERVER_ERROR:
                throw HttpServerErrorException.create(message, statusCode, statusText, headers, body, charset);
            default:
                throw new UnknownHttpStatusCodeException(message, statusCode.value(), statusText, headers, body, charset);
        }
    }

    private String getCustomErrorMessage(int rawStatusCode, String statusText, @Nullable byte[] responseBody, @Nullable
            Charset charset){

        String preface = rawStatusCode + " " + statusText + ": ";
        if (ObjectUtils.isEmpty(responseBody)) {
            return preface + "[no body]";
        }

        charset = charset == null ? StandardCharsets.UTF_8 : charset;

        try {
            String responseString = new String(responseBody, charset);
            LinkedHashMap<String, Object> res = GsonUtils.fromJson(responseString, new TypeToken<LinkedHashMap<String, Object>>(){
            }.getType());

            String message = res.get("message").toString();
            String code = res.get("code").toString();
            if(StringUtils.hasText(code) && StringUtils.hasText(message)){
                return message;
            }
        }catch (Exception e){
            log.warn("parse error message exception", e);
        }

        int maxChars = 200;

        if (responseBody.length < maxChars * 2) {
            return preface + "[" + new String(responseBody, charset) + "]";
        }

        try {
            Reader reader = new InputStreamReader(new ByteArrayInputStream(responseBody), charset);
            CharBuffer buffer = CharBuffer.allocate(maxChars);
            reader.read(buffer);
            reader.close();
            buffer.flip();
            return preface + "[" + buffer.toString() + "... (" + responseBody.length + " bytes)]";
        }
        catch (IOException ex) {
            // should never happen
            throw new IllegalStateException(ex);
        }
    }
}
