package com.mi.oa.infra.mibpm.infra.flowable.variable;

import com.mi.oa.infra.mibpm.flowable.extension.model.ProcessParameter;

import java.util.Map;

/**
 * 控制参数格式化
 *
 * <AUTHOR>
 * @date 2022/6/23 21:35
 */
public interface ParameterPattern {

    /**
     * 格式化指定的控制参数并回填到参数列表中
     *
     * @param parameter 参数定义
     * @param value     参数值
     * @return 格式化后的参数值
     */
    Object format(ProcessParameter parameter, Object value);
}
