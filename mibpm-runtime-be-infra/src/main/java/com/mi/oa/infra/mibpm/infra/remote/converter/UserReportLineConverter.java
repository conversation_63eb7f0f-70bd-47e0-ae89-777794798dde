package com.mi.oa.infra.mibpm.infra.remote.converter;

import org.mapstruct.Mapper;

import com.mi.oa.infra.mibpm.infra.remote.entity.UserReportLineEntity;
import com.mi.oa.infra.oaucf.idm.api.rep.ReportLineDto;

@Mapper(componentModel = "spring")
public interface UserReportLineConverter {

    default UserReportLineEntity convert(ReportLineDto userDo) {
        UserReportLineEntity user = new UserReportLineEntity();
        if (null != userDo) {
            user.setUid(userDo.getUid());
            user.setDisplayName(userDo.getDisplayName());
            user.setUserName(userDo.getUserName());
            user.setLevel(userDo.getLevel());
            user.setPostType(userDo.getPostType());
            user.setHrStatus(userDo.getHrStatus());
        }
        return user;
    }

}
