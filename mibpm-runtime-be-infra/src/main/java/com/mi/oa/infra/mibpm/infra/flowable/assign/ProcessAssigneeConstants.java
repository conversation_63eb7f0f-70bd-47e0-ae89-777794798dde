package com.mi.oa.infra.mibpm.infra.flowable.assign;

import java.util.LinkedHashSet;
import java.util.Set;

public class ProcessAssigneeConstants {
    /**
     * 等于
     */
    public static final String EQ = "=";
    /**
     * 不等于
     */
    public static final String NOT_EQ = "!=";
    /**
     * 小于
     */
    public static final String LT = "<";
    /**
     * 大于
     */
    public static final String GT = ">";
    /**
     * 小于等于
     */
    public static final String LE = "<=";
    /**
     * 大于等于
     */
    public static final String GE = ">=";
    /**
     * 字符串相等
     */
    public static final String EQUALS = "equals";
    /**
     * 字符串包含
     */
    public static final String CONTAINS = "contains";

    /**
     * 审批人
     */
    public static final Integer KEY_TYPE_ASSIGNEE = 1;
    /**
     * 抄送人
     */
    public static final Integer KEY_TYPE_CC = 2;
    /**
     * 操作按钮
     */
    public static final Integer KEY_TYPE_OPERATE_BTU = 3;

    /**
     * 固定人员
     */
    public static final Integer VALUE_TYPE_STATIC = 1;
    /**
     * 组
     */
    public static final Integer VALUE_TYPE_GROUP = 2;
    /**
     * 部门
     */
    public static final Integer VALUE_TYPE_DEPT = 3;
    /**
     * 关系矩阵
     */
    public static final Integer VALUE_TYPE_MATRIX = 4;
    /**
     * 业务系统传入
     */
    public static final Integer VALUE_TYPE_APP = 5;
    /**
     * 控制参数传入
     */
    public static final Integer VALUE_TYPE_PASSVALUE = 6;
    /**
     * 流程创建人
     */
    public static final Integer VALUE_TYPE_INITIATOR = 7;
    /**
     * 汇报线
     */
    public static final Integer VALUE_TYPE_REPORTLINE = 8;
    /**
     * 角色组
     */
    public static final Integer VALUE_TYPE_ROLE = 9;

    public static Set<String> conditionOperatorSet = new LinkedHashSet<String>();

    static {
        conditionOperatorSet.add(EQ);
        conditionOperatorSet.add(NOT_EQ);
        conditionOperatorSet.add(LT);
        conditionOperatorSet.add(GT);
        conditionOperatorSet.add(LE);
        conditionOperatorSet.add(GE);
        conditionOperatorSet.add(EQUALS);
        conditionOperatorSet.add(CONTAINS);
    }
}
