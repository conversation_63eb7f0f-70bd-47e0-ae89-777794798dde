package com.mi.oa.infra.mibpm.infra.flowable.bpmn;

import org.flowable.engine.InternalProcessLocalizationManager;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntity;
import org.flowable.engine.runtime.ProcessInstance;

/**
 * 流程实例国际化管理器
 *
 * @author: qiuzhipeng
 * @Date: 2022/7/27 14:07
 *
 */
public class ProcessLocalizationManager implements InternalProcessLocalizationManager {

    public ProcessLocalizationManager() {
    }

    @Override
    public void localize(ProcessInstance processInstance, String locale, boolean withLocalizationFallback) {
        ExecutionEntity processInstanceExecution = (ExecutionEntity)processInstance;
        processInstanceExecution.setLocalizedName(null);
        processInstanceExecution.setLocalizedDescription(null);
    }

    @Override
    public void localize(HistoricProcessInstance historicProcessInstance, String locale, boolean withLocalizationFallback) {
        HistoricProcessInstanceEntity processInstanceEntity = (HistoricProcessInstanceEntity)historicProcessInstance;
        processInstanceEntity.setLocalizedName(null);
        processInstanceEntity.setLocalizedDescription(null);
    }
}

