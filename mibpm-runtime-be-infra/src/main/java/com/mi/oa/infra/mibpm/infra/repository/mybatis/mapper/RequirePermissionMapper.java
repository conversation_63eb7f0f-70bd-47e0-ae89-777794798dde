package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.RequirePermissionPo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @Date 2024/7/16 16:36
 */
@Mapper
public interface RequirePermissionMapper extends BaseMapper<RequirePermissionPo> {
    @Delete("DELETE FROM mi_bpm_require_permission WHERE user_orpid = #{userOrpid} AND permission_id = #{permissionId}")
    int deletePermission(@Param("userOrpid") String userOrpid, @Param("permissionId") String permissionId);

    @Select("SELECT user_orpid, permission_id FROM mi_bpm_require_permission WHERE user_orpid = #{userOrpid} AND permission_id = #{permissionId}")
    RequirePermissionPo selectPermission(@Param("userOrpid") String userOrpid, @Param("permissionId") String permissionId);
}
