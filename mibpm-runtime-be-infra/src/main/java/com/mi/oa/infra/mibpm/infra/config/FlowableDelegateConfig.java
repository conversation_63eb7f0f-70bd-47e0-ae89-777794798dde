package com.mi.oa.infra.mibpm.infra.config;

import com.mi.oa.infra.form.core.api.remote.FormInstService;
import com.mi.oa.infra.mibpm.domain.apicall.service.ApiCallInstanceDomainService;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.infra.flowable.assign.BpmAssigneeService;
import com.mi.oa.infra.mibpm.infra.flowable.assign.impl.BpmAssigneeServiceImpl;
import com.mi.oa.infra.mibpm.infra.flowable.delegate.ServiceCallDelegate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * flowable 各种委托类配置
 *
 * <AUTHOR>
 * @date 2022/4/27 21:38
 */
@Configuration
public class FlowableDelegateConfig {

    @Bean
    public ServiceCallDelegate serviceCallDelegate(BpmnExtensionHelper bpmnExtensionHelper,
                                                   ApiCallInstanceDomainService apiCallInstanceDomainService,
                                                   FormInstService formInstService) {
        return new ServiceCallDelegate(bpmnExtensionHelper, apiCallInstanceDomainService, formInstService);
    }

    @Bean
    public BpmAssigneeService getBpmAssigneeService() {
        return new BpmAssigneeServiceImpl();
    }
}
