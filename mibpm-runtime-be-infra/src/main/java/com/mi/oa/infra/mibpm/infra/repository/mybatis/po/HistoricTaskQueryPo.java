package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.activiti.engine.impl.persistence.entity.HistoricTaskInstanceEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/25 10:43 AM
 **/
@Data
@TableName("act_hi_taskinst")
public class HistoricTaskQueryPo extends HistoricTaskInstanceEntity {


    @TableId(type = IdType.ASSIGN_UUID)
    protected String id;

    @TableField("PROC_DEF_ID_")
    protected String procDefId;

    @TableField("PROC_INST_ID_")
    protected String proInstId;

    @TableField("TASK_DEF_KEY_")
    protected String taskDefKey;

    @TableField("DURATION_")
    protected Long durationMillis;
}
