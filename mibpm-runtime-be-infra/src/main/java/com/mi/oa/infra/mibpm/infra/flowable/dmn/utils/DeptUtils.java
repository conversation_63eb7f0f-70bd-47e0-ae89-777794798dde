package com.mi.oa.infra.mibpm.infra.flowable.dmn.utils;

import com.mi.oa.infra.mibpm.common.model.Department;
import com.mi.oa.infra.mibpm.infra.remote.sdk.DeptRemote;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 决策表 部门拓展类型方法实现
 *
 * @author: qiuzhipeng
 * @Date: 2021/11/29 14:58
 */
@Slf4j
public class DeptUtils {

    /**
     * 部门匹配 包含
     *
     * @param dept  决策表规则值 （父）
     * @param value 输入参数 （子）
     * @return boolean 是否匹配
     */
    public static boolean deptContains(Object dept, Object value) {
        // 部门为"" 匹配任意值
        if (Objects.isNull(dept) || StringUtils.isBlank(dept.toString())) {
            return true;
        }

        if (Objects.isNull(value) || StringUtils.isBlank(value.toString())) {
            return false;
        }

        DeptRemote idmDeptRemote = SpringContextUtil.getBean(DeptRemote.class);

        Department department = idmDeptRemote.getDepartment((String) value);
        if (Objects.nonNull(department)) {
            List<String> deptCodes = department.getDeptPath()
                    .stream()
                    .map(Department::getDeptCode)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(deptCodes)) {
                for (String deptCode : deptCodes) {
                    if (StringUtils.equals(deptCode.trim(), dept.toString().trim())) {
                        return true;
                    }
                }
                return false;
            }
        }

        log.warn("deptCodes is empty! deptCode = {}", value.toString());
        return value.toString().startsWith(dept.toString());
    }

    /**
     * 部门匹配 不包含
     *
     * @param dept  决策表规则值
     * @param value 输入参数
     * @return boolean 是否匹配
     */
    public static boolean deptNotContains(Object dept, Object value) {
        return !deptContains(dept, value);
    }

    /**
     * 部门匹配 等于
     *
     * @param dept  决策表规则值
     * @param value 输入参数
     * @return boolean 是否匹配
     */
    public static boolean deptEquals(Object dept, Object value) {
        return StringUtils.equals(value.toString().trim(), dept.toString().trim());
    }

    /**
     * 部门匹配 不等于
     *
     * @param dept  决策表规则值
     * @param value 输入参数
     * @return boolean 是否匹配
     */
    public static boolean deptNotEquals(Object dept, Object value) {
        return !deptEquals(dept, value);
    }
}
