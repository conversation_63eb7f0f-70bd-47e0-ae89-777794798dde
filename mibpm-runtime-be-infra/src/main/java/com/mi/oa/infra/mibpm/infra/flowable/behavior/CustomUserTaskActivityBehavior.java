package com.mi.oa.infra.mibpm.infra.flowable.behavior;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.flowable.assign.AssigneeFactory;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.el.ExpressionManager;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.flowable.task.service.TaskService;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 自定义用户任务节点行为解析器
 * 1、扩充任务指派方式
 *
 * <AUTHOR>
 * @date 2021/11/18 20:03
 */
@Slf4j
public class CustomUserTaskActivityBehavior extends UserTaskActivityBehavior {

    protected UserTaskWrapper userTaskExtension;
    protected AssigneeFactory assigneeFactory;
    protected BpmnExtensionHelper bpmnExtensionHelper;

    public CustomUserTaskActivityBehavior(UserTask userTask,
                                          AssigneeFactory assigneeFactory,
                                          BpmnExtensionHelper bpmnExtensionHelper) {
        super(userTask);
        this.userTaskExtension = bpmnExtensionHelper.getUserTaskWrapper(userTask);
        this.assigneeFactory = assigneeFactory;
    }

    @Override
    protected void handleAssignments(TaskService taskService, String assignee, String owner, List<String> candidateUsers, List<String> candidateGroups, TaskEntity task, ExpressionManager expressionManager, DelegateExecution execution, ProcessEngineConfigurationImpl processEngineConfiguration) {
        if (userTaskExtension == null) {
            super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups,
                    task, expressionManager, execution, processEngineConfiguration);
            return;
        }

        List<AssigneeRule> assigneeRules = userTaskExtension.getAssigneeRules();
        UserTaskSignType signType = userTaskExtension.getSignType();
        if (assigneeRules == null) {
            super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups,
                    task, expressionManager, execution, processEngineConfiguration);
            return;
        }
        Map<String, Object> variablesLocal = execution.getVariablesLocal();
        if (null != variablesLocal && variablesLocal.containsKey(BpmVariablesConstants.VARIABLE_SIGN_TASK)) {
            assignee = (String) variablesLocal.get(BpmVariablesConstants.VARIABLE_ASSIGNEE);
        } else if (signType.equals(UserTaskSignType.SINGLE)) {
            for (AssigneeRule assigneeRule : assigneeRules) {
                List<String> miErpProcessAssignees = assigneeFactory.buildAssignee(assigneeRule, execution);
                if (!CollectionUtils.isEmpty(miErpProcessAssignees)) {
                    assignee = miErpProcessAssignees.get(0);
                }
            }
        } else if (UserTaskSignType.COMPETITION.equals(signType)) {
            // 多人竞签，处理候选人而不是审批人
            Set<String> candidatesSet = new HashSet<>();
            for (AssigneeRule assigneeRule : assigneeRules) {
                List<String> candidates = assigneeFactory.buildAssignee(assigneeRule, execution);
                candidatesSet.addAll(candidates);
            }
            candidateUsers = Lists.newArrayList(candidatesSet);
            // 执行审批策略
            candidateUsers = assigneeFactory.approveStrategyHandler(candidateUsers, execution, userTaskExtension);
            assignee = null;
        }
        // 执行审批策略
        assignee = approveStrategyHandler(assignee, execution, userTaskExtension);
        super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups, task,
                expressionManager, execution, processEngineConfiguration);
        handleVoteAssignees(execution, userTaskExtension, task);
    }

    private String approveStrategyHandler(String assignee, DelegateExecution execution, UserTaskWrapper userTaskWrapper) {
        // 处理审批人排除策略
        assignee = executeExcludeStrategyIfNecessary(assignee, execution, userTaskWrapper);
        // 处理审批人为空策略
        assignee = executeEmptyStrategyIfNecessary(assignee, execution, userTaskWrapper);
        return assignee;
    }

    private String executeExcludeStrategyIfNecessary(String assignee, DelegateExecution execution, UserTaskWrapper userTaskWrapper) {
        List<String> assignees
                = assigneeFactory.executeExcludeStrategyIfNecessary(new ArrayList<>(Collections.singletonList(assignee)), execution, userTaskWrapper);
        if (CollectionUtils.isEmpty(assignees)) {
            return null;
        }
        // 直接返回原审批人即可
        return assignee;
    }

    private String executeEmptyStrategyIfNecessary(String assignee, DelegateExecution execution, UserTaskWrapper userTaskWrapper) {
        if (!StringUtils.isEmpty(assignee)) {
            return assignee;
        }
        // 执行审批人为空策略
        List<String> strategyAssignees
                = assigneeFactory.executeEmptyStrategyIfNecessary(new ArrayList<>(), execution, userTaskWrapper);

        if (!CollectionUtils.isEmpty(strategyAssignees)) {
            return strategyAssignees.get(0);
        }
        return assignee;
    }

    private void handleVoteAssignees(DelegateExecution execution, UserTaskWrapper userTaskWrapper,
                                     TaskEntity task) {
        UserTaskSignType signType = userTaskWrapper.getSignType();
        if (!UserTaskSignType.VOTE.equals(signType) && !UserTaskSignType.ANONYMOUS_VOTE.equals(signType)) {
            return;
        }
        List<String> vetoAssignees =
                (List<String>) execution.getVariable(BpmVariablesConstants.VARIABLE_SYS_VETO_ASSIGNEES);
        if (!CollectionUtils.isEmpty(vetoAssignees) && vetoAssignees.contains(task.getAssignee())) {
            String description = task.getDescription();
            Map<String, Object> map;
            if (StringUtils.isEmpty(description)) {
                map = new LinkedHashMap<>();
            } else {
                map = GsonUtils.fromJson(description, LinkedHashMap.class);
            }
            map.put("isVetoTask", true);
            task.setDescription(GsonUtils.toJsonFilterNullField(map));
            task.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_IS_VETO_TASK, true);
            List<String> variable = (List<String>) execution.getVariable(BpmVariablesConstants.VARIABLE_VOTE_VETO_EXECUTION_IDS);
            if (CollectionUtils.isEmpty(variable)) {
                ArrayList<String> list = new ArrayList<>();
                list.add(task.getExecutionId());
                execution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_VETO_EXECUTION_IDS, list);
            } else {
                variable.add(task.getExecutionId());
                execution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_VETO_EXECUTION_IDS, variable);
            }
        }
    }
}
