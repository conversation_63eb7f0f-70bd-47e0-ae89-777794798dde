package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @Date 2024/7/17 11:50
 */
@Data
@TableName(value = "mi_bpm_dashboard_permission_user")
public class UserDashboardPermissionPo {
    /**
     * 用户名
     */
    private String userOrpid;

    /**
     * 被授权用户名
     */
    private String permissionUserId;

    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    @TableField(
            fill = FieldFill.INSERT
    )
    private String createUser;
    @TableField(
            fill = FieldFill.INSERT_UPDATE
    )
    private String updateUser;
    @TableField(
            fill = FieldFill.INSERT,
            typeHandler = ZonedDateTimeBigIntTypeHandler.class
    )
    private ZonedDateTime createTime;
    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            typeHandler = ZonedDateTimeBigIntTypeHandler.class
    )
    private ZonedDateTime updateTime;

}
