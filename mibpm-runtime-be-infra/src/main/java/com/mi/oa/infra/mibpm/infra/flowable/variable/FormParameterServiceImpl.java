package com.mi.oa.infra.mibpm.infra.flowable.variable;

import com.mi.oa.infra.mibpm.flowable.extension.model.ParameterSource;
import com.mi.oa.infra.mibpm.flowable.extension.model.ProcessParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用于处理来源为表单数据的控制参数
 *
 * <AUTHOR>
 * @date 2022/3/8 18:02
 */
@Slf4j
@Service
public class FormParameterServiceImpl implements ParameterService {

    @Override
    public ParameterSource support() {
        return ParameterSource.FORM;
    }

    @Override
    public void compute(ProcessParameter processParameter, Map<String, Object> processVariables) {
        String identification = processParameter.getIdentification();

        processFormCalc(identification, processVariables);
        Object resultValue = processVariables.get(identification);

        if(log.isDebugEnabled()) {
            log.debug("计算表单传入控制参数, processParameter={}, processVariables={}, resultValue={}.", processParameter.toString(), processVariables.toString(), resultValue);
        }
    }


    /**
     *
     * 自由表单 参数拼接获取
     * <p>
     * formdata参数示例
     * {
     * "formItemName": "formItemValue",
     * "tableName": [{
     * "columnName": "columnValue"
     * }]
     * }
     * 如果是获取table中的某一列的值 还需要 ProcessVariable 配置中指定才行
     */
    private void processFormCalc(String identification, Map<String, Object> processVariables) {
        //如果key是类似 xxx.xxx的格式 基本可以认定为是formtable 需要取某一列
        if (identification.contains(".")) {
            List<Object> variableValues = new ArrayList<>();
            String[] tempTableInfo = identification.split("\\.");
            String formTableName = tempTableInfo[0];
            String formTableColumnName = tempTableInfo[1];
            //只处理是数据的形式，不是数组的话 直接取对应的值
            if ((processVariables.get(formTableName) instanceof List)) {
                variableValues.addAll(((List<Map<String, Object>>) processVariables.get(formTableName)).stream().map(m -> m.get(formTableColumnName)).collect(Collectors.toList()));
            } else {
                variableValues.add(processVariables.get(identification));
            }

            processVariables.put(identification, variableValues);
        } else { //否则只要取对应map值就可以
            Object value = processVariables.get(identification);

            processVariables.put(identification, value);
        }

    }
}
