package com.mi.oa.infra.mibpm.infra.remote.sdk.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.reflect.TypeToken;
import com.mi.oa.infra.mibpm.common.exception.InfraException;
import com.mi.oa.infra.mibpm.common.model.Department;
import com.mi.oa.infra.mibpm.infra.remote.sdk.DeptRemote;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.idm.api.IdmDepartmentService;
import com.mi.oa.infra.oaucf.idm.api.errorcode.IdmErrorCodeEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.DepartmentDTO;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/26 16:43
 */
@Slf4j
@Component
public class DeptRemoteImpl implements DeptRemote {

    @Autowired
    private IdmDepartmentService idmDepartmentService;
    private final CacheLoader<String, Optional<Department>> cacheLoader = new CacheLoader<String, Optional<Department>>() {
        @Override
        public Optional<Department> load(String key) {
            Department department = getDepartmentInternal(key);

            return Optional.ofNullable(department);
        }
    };
    private final LoadingCache<String, Optional<Department>> cache = CacheBuilder.newBuilder()
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .initialCapacity(50)
            .maximumSize(200)
            .build(cacheLoader);

    @Override
    public Department getDepartment(String deptCode) {
        if (StringUtils.isBlank(deptCode)) {
            return null;
        }
        try {
            Optional<Department> department = cache.get(deptCode);
            return department.orElse(null);
        } catch (Exception e) {
            log.error("远程调用IDM根据部门id获取部门信息异常", e);
            return null;
        }
    }

    private Department getDepartmentInternal(String deptCode) {
        Resp<DepartmentDTO> response;
        log.info("远程调用IDM根据部门id获取部门信息, 入参 deptId = {}", deptCode);

        try {
            response = idmDepartmentService.findDeptByDeptId(deptCode);
        } catch (Exception e) {
            log.error("远程调用IDM根据部门id获取部门信息异常", e);
            throw new InfraException(IdmErrorCodeEnum.IDM_UNKNOWN_ERROR);
        }

        log.info("远程调用IDM根据部门id获取部门信息, 出参 = {}", response);

        if (response.getCode() != BaseResp.CODE_SUCCESS) {
            throw new InfraException(IdmErrorCodeEnum.IDM_UNKNOWN_ERROR);
        }

        DepartmentDTO idmDepartment = response.getData();
        if (Objects.isNull(idmDepartment)) {
            return null;
        }
        List<Department> deptPath = null;
        String deptFullDescr = idmDepartment.getDeptFullDescr();
        if (StringUtils.isNotEmpty(deptFullDescr)) {
            List<DepartmentDTO> deptParallel = GsonUtils.fromJson(deptFullDescr, new TypeToken<List<DepartmentDTO>>() {
            }.getType());

            if (CollectionUtils.isNotEmpty(deptParallel)) {
                deptPath = deptParallel.stream().map(departmentDTO -> Department.builder()
                        .deptCode(departmentDTO.getDeptId())
                        .deptName(departmentDTO.getDeptName())
                        .deptLevel(departmentDTO.getDeptLevel())
                        .status(departmentDTO.getStatus())
                        .build())
                        .collect(Collectors.toList());
            }
        }

        return Department.builder()
                .deptCode(idmDepartment.getDeptId())
                .deptName(idmDepartment.getDeptName())
                .deptLevel(idmDepartment.getDeptLevel())
                .deptPath(deptPath == null ? Collections.emptyList() : deptPath)
                .status(idmDepartment.getStatus())
                .build();
    }
}
