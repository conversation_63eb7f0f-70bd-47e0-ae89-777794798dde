package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.application.delegation.vo.QueryDelegationInfoVo;
import com.mi.oa.infra.mibpm.common.enums.DelegationScopeEnum;
import com.mi.oa.infra.mibpm.common.enums.DelegationStatusEnum;
import com.mi.oa.infra.mibpm.common.enums.DelegationTypeEnum;
import com.mi.oa.infra.mibpm.common.model.DelegationExclusionProcess;
import com.mi.oa.infra.mibpm.domain.delegation.model.DelegationInfoDo;
import com.mi.oa.infra.mibpm.common.model.DelegationProcess;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DelegationPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/23
 * @Description
 */
@Mapper(componentModel = "spring")
public interface DelegationInfoPoConverter {
    @Mappings({
            @Mapping(source = "delegatorId", target = "userId"),
            @Mapping(source = "approverAllId", target = "delegationUserId"),
            @Mapping(source = "createUserId", target = "createUser"),
            @Mapping(source = "updaterId", target = "updateUser"),
            @Mapping(source = "delegationType", target = "type"),
            @Mapping(source = "delegationStatusEnum", target = "status"),
            @Mapping(source = "delegationExclusionProcessList", target = "exclusionModelCodes")
    })
    DelegationPo doToPo(DelegationInfoDo delegationInfoDo);

    @Mappings({
            @Mapping(source = "approverPartId", target = "delegationUserId"),
    })
    DelegationPo subToPo(DelegationProcess delegationProcess, @MappingTarget DelegationPo po);

    default List<DelegationPo> doToPoList(DelegationInfoDo delegationInfoDo) {
        return delegationInfoDo.getDelegationProcessList().stream()
                .map(delegationProcess -> {
                    DelegationPo po = doToPo(delegationInfoDo);
                    subToPo(delegationProcess, po);
                    return po;
                })
                .collect(Collectors.toList());
    }

    default Integer map(DelegationTypeEnum delegationTypeEnum) {
        return delegationTypeEnum != null ? delegationTypeEnum.getCode() : null;
    }

    default Integer map(DelegationStatusEnum delegationStatusEnum) {
        return delegationStatusEnum != null ? delegationStatusEnum.getCode() : null;
    }

    default Integer map(DelegationScopeEnum delegationScopeEnum) {
        return delegationScopeEnum != null ? delegationScopeEnum.getCode() : null;
    }

    default List<String> map(String modeCode) {
        return modeCode != null ? Arrays.asList(modeCode.split(",")) : null;
    }

    default String map(List<String> modeCode) {
        return modeCode != null ? String.join(",", modeCode) : null;
    }

    default List<String> mapDelegationExclusionProcessListToExclusionModelCodes(List<DelegationExclusionProcess> delegationExclusionProcessList) {
        if (delegationExclusionProcessList == null || delegationExclusionProcessList.isEmpty()) {
            return Collections.emptyList();
        }
        return delegationExclusionProcessList.stream()
                .map(DelegationExclusionProcess::getExclusionModelCode)
                .collect(Collectors.toList());
        }

    QueryDelegationInfoVo poToVo(DelegationPo delegationPo);

}
