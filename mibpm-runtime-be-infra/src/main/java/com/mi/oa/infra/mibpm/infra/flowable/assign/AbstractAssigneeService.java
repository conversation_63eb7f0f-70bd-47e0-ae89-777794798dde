package com.mi.oa.infra.mibpm.infra.flowable.assign;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.RelativeLevelEnum;
import com.mi.oa.infra.mibpm.infra.remote.entity.UserReportLineEntity;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import com.mi.oa.infra.organization.rep.OrgVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.scripting.JuelScriptEngine;
import org.flowable.engine.impl.scripting.JuelScriptEngineFactory;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.script.ScriptException;

/**
 * <AUTHOR>
 * @date 2020/9/6 20:45
 */
public abstract class AbstractAssigneeService implements AssigneeService {

    protected static final java.lang.String ASSIGNEE_SEPARATOR = ",";
    protected Logger logger = LoggerFactory.getLogger(AbstractAssigneeService.class);
    public static final String LEI_JUN = "leijun";

    /**
     * EL表达式校验
     *
     * @param expression
     * @param execution
     * @return
     */
    protected Object checkElCondition(java.lang.String expression, DelegateExecution execution) {
        if (StringUtils.isBlank(expression)) {
            throw new RuntimeException("ConditionValue EL表达式为空");
        }
        if (expression.startsWith("$") == false) {
            throw new RuntimeException("ConditionValue EL表达式格式错误：示例\"${status=='agree'}\"");
        }
        JuelScriptEngine engines = (JuelScriptEngine) new JuelScriptEngineFactory().getScriptEngine();
        if (execution != null) {
            // 将变量信息放置到引擎中，用于表达式计算
            for (Map.Entry<java.lang.String, Object> entry : execution.getVariables().entrySet()) {
                engines.put(entry.getKey(), entry.getValue());
            }
            //将流程版本号放到变量中
            java.lang.String processDefinitionId = execution.getProcessDefinitionId();
            int procVersion = Integer.valueOf(StringUtils.split(processDefinitionId, ":")[1]);
            engines.put("procVersion", procVersion);
        }
        try {
            if (StringUtils.contains(expression, "StringUtils")) {
                //如果表达式存在字符串StringUtils，则认为引用了字符串工具类StringUtils，用法示例：${StringUtils:substring('liumingtao',0,3) == 'liu'}
                engines.compile(
                                "${lang:import(context, 'StringUtils', 'org.apache.commons.lang.StringUtils')}")
                        .eval();
            }
            return engines.compile(expression).eval();
        } catch (ScriptException e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 查找汇报线对应人员
     *
     * @param reportLineList
     * @param level 层级
     * @return
     */
    protected String findLevelManagerUser(List<BpmUser> reportLineList, int level) {
        int finalLevel = reportLineList.size() - level;
        if (finalLevel >= 0) {
            return reportLineList.get(finalLevel).getUserName();
        }
        return null;
    }

    /**
     * 查找单个上级领导
     *
     * @param reportLineList
     * @param level 层级
     * @return
     */
    protected String findSupperManagerUser(List<BpmUser> reportLineList, int level) {
        // idm 汇报线从1开始为汇报人本身，level为1时应取汇报线中level等2的值
        int finalLevel = ++level;

        return reportLineList.stream()
                .filter(i -> ((UserReportLineEntity) i).getLevel() == finalLevel)
                .findAny()
                .map(BpmUser::getUserName)
                .orElse(null);
    }

    /**
     * 向上审批到x级领导
     *
     * @param reportLineList
     * @param level 层级
     * @return
     */
    protected List<String> findUpSupperManagerUserList(List<BpmUser> reportLineList, int level) {
        // idm 汇报线从1开始为汇报人本身，level为1时应取汇报线中level等2的值
        int finalLevel = ++level;

        return reportLineList.stream()
                .filter(i -> ((UserReportLineEntity) i).getLevel() <= finalLevel)
                .map(BpmUser::getUserName).collect(Collectors.toList());
    }

    /**
     * 审批到x级领导
     *
     * @param reportLineList
     * @param level
     * @return
     */
    protected List<String> findToSupperManagerUserList(List<BpmUser> reportLineList, int level) {

        List<BpmUser> list = reportLineList.stream()
                .sorted(Comparator.comparingInt(a -> ((UserReportLineEntity) a).getLevel()))
                .collect(Collectors.toList());

        int curLevel = 1;
        for (int i = list.size() - 1; i >= 0; i--) {
            ((UserReportLineEntity) list.get(i)).setLevel(curLevel++);
        }

        return list.stream()
                .filter(i -> ((UserReportLineEntity) i).getLevel() >= level)
                .map(BpmUser::getUserName).collect(Collectors.toList());
    }

    protected Integer getOrgIndex(String level, String deptType, List<OrgVO> orgVOS) {

        RelativeLevelEnum levelEnum = RelativeLevelEnum.findByCode(level);
        int levelNumber = null == levelEnum ? 0 : levelEnum.getLevel();
        int deptLevel = 0;
        DeptTypeEnum deptTypeEnum = DeptTypeEnum.findByCode(deptType);
        if (null != deptTypeEnum) {
            deptLevel = deptTypeEnum.getLevel();
        }
        if (DeptTypeEnum.DIRECT_DEPT.equals(deptTypeEnum)) {
            if (deptLevel + levelNumber >= 0 && orgVOS.size() > deptLevel + levelNumber) {
                return orgVOS.size() - 1 - (deptLevel + levelNumber);
            }
        } else {
            int finalIndex = deptLevel - levelNumber;
            return Math.min(finalIndex, orgVOS.size() - 1);
        }
        return -1;
    }

    @NotNull
    protected static List<String> extractVariable(Map<String, Object> variables, String passvalue) {
        List<String> values = new ArrayList<>();
        Object o = variables.get(passvalue);
        if (o instanceof List) {
            values.addAll((Collection<? extends String>) o);
        } else if (o instanceof String) {
            String s = (String) o;
            String[] split = s.split(ASSIGNEE_SEPARATOR);
            values.addAll(Arrays.asList(split));
        }

        return values;
    }

    @NotNull
    protected static List<BpmUser> filterReportLine(List<BpmUser> reportLineUsers, BpmUser user) {
        return reportLineUsers.stream()
                .filter(i -> !user.getUserName().equals(i.getUserName())
                        && !LEI_JUN.equals(i.getUserName()))
                .collect(Collectors.toList());
    }

    @NotNull
    protected static List<BpmUser> filterReportLine(List<BpmUser> reportLineUsers, String user) {
        return reportLineUsers.stream().filter(i -> !user.equals(i.getUserName())
                && !user.equals(i.getUid())
                && !LEI_JUN.equals(i.getUserName())).collect(Collectors.toList());
    }

    @NotNull
    protected static List<BpmUser> filterReportLineSelf(List<BpmUser> reportLineUsers, BpmUser user) {
        return reportLineUsers.stream()
                .filter(i -> !user.getUserName().equals(i.getUserName()))
                .collect(Collectors.toList());
    }

    @NotNull
    protected static List<BpmUser> filterReportLineLei(List<BpmUser> reportLineUsers) {
        return reportLineUsers.stream()
                .filter(i -> !LEI_JUN.equals(i.getUserName()))
                .collect(Collectors.toList());
    }

    /**
     * 先查部门负责人在汇报线里的位置，然后再进行加减层级，获取最终的index
     *
     * @param orgVOS
     * @param deptTypeEnum
     * @param levelEnum
     * @param users
     * @return
     */
    protected int getOrgOwnerIndex(List<OrgVO> orgVOS, DeptTypeEnum deptTypeEnum, RelativeLevelEnum levelEnum,
                                   List<BpmUser> users) {
        Integer orgIndex = getOrgIndex(RelativeLevelEnum.PLUS_0.getCode(), deptTypeEnum.getCode(), orgVOS);
        Integer level = levelEnum.getLevel();
        if (orgIndex > -1 && orgIndex < orgVOS.size()) {
            OrgVO orgVO = orgVOS.get(orgIndex);
            String orgOwner = orgVO.getOrgOwner();
            for (int i = 0; i < users.size(); i++) {
                //这个users就是七个汇报线上所有的人 上到雷总 下到自己
                //正常情况下 汇报线中一般是会有部门负责人的 所以这里直接找就行了
                //但是特殊情况下 某个的汇报线中没有部门负责人 比如 部长助理 直接汇报给部长
                //那么这个users中就不包含部门负责人 所以这里就找不到人了，返回null
                //再返回null的情况下 就要立刻给流程管理员通过小米审批机器人 发送消息
                if (users.get(i).getUserName().equals(orgOwner)) {
                    if (i + level >= 0 && i + level <= users.size()) {
                        return i + level;
                    }
                }
            }
            sendAbnormalNotification(users, orgVO, deptTypeEnum);

        }
        return -1;
    }

    /**
     * 发送审批节点异常通知
     * @param users 用户汇报线
     * @param orgVO 指定部门的信息
     * @param deptTypeEnum 部门的类型
     */
    private void sendAbnormalNotification(List<BpmUser> users, OrgVO orgVO, DeptTypeEnum deptTypeEnum) {
        if (CollectionUtils.isEmpty(users)) {
            logger.warn("用户汇报线为空，无法发送异常通知");
            return;
        }
        try{
            TaskEntity taskEntity = getCurrentTask();
        }


    }

    /**
     * 获取当前正在执行的任务
     * @return
     */
    private TaskEntity getCurrentTask() {
        try{
            //spring上下文获取TaskService
            SpringContextUtil.getBean(org.flowable.engine.TaskService.class);
        }

    }
}
