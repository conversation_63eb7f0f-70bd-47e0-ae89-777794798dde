package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelHistoryPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ModelHistoryPoMapper extends BaseMapper<ModelHistoryPo> {

    default ModelHistoryPo queryLatestByModelId(String modelId) {
        return this.selectOne(
                new QueryWrapper<ModelHistoryPo>().lambda().eq(ModelHistoryPo::getModelId, modelId)
                        .orderByDesc(ModelHistoryPo::getVersion).last("limit 1"));
    }

    default List<ModelHistoryPo> listWithoutJson(String modelId) {
        return this.selectList(
                new QueryWrapper<ModelHistoryPo>().lambda()
                        .select(ModelHistoryPo::getId, ModelHistoryPo::getName, ModelHistoryPo::getModelKey,
                                ModelHistoryPo::getDescription, ModelHistoryPo::getModelComment,
                                ModelHistoryPo::getCreated,
                                ModelHistoryPo::getCreatedBy, ModelHistoryPo::getLastUpdatedBy,
                                ModelHistoryPo::getLastUpdated,
                                ModelHistoryPo::getRemovalDate, ModelHistoryPo::getVersion,
                                ModelHistoryPo::getModelId, ModelHistoryPo::getModelType, ModelHistoryPo::getTenantId)
                        .eq(ModelHistoryPo::getModelId, modelId));
    }

    default ModelHistoryPo queryByModelIdAndVersion(String modelId, int version) {
        return this.selectOne(
                new QueryWrapper<ModelHistoryPo>().lambda().eq(ModelHistoryPo::getModelId, modelId)
                        .eq(ModelHistoryPo::getVersion, version).last("limit 1"));
    }
}
