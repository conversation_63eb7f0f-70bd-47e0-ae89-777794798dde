package com.mi.oa.infra.mibpm.infra.repository.deploy.manager;

import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.flowable.extension.model.FormBindRelation;
import com.mi.oa.infra.mibpm.infra.models.entity.DeployContext;
import com.mi.oa.infra.mibpm.infra.models.entity.ModelDo;
import com.mi.oa.infra.mibpm.infra.remote.sdk.FormRemoteService;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/22 15:55
 */
@Service
@Slf4j
public class FormDeployManager extends AbstractDeployManager {
    @Autowired
    private FormRemoteService formRemoteService;

    @Override
    public ModelType support() {
        return ModelType.MODEL_TYPE_FORM;
    }

    @Override
    public void deploy(DeployContext deployContext) {
        ModelDo formModel = deployContext.getModel();
        if (StringUtils.isNotBlank(formModel.getModelEditorContent())) {
            ProcessDefinition processDefinition = deployContext.getProcessDefinition();

            if (Objects.isNull(processDefinition)) {
                log.warn("流程定义不存在");
                return;
            }

            FormBindRelation formBindRelation = GsonUtils.fromJson(formModel.getModelEditorContent(), FormBindRelation.class);
            if (formBindRelation != null && StringUtils.isNotBlank(formBindRelation.getFormModelId())) {
                // 第三方表单 不需要部署
                return;
            }
            // deploy form
            formRemoteService.createFormDef(processDefinition.getId(),
                    processDefinition.getKey(), processDefinition.getName(),
                    GsonUtils.fromJson(formModel.getModelEditorContent(), Map.class),
                    deployContext.getSchemaDiff());

            deployContext.getDeploymentResult().setDefinitionId(processDefinition.getId());
        }
    }
}
