package com.mi.oa.infra.mibpm.infra.repository.deploy.converter;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.infra.mibpm.common.enums.ApiCallProtocolEnum;
import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiTemplateDo;
import com.mi.oa.infra.mibpm.domain.process.model.UsableConfig;
import com.mi.oa.infra.mibpm.domain.process.model.VisibleConfig;
import com.mi.oa.infra.mibpm.flowable.extension.model.FormBindRelation;
import com.mi.oa.infra.mibpm.flowable.extension.model.FormType;
import com.mi.oa.infra.mibpm.infra.models.entity.ModelDo;
import com.mi.oa.infra.mibpm.infra.repository.deploy.entity.DecisionTablePartial;
import com.mi.oa.infra.mibpm.infra.repository.deploy.entity.FormPartial;
import com.mi.oa.infra.mibpm.infra.repository.deploy.entity.FormTypes;
import com.mi.oa.infra.mibpm.infra.repository.deploy.entity.ProcessPartial;
import com.mi.oa.infra.mibpm.infra.repository.deploy.entity.ServicePartial;
import com.mi.oa.infra.mibpm.sdk.dto.ModelEnableStatus;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.xiaomi.oa.infra.mipaas.internal.deployment.models.Model;
import com.xiaomi.oa.infra.mipaas.internal.deployment.models.ModelStatus;
import org.mapstruct.Mapper;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/14 10:50
 */
@Mapper(componentModel = "spring")
public interface DeployModelConverter {

    default List<ModelDo> convertToBpmnModel(List<Model<ProcessPartial>> models) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        List<ModelDo> modelDos = new ArrayList<>(models.size());
        for (Model<ProcessPartial> model : models) {
            ModelDo modelDo = baseConvert(model);
            modelDo.setModelType(ModelType.MODEL_TYPE_BPMN);

            modelDo.setVersion(model.getVersion());
            modelDo.setChecker(model.getContent().getChecker());
            modelDo.setCategoryCode(model.getContent().getCategoryCode());
            modelDo.setFromOld(0);
            modelDo.setUsableConfig(getUsableConfig(model));
            modelDo.setVisibleConfig(getVisibleConfig(model));
            modelDo.setModelEditorContent(model.getContent().getModelEditorContent());
            modelDo.setOwners(model.getContent().getOwners());

            modelDos.add(modelDo);
        }
        return modelDos;
    }

    static VisibleConfig getVisibleConfig(Model<ProcessPartial> model) {
        VisibleConfig visibleConfig = new VisibleConfig();
        visibleConfig.setUsers(model.getContent().getVisibleConfig().getUsers());
        visibleConfig.setDepartments(model.getContent().getVisibleConfig().getDepartments());
        return visibleConfig;
    }

    static UsableConfig getUsableConfig(Model<ProcessPartial> model) {
        UsableConfig usableConfig = new UsableConfig();
        usableConfig.setAppEnabled(model.getContent().getUsableConfig().getAppEnabled());
        usableConfig.setAppUrl(model.getContent().getUsableConfig().getAppUrl());
        usableConfig.setWebUrl(model.getContent().getUsableConfig().getWebUrl());
        usableConfig.setWebEnabled(model.getContent().getUsableConfig().getWebEnabled());
        usableConfig.setBusinessEnabled(model.getContent().getUsableConfig().getBusinessEnabled());

        return usableConfig;
    }

    default List<ModelDo> convertToDmnModel(List<Model<DecisionTablePartial>> models) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        List<ModelDo> modelDos = new ArrayList<>(models.size());
        for (Model<DecisionTablePartial> model : models) {
            ModelDo modelDo = baseConvert(model);
            modelDo.setModelType(ModelType.MODEL_TYPE_DECISION_TABLE);
            modelDo.setModelEditorContent(model.getContent().getModelEditorContent());

            modelDos.add(modelDo);
        }
        return modelDos;
    }

    default List<ModelDo> convertToFormModel(List<Model<FormPartial>> models) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        List<ModelDo> modelDos = new ArrayList<>(models.size());
        for (Model<FormPartial> model : models) {
            ModelDo modelDo = baseConvert(model); ;
            modelDo.setModelType(ModelType.MODEL_TYPE_FORM);
            if (model.getContent().getFormType() == FormTypes.EXT_FORM) {
                modelDo.setModelEditorContent(GsonUtils.toJsonWtihNullField(getFormBindRelation(model)));
            } else {
                modelDo.setModelEditorContent(model.getContent().getModelEditorContent());
            }

            modelDos.add(modelDo);
        }
        return modelDos;
    }

    default FormBindRelation getFormBindRelation(Model<FormPartial> model) {
        FormBindRelation formBindRelation = new FormBindRelation();
        FormBindRelation.FormLink formLink = new FormBindRelation.FormLink();
        formLink.setTestLink(model.getContent().getFormLink().getTestLink());
        formLink.setProdLink(model.getContent().getFormLink().getProdLink());
        formBindRelation.setFormLink(formLink);
        formBindRelation.setNoFormRequired(Boolean.FALSE);
        formBindRelation.setFormModelId(model.getId());
        formBindRelation.setFormType(FormType.EXT_FORM);
        return formBindRelation;
    }

    default List<ModelDo> convertToServiceModel(List<Model<ServicePartial>> models) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        List<ModelDo> modelDos = new ArrayList<>(models.size());
        for (Model<ServicePartial> model : models) {
            ModelDo modelDo = baseConvert(model);
            modelDo.setModelType(ModelType.MODEL_TYPE_SERVICE);
            ApiTemplateDo apiTemplateDo = ApiTemplateDo.builder()
                    .templateCode(model.getId())
                    .appCode(model.getAppId())
                    .apiName(model.getNameTranslation().getZhName())
                    .method(model.getContent().getMethod())
                    .url(model.getContent().getTestConfig().getUrl())
                    .appId(model.getContent().getTestConfig().getAppId())
                    .appKey(model.getContent().getTestConfig().getAppKey())
                    .prodUrl(model.getContent().getProdConfig().getUrl())
                    .appId(model.getContent().getProdConfig().getAppId())
                    .appKey(model.getContent().getProdConfig().getAppKey())
                    .protocol(ApiCallProtocolEnum.valueOf(model.getContent().getProtocol()))
                    .header(model.getContent().getHeader())
                    .payload(model.getContent().getPayload())
                    .isSync(model.getContent().getIsSync())
                    .response(model.getContent().getResponse())
                    .description(model.getComment())
                    .adminUser(model.getContent().getAdminUser())
                    .createTime(ZonedDateTime.now())
                    .build();
            modelDo.setModelEditorContent(GsonUtils.toJsonWtihNullField(apiTemplateDo));
            modelDos.add(modelDo);
        }
        return modelDos;
    }

    default ModelDo baseConvert(Model<?> model) {
        ModelDo modelDo = new ModelDo();
        modelDo.setModelCode(model.getId());
        modelDo.setName(model.getNameTranslation().getZhName());
        modelDo.setEnName(model.getNameTranslation().getZhName());
        modelDo.setDescription(model.getDescription());
        modelDo.setModelEnableStatus(model.getModelStatus() == ModelStatus.ENABLED
                ? ModelEnableStatus.ENABLE : ModelEnableStatus.DISABLE);
        modelDo.setAppCode(model.getAppId());
        modelDo.setComment(model.getComment());
        modelDo.setUpdateTime(model.getUpdateTime());
        modelDo.setUpdateUser(model.getUpdateBy());
        return modelDo;
    }

}
