package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.UserDashboardPermissionPo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @Date 2024/7/17 14:01
 */
@Mapper
public interface UserDashboardPermissionMapper extends BaseMapper<UserDashboardPermissionPo> {

    default UserDashboardPermissionPo selectByUserAndPermission(String userId, String permission) {
        return selectOne(new LambdaQueryWrapper<UserDashboardPermissionPo>().eq(UserDashboardPermissionPo::getUserOrpid, userId)
                .eq(UserDashboardPermissionPo::getPermissionUserId, permission));
    }

}
