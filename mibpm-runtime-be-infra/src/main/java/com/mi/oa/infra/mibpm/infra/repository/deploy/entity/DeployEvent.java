package com.mi.oa.infra.mibpm.infra.repository.deploy.entity;

import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/8/22 17:28
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeployEvent {
    private String appCode;
    private String processKey;
    private String procDefId;
    private String modelType;
    private String dmnJson;

    @Override
    public String toString(){
        return GsonUtils.toJsonWtihNullField(this);
    }
}