package com.mi.oa.infra.mibpm.infra.flowable.sign;

import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/14 15:33
 **/
@Service
public class CompetitionSignBeforeStrategy extends SignBeforeStrategy {
    CompetitionSignBeforeStrategy(RuntimeService runtimeService, TaskService taskService) {
        super(runtimeService, taskService);
    }

    @Override
    UserTaskSignType getUserTaskSignType() {
        return UserTaskSignType.COMPETITION;
    }

}
