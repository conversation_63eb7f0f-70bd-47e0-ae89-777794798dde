package com.mi.oa.infra.mibpm.infra.flowable.behavior;

import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.SubmitTaskAutoCompleteEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.CallActivityWrapper;
import com.mi.oa.infra.mibpm.flowable.extension.model.SubProcessAutoSubmitConfigEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.SubProcessProperty;
import com.mi.oa.infra.mibpm.infra.flowable.variable.ProcessVariableFactory;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.FormRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.impl.NewAccountRemoteServiceImpl;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.flowable.bpmn.model.CallActivity;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.interceptor.StartProcessInstanceAfterContext;
import org.flowable.engine.interceptor.StartProcessInstanceBeforeContext;
import org.flowable.engine.interceptor.StartProcessInstanceInterceptor;
import org.flowable.engine.interceptor.StartSubProcessInstanceAfterContext;
import org.flowable.engine.interceptor.StartSubProcessInstanceBeforeContext;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2023/12/14 16:58
 **/
@Slf4j
public class CustomStartProcessInstanceInterceptor implements StartProcessInstanceInterceptor {
    @Override
    public void beforeStartProcessInstance(StartProcessInstanceBeforeContext instanceContext) {

    }

    @Override
    public void afterStartProcessInstance(StartProcessInstanceAfterContext instanceContext) {

    }

    @Override
    public void beforeStartSubProcessInstance(StartSubProcessInstanceBeforeContext instanceContext) {
        log.info("before Start SubProcessInstance");
        instanceContext.setBusinessKey(UUID.randomUUID().toString());
        ExecutionEntity callActivityExecution = instanceContext.getCallActivityExecution();
        CallActivity callActivity = (CallActivity) callActivityExecution.getCurrentFlowElement();
        Map<String, Object> variables = instanceContext.getVariables();
        NewAccountRemoteServiceImpl accountRemoteService = SpringContextUtil.getBean(AccountRemoteService.class);
        String userName;
        if (callActivity.hasMultiInstanceLoopCharacteristics()) {
            userName = (String) callActivityExecution.getVariable("var");
        } else {
            userName = (String) callActivityExecution.getVariable(BpmVariablesConstants.ENGINE_SUB_PROCESS_INITIATORS);
        }
        BpmUser user = accountRemoteService.getUser(userName);
        variables.put(BpmVariablesConstants.VARIABLE_INITIATOR, null == user ? "" : user.getUserName());
        ProcessVariableFactory processVariableFactory = SpringContextUtil.getBean(ProcessVariableFactory.class);
        variables.put(BpmVariablesConstants.VARIABLE_PROC_DEF_NAME, instanceContext.getProcessDefinition().getName());
        variables.put(BpmVariablesConstants.VARIABLE_INITIATE_TIME, DateUtils.getFormatDate("yyyyMMddHHmmss"));
        instanceContext.setVariables(variables);
        String title = processVariableFactory.buildProcessInstanceName(instanceContext.getProcessDefinition().getId(), variables);
        instanceContext.setProcessInstanceName(title);
    }

    @Override
    public void afterStartSubProcessInstance(StartSubProcessInstanceAfterContext instanceContext) {
        log.info("afterStartSubProcessInstance");
        ExecutionEntity processInstance = instanceContext.getProcessInstance();
        Map<String, Object> variables = instanceContext.getVariables();
        processInstance.setStartUserId((String) variables.get(BpmVariablesConstants.VARIABLE_INITIATOR));
        ExecutionEntity callActivityExecution = instanceContext.getCallActivityExecution();
        ExecutionEntity childExecution = instanceContext.getChildExecution();
        FlowElement currentFlowElement = callActivityExecution.getCurrentFlowElement();
        if (currentFlowElement instanceof CallActivity) {
            BpmnExtensionHelper bpmnExtensionHelper = SpringContextUtil.getBean(BpmnExtensionHelper.class);
            CallActivityWrapper callActivityWrapper = bpmnExtensionHelper.getCallActivityWrapper((CallActivity) currentFlowElement);
            SubProcessProperty subProps = callActivityWrapper.getSubProps();
            if (null == subProps) {
                return;
            }
            Integer loopCounter = (Integer) callActivityExecution.getVariable(BpmVariablesConstants.VARIABLE_LOOP_COUNTER);
            CallActivityFormDataHelper callActivityFormDataHelper = SpringContextUtil.getBean(CallActivityFormDataHelper.class);
            Map<String, Object> childFormData = callActivityFormDataHelper.fillChildToChild(variables, subProps, loopCounter);
            if (MapUtils.isNotEmpty(childFormData)) {
                childExecution.setVariables(childFormData);
            }
            if (null != subProps && SubProcessAutoSubmitConfigEnum.AUTO.equals(subProps.getSubInitiationMethod())) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCompletion(int status) {
                        sendSubmitTaskAutoCompleteEvent(processInstance, childFormData);
                    }
                });
            } else {
                FormRemoteService formRemoteService = SpringContextUtil.getBean(FormRemoteService.class);
                formRemoteService.saveFormData(processInstance.getProcessDefinitionId(),
                        processInstance.getProcessInstanceId(), processInstance.getProcessInstanceId(),
                        "", childFormData);
            }
        }

    }

    private void sendSubmitTaskAutoCompleteEvent(ExecutionEntity processInstance, Map<String, Object> formData) {
        // 设置事件基础信息
        EventPublisher eventPublisher = SpringContextUtil.getBean(EventPublisher.class);
        SubmitTaskAutoCompleteEvent submitTaskAutoCompleteEvent = SubmitTaskAutoCompleteEvent.builder()
                .processDefinitionId(processInstance.getProcessDefinitionId())
                .processInstanceId(processInstance.getProcessInstanceId())
                .businessKey(processInstance.getBusinessKey())
                .formData(formData)
                .build();
        submitTaskAutoCompleteEvent.setId(processInstance.getProcessInstanceId());
        submitTaskAutoCompleteEvent.setIdentifier(EventIdentify.SUBMIT_TASK_AUTO_COMPLETE.name());
        submitTaskAutoCompleteEvent.setTimestamp(System.currentTimeMillis());
        // 发送自动跳过发起任务事件
        eventPublisher.publish(submitTaskAutoCompleteEvent);
    }

}
