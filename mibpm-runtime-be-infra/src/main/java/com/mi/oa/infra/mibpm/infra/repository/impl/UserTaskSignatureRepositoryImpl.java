package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.mi.oa.infra.mibpm.domain.task.model.UserTaskSignature;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.UserTaskSignatureConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.UserTaskSignatureMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.UserTaskSignaturePo;
import com.mi.oa.infra.mibpm.infra.task.repository.UserTaskSignatureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/6 14:54
 **/
@Repository
public class UserTaskSignatureRepositoryImpl implements UserTaskSignatureRepository {

    @Autowired
    private UserTaskSignatureMapper userTaskSignatureMapper;
    @Autowired
    private UserTaskSignatureConverter userTaskSignatureConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(UserTaskSignature userTaskSignature) {
        if (userTaskSignature.isSaveAsDefault()) {
            UserTaskSignaturePo setValue = new UserTaskSignaturePo();
            setValue.setSaveAsDefault(false);
            userTaskSignatureMapper.update(setValue, new UpdateWrapper<UserTaskSignaturePo>().lambda()
                    .eq(UserTaskSignaturePo::getCreateUser, userTaskSignature.getUserId())
                    .eq(UserTaskSignaturePo::isSaveAsDefault, true));
        }
        UserTaskSignaturePo po = userTaskSignatureConverter.map(userTaskSignature);
        userTaskSignatureMapper.insert(po);

    }

    @Override
    public UserTaskSignature getByTaskIdAndUserId(String taskId, String userId) {
        List<UserTaskSignaturePo> po = userTaskSignatureMapper.selectList(
                new QueryWrapper<UserTaskSignaturePo>().lambda().eq(UserTaskSignaturePo::getTaskId, taskId)
                        .eq(UserTaskSignaturePo::getCreateUser, userId).orderByDesc(UserTaskSignaturePo::getId));
        if (po.isEmpty()) {
            return null;
        }
        return userTaskSignatureConverter.map(po.get(0));
    }

    @Override
    public UserTaskSignature findUserDefault(String userId) {
        List<UserTaskSignaturePo> po = userTaskSignatureMapper.selectList(
                new QueryWrapper<UserTaskSignaturePo>().lambda().eq(UserTaskSignaturePo::getCreateUser, userId)
                        .eq(UserTaskSignaturePo::isSaveAsDefault, true).orderByDesc(UserTaskSignaturePo::getId));
        if (po.isEmpty()) {
            return null;
        }
        return userTaskSignatureConverter.map(po.get(0));
    }
}
