package com.mi.oa.infra.mibpm.infra.repository.impl;

import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.task.service.HistoricTaskService;
import org.flowable.task.service.TaskServiceConfiguration;
import org.flowable.task.service.impl.util.CommandContextUtil;

/**
 * <AUTHOR>
 */
public class HistoricTaskServiceGetCmd implements Command<HistoricTaskService> {
    @Override
    public HistoricTaskService execute(CommandContext commandContext) {
        TaskServiceConfiguration taskServiceConfiguration = CommandContextUtil.getTaskServiceConfiguration();
        HistoricTaskService historicTaskService = taskServiceConfiguration.getHistoricTaskService();
        return historicTaskService;
    }
}
