package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.domain.userconfig.model.DeptPermissionDo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DeptPermissionPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/22 17:44
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface DeptPermissionPoConverter {
   DeptPermissionPo doToPo(DeptPermissionDo deptPermissionDo);

   List<DeptPermissionDo> poToDo(List<DeptPermissionPo> deptPermissionPo);
}
