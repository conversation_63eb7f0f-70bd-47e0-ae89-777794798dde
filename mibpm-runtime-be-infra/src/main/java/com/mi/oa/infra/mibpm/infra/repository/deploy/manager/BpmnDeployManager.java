package com.mi.oa.infra.mibpm.infra.repository.deploy.manager;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.infra.mibpm.common.constant.ModelRelationTypeConstants;
import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.common.exception.InfraException;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.BpmnExtensionElements;
import com.mi.oa.infra.mibpm.flowable.extension.model.FormBindRelation;
import com.mi.oa.infra.mibpm.flowable.extension.model.ProcessWrapper;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.errorcode.ModelsErrorCodeEnum;
import com.mi.oa.infra.mibpm.infra.models.entity.DeployContext;
import com.mi.oa.infra.mibpm.infra.models.entity.DeploymentResult;
import com.mi.oa.infra.mibpm.infra.models.entity.ModelDo;
import com.mi.oa.infra.mibpm.infra.models.repository.ModelsRepository;
import com.mi.oa.infra.mibpm.infra.process.repository.ProcessDefinitionConfigRepository;
import com.mi.oa.infra.mibpm.infra.repository.deploy.converter.BpmnConverter;
import com.mi.oa.infra.mibpm.infra.repository.deploy.converter.ModelConverter;
import com.mi.oa.infra.mibpm.utils.ExternalThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.MultiInstanceLoopCharacteristics;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.impl.persistence.entity.DeploymentEntityImpl;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.ui.modeler.domain.Model;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/22 15:55
 */
@Slf4j
@Service
public class BpmnDeployManager extends AbstractDeployManager {
    @Autowired
    private ModelConverter modelConverter;
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private BpmnExtensionHelper bpmnExtensionHelper;
    @Autowired
    private ProcessDefinitionConfigRepository processDefinitionConfigRepository;

    private static final BpmnXMLConverter BPMN_XML_CONVERTER = new BpmnXMLConverter();

    @Override
    public ModelType support() {
        return ModelType.MODEL_TYPE_BPMN;
    }

    @Override
    public void verify(DeployContext deployContext) {
    }

    @Override
    public void postProcessBeforeDeploy(DeployContext deployContext) {
        Model model = modelConverter.map(deployContext.getModel());
        BpmnModel bpmnModel = BpmnConverter.convertToBpmnModel(model.getModelEditorJson());
        deployContext.setBpmnModel(bpmnModel);
        if (bpmnModel == null) {
            throw new IllegalArgumentException("xml to bpmn model error");
        }

        Map<String, List<Map<String, Object>>> schemaDiff = new HashMap<>(32);

        resetFormConfigIfExtForm(deployContext, bpmnModel);
        for (FlowElement flowElement : bpmnModel.getMainProcess().getFlowElements()) {
            if (flowElement instanceof UserTask) {
                UserTaskWrapper userTaskExtension = bpmnExtensionHelper.getUserTaskWrapper((UserTask) flowElement);
                // 解析节点表单配置
                extractFormSchemaDiff(schemaDiff, userTaskExtension);
                // 预处理加签配置节点（改为多实例）
                fillTaskLoop(userTaskExtension.getUserTask(), userTaskExtension);
            }
        }
        deployContext.setSchemaDiff(schemaDiff);
    }

    @Override
    public void deploy(DeployContext deployContext) {
        Deployment deployment;
        try {
            String finalXml = new String(BPMN_XML_CONVERTER.convertToXML(deployContext.getBpmnModel()));

            deployment = repositoryService.createDeployment()
                    .addString(String.join(".", deployContext.getModel().getModelCode(),
                            ModelType.MODEL_TYPE_BPMN.getPrefix()), finalXml)
                    .name(deployContext.getModel().getName())
                    .deploy();
        } catch (Exception e) {
            throw new InfraException(ModelsErrorCodeEnum.DEPLOY_BPMN_MODEL_ERROR,
                    deployContext.getModel().getModelCode());
        }

        DeploymentEntityImpl deploymentEntity = (DeploymentEntityImpl) deployment;
        List<ProcessDefinition> processDefinitions = deploymentEntity.getDeployedArtifacts(ProcessDefinition.class);

        if (CollectionUtils.isNotEmpty(processDefinitions)) {
            deployContext.getModel().setDeploymentId(deployment.getId());
            deployContext.setProcessDefinition(processDefinitions.get(0));
            deployContext.getDeploymentResult().setDefinitionId(processDefinitions.get(0).getId());
        }

        ModelsRepository modelRepository = deployContext.getModelRepository();
        modelRepository.saveModelResource(deployContext);
    }

    @Override
    public void postProcessAfterDeploy(DeployContext deployContext) {
        BpmnModel bpmnModel = deployContext.getBpmnModel();
        // 解析需要部署的依赖表单模型
        String formModelId = parseFormModel(bpmnExtensionHelper.getProcessWrapper(bpmnModel.getMainProcess()));
        if (formModelId == null) {
            log.info("This process does not require a form model, bpmn model Id = [{}]", deployContext.getModel().getModelCode());
            return;
        }

        // deploy relation form model
        ModelDo formModel;
        if ((formModel = findRelationFormModel(deployContext, formModelId)) == null) {
            return;
        }

        ModelsRepository modelRepository = deployContext.getModelRepository();

        DeploymentResult formDeploymentResult = modelRepository.deploy(DeployContext.builder()
                .model(formModel)
                .processDefinition(deployContext.getProcessDefinition())
                .schemaDiff(deployContext.getSchemaDiff())
                .modelRepository(deployContext.getModelRepository())
                .build());

        modelRepository.saveRelations(deployContext.getDeploymentResult().getHistoryId(),
                formDeploymentResult.getHistoryId(), ModelRelationTypeConstants.TYPE_FORM_MODEL_CHILD);

        // 刷新流程节点配置表
        ExternalThreadPool.getInstance().execute(() -> {
            processDefinitionConfigRepository.save(deployContext.getProcessDefinition().getId());
        });
    }

    /**
     * 查找表单模型
     * 当依赖模型列表中存在时(latest) 则去除，否则从仓库中查询
     *
     * @param deployContext
     * @param formModelId
     * @return
     */
    @Nullable
    private ModelDo findRelationFormModel(DeployContext deployContext, String formModelId) {
        ModelDo formModel = null;
        if (CollectionUtils.isNotEmpty(deployContext.getRelationModels())) {
            formModel = deployContext.getRelationModels().stream()
                    .filter(m -> ModelType.MODEL_TYPE_FORM == m.getModelType()
                            && formModelId.equals(m.getModelCode()))
                    .findFirst()
                    .orElse(null);
        }
        if (formModel == null) {
            // Only the process model has been modified, and the form model is found from the repository
            if ((formModel = deployContext.getModelRepository().queryModelById(formModelId)) == null) {
                return null;
            }
        }
        return formModel;
    }

    /**
     * 解析表单模型
     *
     * @param processWrapper
     * @return formModelId
     */
    private String parseFormModel(ProcessWrapper processWrapper) {
        FormBindRelation formBindRelation = processWrapper.getFormBindRelation();
        if (formBindRelation == null) {
            throw new RuntimeException("form bind relation is null");
        }
        // not required form
        if (!formBindRelation.isNoFormRequired()) {
            return formBindRelation.getFormModelId();
        }
        return null;
    }

    private void resetFormConfigIfExtForm(DeployContext deployContext, BpmnModel bpmnModel) {
        ProcessWrapper processWrapper = bpmnExtensionHelper.getProcessWrapper(bpmnModel.getMainProcess());

        FormBindRelation formBindRelation = processWrapper.getFormBindRelation();
        if (formBindRelation == null) {
            throw new RuntimeException("form bind relation is null");
        }
        if (!formBindRelation.isNoFormRequired() && formBindRelation.getFormType().isExtFormType()) {
            // 更新第三方表单地址
            ModelDo relationFormModel = findRelationFormModel(deployContext, formBindRelation.getFormModelId());
            if (relationFormModel != null) {
                bpmnModel.getMainProcess().getExtensionElements()
                        .get(BpmnExtensionElements.PROCESS_FORM_BIND_RELATION.getKey()).get(0)
                        .setElementText(relationFormModel.getModelEditorContent());
            }
        }
    }

    /**
     * 提取用户任务节点中的表单配置
     *
     * @param schemaDiff
     * @param userTaskExtension
     */
    private void extractFormSchemaDiff(Map<String, List<Map<String, Object>>> schemaDiff, UserTaskWrapper userTaskExtension) {
        if (Objects.nonNull(userTaskExtension.getFormPermission())) {
            List<Map<String, Object>> data = userTaskExtension.getFormPermission().getData();
            String type = userTaskExtension.getFormPermission().getType();
            // 简单模式只保留operationPermission属性，需要将类型传给表单服务
            if (null != data) {
                for (Map<String, Object> fieldMap : data) {
                    fieldMap.put("formPermissionType", type);
                }
            }
            schemaDiff.put(userTaskExtension.getUserTask().getId(), data);
        }
    }

    /**
     * 处理加签节点（将单实例节点改为多实例节点）以保证运行时加签行为一致
     *
     * @param userTask
     * @param userTaskExtension
     */
    private void fillTaskLoop(UserTask userTask, UserTaskWrapper userTaskExtension) {
        UserTaskSignType signType = userTaskExtension.getSignType();
        switch (signType) {
            case SEQUENTIAL:
            case PARALLEL_ONE:
            case PARALLEL_ALL:
                userTask.setAssignee("${assignee}");
                break;
            case SINGLE:
            default:
                break;
        }
        // 配置了加签的节点改为多实例节点
        if (null == userTask.getLoopCharacteristics()
                && CollectionUtils.isNotEmpty(userTaskExtension.getUserTaskOperations())
                && userTaskExtension.getUserTaskOperations().contains(UserTaskOperation.SIGNATURE)) {
            MultiInstanceLoopCharacteristics loopCharacteristics = new MultiInstanceLoopCharacteristics();
            userTask.setLoopCharacteristics(loopCharacteristics);
            if (userTask.hasMultiInstanceLoopCharacteristics()) {
                userTask.setAssignee("${assignee}");
                loopCharacteristics.setSequential(false);
                loopCharacteristics.setInputDataItem("${sys_multiinstance_assignees}");
                loopCharacteristics.setElementVariable("assignee");
                loopCharacteristics.setCompletionCondition("${nrOfCompletedInstances == nrOfInstances}");
            }
        }
    }
}
