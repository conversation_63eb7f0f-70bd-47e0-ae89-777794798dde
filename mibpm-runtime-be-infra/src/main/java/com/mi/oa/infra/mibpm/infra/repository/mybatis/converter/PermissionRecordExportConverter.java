package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionRecordsDo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.PermissionRecordsPo;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2024/7/18 10:43
 */
@Mapper(componentModel = "spring")
public interface PermissionRecordExportConverter {

    PermissionRecordsDo poToDo(PermissionRecordsPo permissionRecordsPo);

    default BpmUser map(String s) {
        return BpmUser.builder().userName(s).build();
    }
}
