package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.domain.userconfig.model.UserPermissionDo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.UserDashboardPermissionPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.UserPermissionPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/22 17:43
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface UserPermissionPoConverter {
    UserPermissionPo doToPo(UserPermissionDo userPermissionDo);

    UserDashboardPermissionPo poToDashboard(UserPermissionPo userPermissionPo);
    UserDashboardPermissionPo doToDashboard(UserPermissionDo userPermissionPo);

    List<UserPermissionDo> poToDo(List<UserPermissionPo> userPermissionPo);
}
