package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.infra.mibpm.application.message.dto.req.DeletePermissionReq;
import com.mi.oa.infra.mibpm.common.enums.DeptFilter;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Department;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionDo;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionRecordsDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DeptPermissionDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserPermissionDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.PermissionOperationRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.DeptRemote;
import com.mi.oa.infra.mibpm.infra.repository.enums.PermOperationType;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.DeptPermissionPoConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.PermissionExportConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.PermissionRecordExportConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.UserPermissionPoConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.DeptPermissionMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.PermissionRecordsMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.RequirePermissionMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.UserDashboardPermissionMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.UserPermissionMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DeptPermissionPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.PermissionRecordsPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.RequirePermissionPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.UserDashboardPermissionPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.UserPermissionPo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/7/16 17:29
 */
@Repository
@Slf4j
public class PermissionOperationRepositorylmpl implements PermissionOperationRepository {
    @Autowired
    DeptRemote deptRemote;
    @Autowired
    AccountRemoteService accountRemoteService;
    @Autowired
    RequirePermissionMapper requirePermissionMapper;
    @Autowired
    PermissionRecordsMapper permissionRecordsMapper;
    @Autowired
    UserPermissionMapper userPermissionMapper;
    @Autowired
    DeptPermissionMapper deptPermissionMapper;
    @Autowired
    PermissionExportConverter permissionExportConverter;
    @Autowired
    PermissionRecordExportConverter permissionRecordExportConverter;
    @Autowired
    UserPermissionPoConverter userPermissionPoConverter;
    @Autowired
    DeptPermissionPoConverter deptPermissionPoConverter;
    @Autowired
    private UserDashboardPermissionMapper userDashboardPermissionMapper;

    @Override
    public Boolean isDeptPermission(String userOrpid, String deptId) {
        DeptPermissionPo deptPermissionPo = deptPermissionMapper.selectInitDeptId(userOrpid, deptId);
        return deptPermissionPo != null;
    }

    @Override
    public Boolean isAssigneePermission(String userOrpid, String permissionUserId) {
        UserPermissionPo userPermissionPo = userPermissionMapper.selectAssigneeId(userOrpid, permissionUserId);
        return userPermissionPo != null;
    }

    @Override
    public void addUserInitDeptId(String userOrpid, String deptId, DeptFilter deptFilter) {
        DeptPermissionPo deptPermissionPo = new DeptPermissionPo();
        deptPermissionPo.setUserOrpid(userOrpid);
        deptPermissionPo.setDeptId(deptId);
        deptPermissionPo.setFilter(deptFilter.getDescription());
        deptPermissionMapper.insert(deptPermissionPo);
    }

    @Override
    public PageModel<PermissionDo> page(String userOrpid, long pageNum, long pageSize, String deptId) {
        LambdaQueryWrapper<RequirePermissionPo> wrapper = new LambdaQueryWrapper<RequirePermissionPo>()
                .eq(StringUtils.isNotBlank(userOrpid), RequirePermissionPo::getUserOrpid, userOrpid);
        if (StringUtils.isNotBlank(deptId)) {
            wrapper.and(w -> w.eq(RequirePermissionPo::getPermissionId, deptId)
                    .eq(RequirePermissionPo::getPermissionIdType, PermissionIdType.DEPT_TYPE));
        }
        Page<RequirePermissionPo> page = requirePermissionMapper.selectPage(
                new Page<>(pageNum, pageSize), wrapper);
        Set<String> userIds = new HashSet<>();
        for (RequirePermissionPo record : page.getRecords()) {
            if (record.getPermissionIdType() == PermissionIdType.USER_TYPE) {
                userIds.add(record.getPermissionId());
            }
        }
        Map<String, BpmUser> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<BpmUser> users = accountRemoteService.listUsers(userIds);
            for (BpmUser user : users) {
                userMap.put(user.getUserName(), user);
            }
        }
        List<PermissionDo> list = new ArrayList<>();
        for (RequirePermissionPo record : page.getRecords()) {
            PermissionDo permissionDo = permissionExportConverter.poToDo(record);
            if (record.getPermissionIdType() == PermissionIdType.DEPT_TYPE) {
                Department department = deptRemote.getDepartment(record.getPermissionId());
                String fullDeptName = deptPathName(department);
                permissionDo.setPermissionName(fullDeptName);
            } else if (record.getPermissionIdType() == PermissionIdType.USER_TYPE) {
                BpmUser user = userMap.get(record.getPermissionId());
                if (user != null) {
                    permissionDo.setPermissionName(user.getDisplayName());
                }
            }
            list.add(permissionDo);
        }
        return PageModel.build(list, page.getSize(), page.getCurrent(), page.getTotal());
    }

    @Override
    public PageModel<PermissionRecordsDo> exportRecords(String userOrpid, long pageNum, long pageSize, String deptId) {
        LambdaQueryWrapper<PermissionRecordsPo> wrapper = new LambdaQueryWrapper<PermissionRecordsPo>()
                .eq(StringUtils.isNotBlank(userOrpid), PermissionRecordsPo::getUserOrpid, userOrpid);
        if (StringUtils.isNotBlank(deptId)) {
            wrapper.and(w -> w.eq(PermissionRecordsPo::getPermissionId, deptId)
                    .eq(PermissionRecordsPo::getPermissionIdType, PermissionIdType.DEPT_TYPE));
        }
        Page<PermissionRecordsPo> exportRecords = permissionRecordsMapper.selectPage(
                new Page<>(pageNum, pageSize), wrapper);
        Set<String> userIds = new HashSet<>();
        for (PermissionRecordsPo record : exportRecords.getRecords()) {
            if (record.getPermissionIdType() == PermissionIdType.USER_TYPE) {
                userIds.add(record.getPermissionId());
            }
        }
        Map<String, BpmUser> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<BpmUser> users = accountRemoteService.listUsers(userIds);
            for (BpmUser user : users) {
                userMap.put(user.getUserName(), user);
            }
        }
        List<PermissionRecordsDo> list = new ArrayList<>();
        for (PermissionRecordsPo record : exportRecords.getRecords()) {
            PermissionRecordsDo permissionRecordsDo = permissionRecordExportConverter.poToDo(record);
            BpmUser createUser = accountRemoteService.getUser(record.getCreateUser());
            permissionRecordsDo.setCreateUser(createUser);
            if (record.getPermissionIdType() == PermissionIdType.DEPT_TYPE) {
                Department department = deptRemote.getDepartment(record.getPermissionId());
                permissionRecordsDo.setPermissionName(department.getDeptName());
            } else if (record.getPermissionIdType() == PermissionIdType.USER_TYPE) {
                BpmUser user = userMap.get(record.getPermissionId());
                if (user != null) {
                    permissionRecordsDo.setPermissionName(user.getDisplayName());
                }
            }
            list.add(permissionRecordsDo);
        }
        return PageModel.build(list, exportRecords.getSize(), exportRecords.getCurrent(), exportRecords.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPermissionAssignee(UserPermissionDo userPermissionDo) {
        RequirePermissionPo requirePermissionPo = new RequirePermissionPo();
        requirePermissionPo.setUserOrpid(userPermissionDo.getUserOrpid());
        requirePermissionPo.setCreateUser(userPermissionDo.getCreateUser());
        requirePermissionPo.setPermissionId(userPermissionDo.getPermissionUserId());
        requirePermissionPo.setPermissionIdType(PermissionIdType.USER_TYPE);
        requirePermissionMapper.insert(requirePermissionPo);
        UserPermissionPo userPermission = userPermissionMapper.selectAssigneeId(userPermissionDo.getUserOrpid(),
                userPermissionDo.getPermissionUserId());
        if (null == userPermission) {
            UserPermissionPo userPermissionPo = userPermissionPoConverter.doToPo(userPermissionDo);
            userPermissionMapper.insert(userPermissionPo);
        }
        UserDashboardPermissionPo userDashboard = userDashboardPermissionMapper.selectByUserAndPermission(userPermissionDo.getUserOrpid(),
                userPermissionDo.getPermissionUserId());
        if (null == userDashboard) {
            UserDashboardPermissionPo dashboardPo = userPermissionPoConverter.doToDashboard(userPermissionDo);
            userDashboardPermissionMapper.insert(dashboardPo);
        }
    }

    @Override
    public void addPermissionDept(DeptPermissionDo deptPermissionDo) {
        RequirePermissionPo requirePermissionPo = new RequirePermissionPo();
        requirePermissionPo.setUserOrpid(deptPermissionDo.getUserOrpid());
        requirePermissionPo.setPermissionId(deptPermissionDo.getCreateUser());
        requirePermissionPo.setPermissionId(deptPermissionDo.getDeptId());
        requirePermissionPo.setPermissionIdType(PermissionIdType.DEPT_TYPE);
        requirePermissionMapper.insert(requirePermissionPo);
        DeptPermissionPo deptPermissionPo = deptPermissionPoConverter.doToPo(deptPermissionDo);
        deptPermissionMapper.insert(deptPermissionPo);
    }

    @Override
    public void deletePermission(DeletePermissionReq req) {
        if (req.getPermissionIdType() == PermissionIdType.USER_TYPE) {
            userPermissionMapper.deleteUserPermission(req.getUserOrpid(), req.getPermissionId());
        } else if (req.getPermissionIdType() == PermissionIdType.DEPT_TYPE) {
            deptPermissionMapper.deleteDeptPermission(req.getUserOrpid(), req.getPermissionId());
        }
        requirePermissionMapper.deletePermission(req.getUserOrpid(), req.getPermissionId());
    }

    @Override
    public void addApplyRecord(String userOrpid, String permissionId, String businessKey, String taskId, PermissionIdType permissionIdType, String createUser) {
        PermissionRecordsPo permissionRecordsPo = new PermissionRecordsPo();
        permissionRecordsPo.setUserOrpid(userOrpid);
        permissionRecordsPo.setPermissionId(permissionId);
        permissionRecordsPo.setBusinessKey(businessKey);
        permissionRecordsPo.setPermissionIdType(permissionIdType);
        permissionRecordsPo.setCreateUser(createUser);
        permissionRecordsPo.setTaskId(taskId);
        permissionRecordsPo.setOperationType(PermOperationType.CREATE.getDescription());
        permissionRecordsMapper.insert(permissionRecordsPo);
    }

    @Override
    public void addRemoveRecord(DeletePermissionReq req) {
        PermissionRecordsPo permissionRecordsPo = new PermissionRecordsPo();
        permissionRecordsPo.setUserOrpid(req.getUserOrpid());
        permissionRecordsPo.setPermissionId(req.getPermissionId());
        permissionRecordsPo.setPermissionIdType(req.getPermissionIdType());
        permissionRecordsPo.setOperationType(PermOperationType.DELETE.getDescription());
        permissionRecordsMapper.insert(permissionRecordsPo);
    }

    @Override
    public PermissionDo queryPermission(String userOrpid, String permissionId) {
        RequirePermissionPo requirePermissionPo = requirePermissionMapper.selectPermission(userOrpid, permissionId);
        return permissionExportConverter.poToDo(requirePermissionPo);
    }

    private String deptPathName(Department department) {
        List<Department> deptPath = department.getDeptPath();
        StringBuilder fullDeptPathName = new StringBuilder();
        for (int i = 1; i < deptPath.size(); i++) {
            Department dept = deptPath.get(i);
            fullDeptPathName.append(dept.getDeptName()).append("/");
        }
        if (fullDeptPathName.length() > 0) {
            fullDeptPathName.setLength(fullDeptPathName.length() - 1);
        }
        return fullDeptPathName.toString();
    }

    @Override
    public List<DeptPermissionDo> queryDeptPermission(String userOrpid) {
        return deptPermissionPoConverter.poToDo(deptPermissionMapper.selectDeptPermission(userOrpid));
    }

    @Override
    public List<UserPermissionDo> queryAssigneePermission(String userOrpid) {
        return userPermissionPoConverter.poToDo(userPermissionMapper.selectUserPermission(userOrpid));
    }

    @Override
    public void removeDeptId(String userOrpid, String deptId) {
        if (deptId == null || deptId.isEmpty()) {
            deptPermissionMapper.deleteDeptAllPermission(userOrpid);
        } else {
            deptPermissionMapper.deleteDeptPermission(userOrpid, deptId);
        }
    }

    @Override
    public void removeAssigneeId(String userOrpid, String permissionUserId) {
        if (StringUtils.isBlank(permissionUserId)) {
            userPermissionMapper.deleteUserAllPermission(userOrpid);
        } else {
            userPermissionMapper.deleteUserPermission(userOrpid, permissionUserId);
        }
    }

    @Override
    public void removeDashboard(String userOrpid, String permissionUserId) {
        if (StringUtils.isBlank(permissionUserId)) {
            userDashboardPermissionMapper.delete(new LambdaQueryWrapper<UserDashboardPermissionPo>()
                    .eq(UserDashboardPermissionPo::getUserOrpid, userOrpid));
        } else {
            userDashboardPermissionMapper.delete(new LambdaQueryWrapper<UserDashboardPermissionPo>()
                    .eq(UserDashboardPermissionPo::getUserOrpid, userOrpid)
                    .eq(UserDashboardPermissionPo::getPermissionUserId, permissionUserId));
        }

    }

    @Override
    public void addDeptId(String userOrpid, String deptId) {
        DeptPermissionPo deptPermissionPo = new DeptPermissionPo();
        deptPermissionPo.setUserOrpid(userOrpid);
        deptPermissionPo.setDeptId(deptId);
        deptPermissionMapper.insert(deptPermissionPo);
    }

    @Override
    public void addAssigneeId(String userOrpid, String permissionUserId) {
        UserPermissionPo userPermissionPo = new UserPermissionPo();
        userPermissionPo.setUserOrpid(userOrpid);
        userPermissionPo.setPermissionUserId(permissionUserId);
        userPermissionMapper.insert(userPermissionPo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertIfNotExist(String userOrpid, String permissionUserId) {
        UserDashboardPermissionPo userDashboardPermissionPo = userDashboardPermissionMapper.selectByUserAndPermission(userOrpid, permissionUserId);
        if (null == userDashboardPermissionPo) {
            userDashboardPermissionPo = new UserDashboardPermissionPo();
            userDashboardPermissionPo.setUserOrpid(userOrpid);
            userDashboardPermissionPo.setPermissionUserId(permissionUserId);
            userDashboardPermissionPo.setCreateUser(userOrpid);
            userDashboardPermissionPo.setUpdateUser(userOrpid);
            userDashboardPermissionPo.setCreateTime(ZonedDateTime.now());
            userDashboardPermissionPo.setUpdateTime(ZonedDateTime.now());
            userDashboardPermissionMapper.insert(userDashboardPermissionPo);
        }
    }
}
