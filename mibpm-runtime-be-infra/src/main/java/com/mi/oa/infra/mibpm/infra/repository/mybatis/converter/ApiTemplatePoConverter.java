package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.common.enums.ApiCallProtocolEnum;
import com.mi.oa.infra.mibpm.common.enums.TemplateTypeEnum;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiTemplateDo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ApiTemplatePo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ProcessTemplateSettingPo;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.mapstruct.Mapper;
import org.springframework.http.HttpMethod;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Objects;


/**
 * @author: qiuzhipeng
 * @Date: 2022/1/17 16:18
 */

@Mapper(componentModel = "spring")
public interface ApiTemplatePoConverter {


    ApiTemplateDo poToDo(ApiTemplatePo apiTemplatePo);

    ApiTemplatePo doToPo(ApiTemplateDo apiTemplateDo);

    /**
     * 将流程配置中的回调信息转换为回调模版信息
     *
     * @param processTemplateSettingPo
     * @return
     */

    @SneakyThrows
    default ApiTemplateDo processTemplateSettingToApiTemplate(ProcessTemplateSettingPo processTemplateSettingPo){
        if(Objects.isNull(processTemplateSettingPo)){
            return null;
        }
        // 获取默认请求模版
        String defaultTemplate = FileUtils.readFileToString(new File("generator/RemoteCallTemplate.txt"), StandardCharsets.UTF_8);
        ApiTemplateDo apiTemplateDo = ApiTemplateDo.builder()
                .templateType(TemplateTypeEnum.DEFAULT)
                .isSync(!processTemplateSettingPo.getContinueOnError())
                .payload(defaultTemplate)
                .appId(processTemplateSettingPo.getCallBackAppId())
                .appKey(processTemplateSettingPo.getCallBackAppKey())
                .protocol(ApiCallProtocolEnum.X5)
                .url(processTemplateSettingPo.getOperateCallbackUrl())
                .method(HttpMethod.POST.name())
                .build();
        return apiTemplateDo;
    }
}
