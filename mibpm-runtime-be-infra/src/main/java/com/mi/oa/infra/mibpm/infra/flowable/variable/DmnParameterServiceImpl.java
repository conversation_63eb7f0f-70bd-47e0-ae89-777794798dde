package com.mi.oa.infra.mibpm.infra.flowable.variable;

import com.mi.oa.infra.mibpm.flowable.extension.model.ParameterSource;
import com.mi.oa.infra.mibpm.flowable.extension.model.ProcessParameter;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.dmn.api.DmnRuleService;
import org.flowable.dmn.api.ExecuteDecisionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 处理来源为决策表的控制参数
 *
 * <AUTHOR>
 * @date 2022/3/8 16:29
 */
@Slf4j
@Service
public class DmnParameterServiceImpl implements ParameterService {

    @Autowired
    private DmnRuleService dmnRuleService;

    @Override
    public ParameterSource support() {
        return ParameterSource.DMN;
    }

    @Override
    public void compute(ProcessParameter processParameter, Map<String, Object> processVariables) {
        ProcessParameter.DmnSource dmnSource = processParameter.getDmnSource();
        if (Objects.isNull(dmnSource) || StringUtils.isBlank(dmnSource.getModelCode())) {
            return;
        }
        // 获取决策表
        ExecuteDecisionBuilder decisionBuilder = dmnRuleService.createExecuteDecisionBuilder().decisionKey(dmnSource.getModelCode());
        // 设置决策表输入参数
        for (String inputKey : dmnSource.getInput().keySet()) {
            decisionBuilder.variable(inputKey, processVariables.get(dmnSource.getInput().get(inputKey)));
        }
        // 执行决策表
        log.info("流程变量决策表计算入参: {},流程变量: {}", GsonUtils.toJsonFilterNullField(dmnSource), GsonUtils.toJsonFilterNullField(processVariables));
        List<Map<String, Object>> rules = decisionBuilder.execute();
        log.info("流程变量决策表计算结果: {}", GsonUtils.toJsonFilterNullField(rules));
        // 收集执行结果
        List<Object> result = new LinkedList<>();
        for (Map<String, Object> rule : rules) {
            result.add(rule.get(dmnSource.getOutput()));
        }
        processVariables.put(processParameter.getIdentification(), result);
    }
}
