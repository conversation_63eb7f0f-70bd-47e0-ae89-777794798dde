package com.mi.oa.infra.mibpm.infra.repository.deploy.converter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.input.BOMInputStream;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;

/**
 * bpmn model xml和json转换器
 *
 * <AUTHOR>
 * @date 2022/1/5 20:47
 */
@Slf4j
public class BpmnConverter {

    public static BpmnModel convertToBpmnModel(String xml) {
        InputStream inputStream = new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8));
        XMLInputFactory xif = XMLInputFactory.newInstance();
        // 防止xml外部实体注入漏洞
        xif.setProperty(XMLInputFactory.SUPPORT_DTD, false);
        xif.setProperty(XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES, false);

        BOMInputStream bomInputStream = new BOMInputStream(inputStream);
        InputStreamReader in = new InputStreamReader(bomInputStream, StandardCharsets.UTF_8);
        XMLStreamReader xtr;
        try {
            xtr = xif.createXMLStreamReader(in);
            // xml转model
            return new BpmnXMLConverter().convertToBpmnModel(xtr);
        } catch (Exception e) {
            log.error("xml to bpmn model error, xml {}", xml, e);
        }
        return null;
    }
}
