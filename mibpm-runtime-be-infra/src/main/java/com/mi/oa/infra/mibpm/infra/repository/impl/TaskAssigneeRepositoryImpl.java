package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.mi.oa.infra.mibpm.common.model.TaskAssignee;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.TaskAssigneeRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiErpProcessInstanceAssignee;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.service.MiErpProcessInstanceAssigneeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/9 18:30
 */
@Repository
public class TaskAssigneeRepositoryImpl implements TaskAssigneeRepository {

    @Autowired
    private MiErpProcessInstanceAssigneeService miErpProcessInstanceAssigneeService;

    @Override
    public void saveTaskAssignee(ProcessInstanceDo processInstanceDo, List<TaskAssignee> taskAssignees) {
        if (Objects.isNull(processInstanceDo) || CollectionUtils.isEmpty(taskAssignees)) {
            return;
        }
        List<MiErpProcessInstanceAssignee> pathList = new ArrayList<>();
        taskAssignees.forEach(taskAssignee -> pathList.add(MiErpProcessInstanceAssignee.builder()
                .approver(taskAssignee.getAssignee())
                .businessKey(processInstanceDo.getBusinessKey())
                .process(processInstanceDo.getModelCode())
                .task(taskAssignee.getTaskDefinitionKey())
                .build()));

        miErpProcessInstanceAssigneeService.saveBatch(pathList);
    }

}
