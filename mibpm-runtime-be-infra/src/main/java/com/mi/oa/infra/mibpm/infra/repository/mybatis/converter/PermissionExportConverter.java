package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionDo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.RequirePermissionPo;
import org.mapstruct.Mapper;

/**
 * @<PERSON> lix<PERSON>han
 * @Date 2024/7/16 18:07
 *
 */
@Mapper(componentModel = "spring")
public interface PermissionExportConverter {
    RequirePermissionPo doToPo(PermissionDo permissionDo);

    PermissionDo poToDo(RequirePermissionPo requirePermissionPo);
}
