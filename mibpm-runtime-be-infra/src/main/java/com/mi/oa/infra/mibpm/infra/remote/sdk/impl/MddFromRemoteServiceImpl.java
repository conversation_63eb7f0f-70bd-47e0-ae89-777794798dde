package com.mi.oa.infra.mibpm.infra.remote.sdk.impl;

import com.alibaba.cola.dto.SingleResponse;
import com.mi.oa.infra.mibpm.common.exception.InfraException;
import com.mi.oa.infra.mibpm.flowable.bpmn.BpmnModelService;
import com.mi.oa.infra.mibpm.infra.procinst.errorcode.ProcInstInfraErrorCodeEnum;
import com.mi.oa.infra.mibpm.infra.procinst.remote.MddFromRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.entity.MddFormDefDTO;
import com.mi.oa.infra.mibpm.infra.remote.sdk.MddProcessFormService;
import com.mi.ucf.mdd.dto.data.FormDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.bpmn.model.UserTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/19 14:40
 */
@Slf4j
@Service
public class MddFromRemoteServiceImpl implements MddFromRemoteService {

    @Autowired
    private MddProcessFormService mddProcessFormService;
    @Autowired
    private BpmnModelService bpmnModelService;

    @Override
    public Map<String, Object> querySubmitFormDef(String processDefinitionId) {
        if (StringUtils.isBlank(processDefinitionId)) {
            throw new InfraException(ProcInstInfraErrorCodeEnum.PARAM_PROC_DEF_ID_IS_EMPTY);
        }

        String submitTaskKey = getSubmitTaskKey(processDefinitionId);
        String clientType = "web";
        try {
            SingleResponse<MddFormDefDTO> response = mddProcessFormService.getProcessFromDef(processDefinitionId,
                    submitTaskKey, clientType);
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                MddFormDefDTO fromDef = response.getData();
                Map<String, Object> schema = fromDef.getSchema();
                Map<String, Object> componentProps = (Map<String, Object>) schema.get("componentProps");
                List<Map<String, Object>> controls = (List<Map<String, Object>>) componentProps.get("controls");

                List<Map<String, Object>> components = TreeUtil.tileTree(controls,
                        childMap -> (List<Map<String, Object>>) childMap.get("controls"));
                components.forEach(component -> {
                    if (BooleanUtils.isFalse((Boolean) component.get("visible"))) {
                        component.put("operationPermission", "hidden");
                        component.remove("visible");
                    } else if (BooleanUtils.isTrue((Boolean) component.get("disabled"))) {
                        component.put("operationPermission", "readOnly");
                        component.remove("disabled");
                    } else {
                        component.put("operationPermission", "normal");
                    }

                    if (StringUtils.isNotBlank((String) component.get("title"))) {
                        component.put("id", (String) component.get("title"));
                    } else if (StringUtils.isNotBlank((String) component.get("name"))) {
                        component.put("id", (String) component.get("name"));
                    } else {
                        component.put("id", (String) component.get("label") + (String) component.get("value"));
                    }

                });

                return response.getData().getSchema();
            }
        } catch (Exception e) {
            log.error("查询表单定义失败, processDefinitionId = {}, taskDefinitionId = {}, clientType = {}",
                    processDefinitionId, submitTaskKey, clientType, e);
        }

        return Collections.emptyMap();
    }

    @Override
    public Map<String, List<Map<String, Object>>> queryFormDefOfTask(String processDefinitionId) {
        if (StringUtils.isBlank(processDefinitionId)) {
            throw new InfraException(ProcInstInfraErrorCodeEnum.PARAM_PROC_DEF_ID_IS_EMPTY);
        }

        Map<String, List<Map<String, Object>>> formDefMap = new LinkedHashMap<>();

        List<UserTask> userTasks = getUserTasks(processDefinitionId);
        if (CollectionUtils.isNotEmpty(userTasks)) {
            userTasks.forEach(userTask -> {
                String clientType = "web";
                try {
                    SingleResponse<MddFormDefDTO> response = mddProcessFormService.getProcessFromDef(
                            processDefinitionId,
                            userTask.getId(), clientType);
                    if (response.isSuccess() && Objects.nonNull(response.getData())) {
                        MddFormDefDTO fromDef = response.getData();
                        Map<String, Object> schema = fromDef.getSchema();
                        Map<String, Object> componentProps = (Map<String, Object>) schema.get("componentProps");
                        List<Map<String, Object>> controls = (List<Map<String, Object>>) componentProps.get("controls");

                        List<Map<String, Object>> components = TreeUtil.tileTree(controls,
                                childMap -> (List<Map<String, Object>>) childMap.get("controls"));

                        List<Map<String, Object>> data = new ArrayList<>();
                        for (Map<String, Object> component : components) {
                            if (!StringUtils.equals("formGroup", (String) component.get("type"))) {
                                data.add(component);
                            }
                        }

                        data.forEach(component -> {
                            component.remove("controls");
                            if (BooleanUtils.isFalse((Boolean) component.get("visible"))) {
                                component.put("operationPermission", "hidden");
                                component.remove("visible");
                            } else if (BooleanUtils.isTrue((Boolean) component.get("disabled"))) {
                                component.put("operationPermission", "readOnly");
                                component.remove("disabled");
                            } else {
                                component.put("operationPermission", "normal");
                            }

                            if (StringUtils.isNotBlank((String) component.get("title"))) {
                                component.put("id", (String) component.get("title"));
                            } else if (StringUtils.isNotBlank((String) component.get("name"))) {
                                component.put("id", (String) component.get("name"));
                            } else {
                                component.put("id", (String) component.get("label") + (String) component.get("value"));
                            }

                        });

                        formDefMap.put(userTask.getId(), data);
                    }
                } catch (Exception e) {
                    log.error("查询表单定义失败, processDefinitionId = {}, taskDefinitionId = {}, clientType = {}",
                            processDefinitionId, userTask.getId(), clientType, e);
                }

            });

        }
        return formDefMap;
    }

    @Override
    public Map<String, Object> queryFormData(String processInstanceId, String taskDefinitionId) {
        if (StringUtils.isBlank(processInstanceId)) {
            throw new InfraException(ProcInstInfraErrorCodeEnum.PARAM_PROC_INST_ID_IS_EMPTY);
        }
        if (StringUtils.isBlank(taskDefinitionId)) {
            throw new InfraException(ProcInstInfraErrorCodeEnum.INFRA_UNKNOWN_ERROR);
        }

        String clientType = "web";
        try {
            SingleResponse<FormDataDTO> response = mddProcessFormService.getProcessFormData(processInstanceId,
                    taskDefinitionId, clientType);
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("查询表单实例失败, processInstanceId = {}, taskDefinitionId = {}, clientType = {}",
                    processInstanceId, taskDefinitionId, clientType, e);
        }
        return Collections.emptyMap();
    }

    private List<UserTask> getUserTasks(String processDefinitionId) {
        Process process = bpmnModelService.findMainProcess(processDefinitionId);
        if (Objects.isNull(process)) {
            throw new InfraException(ProcInstInfraErrorCodeEnum.PROC_DEF_NOT_EXISTS);
        }

        return process.findFlowElementsOfType(UserTask.class);
    }

    private String getSubmitTaskKey(String processDefinitionId) {
        Process process = bpmnModelService.findMainProcess(processDefinitionId);
        if (Objects.isNull(process)) {
            throw new InfraException(ProcInstInfraErrorCodeEnum.PROC_DEF_NOT_EXISTS);
        }

        List<StartEvent> startEvents = process.findFlowElementsOfType(StartEvent.class);
        for (StartEvent startEvent : startEvents) {
            List<SequenceFlow> outgoingFlows = startEvent.getOutgoingFlows();
            for (SequenceFlow outgoingFlow : outgoingFlows) {
                return outgoingFlow.getTargetRef();
            }
        }

        return "submit";
    }
}
