package com.mi.oa.infra.mibpm.infra.remote.converter;

import java.util.List;

import org.mapstruct.Mapper;

import com.mi.oa.infra.mibpm.infra.category.entity.CategoryDto;

@Mapper(componentModel = "spring")
public interface CategoryDtoConverter {
    
    CategoryDto convert(com.mi.oa.infra.mibpm.sdk.dto.CategoryDto categoryDto);

    List<CategoryDto> convert(List<com.mi.oa.infra.mibpm.sdk.dto.CategoryDto> categoryDtoList);
}
