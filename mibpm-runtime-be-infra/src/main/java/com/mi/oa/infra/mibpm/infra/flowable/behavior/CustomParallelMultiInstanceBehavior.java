package com.mi.oa.infra.mibpm.infra.flowable.behavior;

import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.flowable.bpmn.model.Activity;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior;
import org.flowable.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.engine.runtime.Execution;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义并行多实例行为解释器
 *
 * <AUTHOR>
 * @date 2021/11/29 10:41
 */
public class CustomParallelMultiInstanceBehavior extends ParallelMultiInstanceBehavior {

    private final CustomMultiInstanceBehaviorHelper customMultiInstanceBehaviorHelper;

    public CustomParallelMultiInstanceBehavior(Activity activity,
                                               AbstractBpmnActivityBehavior originalActivityBehavior,
                                               CustomMultiInstanceBehaviorHelper customMultiInstanceBehaviorHelper) {
        super(activity, originalActivityBehavior);

        this.customMultiInstanceBehaviorHelper = customMultiInstanceBehaviorHelper;
    }

    @Override
    protected int createInstances(DelegateExecution multiInstanceRootExecution) {
        customMultiInstanceBehaviorHelper.createMultiInstances(multiInstanceRootExecution);
        return super.createInstances(multiInstanceRootExecution);
    }

    @Override
    public void leave(DelegateExecution execution) {
        FlowElement currentFlowElement = execution.getCurrentFlowElement();
        BpmnExtensionHelper bpmnExtensionHelper = SpringContextUtil.getBean(BpmnExtensionHelper.class);
        if (currentFlowElement instanceof UserTask) {
            UserTaskWrapper userTaskWrapper = bpmnExtensionHelper.getUserTaskWrapper((UserTask) currentFlowElement);
            if (handleSignature(execution, userTaskWrapper)) {
                return;
            }
            if (handleVote(execution, userTaskWrapper)) {
                return;
            }
        }
        super.leave(execution);
    }

    private boolean handleSignature(DelegateExecution execution, UserTaskWrapper userTaskWrapper) {
        Boolean signFlag = (Boolean) execution.getVariableLocal(BpmVariablesConstants.VARIABLE_SIGN_FLAG);
        if (BooleanUtils.isNotTrue(signFlag)) {
            return false;
        }
        UserTaskSignType signType = userTaskWrapper.getSignType();
        SignAddTypeEnum signAddTypeEnum = (SignAddTypeEnum) execution.getVariableLocal(
                BpmVariablesConstants.VARIABLE_SIGN_TYPE);
        if (UserTaskSignType.PARALLEL_ONE.equals(signType)) {
            // 或签，某一个实例通过即通过节点，因此需要特殊处理加签任务，当加签主任务完成时，不离开节点
            List<String> subTaskIds = (List<String>) execution.getVariableLocal(
                    BpmVariablesConstants.VARIABLE_SIGN_SUB_TASK);
            if (CollectionUtils.isNotEmpty(subTaskIds)) {
                return true;
            } else {
                if (SignAddTypeEnum.SIGN_AFTER.equals(signAddTypeEnum)) {
                    super.leave(execution);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @param execution
     * @param userTaskWrapper
     * @return true 则直接结束leave false则继续后续
     */
    private boolean handleVote(DelegateExecution execution, UserTaskWrapper userTaskWrapper) {

        UserTaskSignType voteType = userTaskWrapper.getSignType();
        if (!UserTaskSignType.VOTE.equals(voteType) && !UserTaskSignType.ANONYMOUS_VOTE.equals(voteType)) {
            return false;
        }
        String operation = (String) execution.getVariable(BpmVariablesConstants.VARIABLE_VOTE_OPERATION);
        if (null == operation) {
            return false;
        }

        Integer total = getLoopVariable(execution, BpmVariablesConstants.VARIABLE_NUMBER_TOTAL_INSTANCES);
        Integer completedCount = getLoopVariable(execution, BpmVariablesConstants.VARIABLE_NUMBER_COMPLETED_INSTANCES);
        DelegateExecution miRootExecution = getMultiInstanceRootExecution(execution);
        if (UserTaskOperation.VOTE_AGREE.getCode().equals(operation)) {
            Integer agreeCount = getLoopVariable(execution, BpmVariablesConstants.VARIABLE_VOTE_AGREE_COUNT);
            agreeCount += 1;
            if (miRootExecution != null) { // will be null in case of empty collection
                Double agreeRate = agreeCount / (double) total;
                miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_AGREE_COUNT, agreeCount);
                miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_AGREE_RATE, agreeRate);
            }
        } else if (UserTaskOperation.VOTE_ABSTAIN.getCode().equals(operation)) {
            Integer abstainCount = getLoopVariable(execution, BpmVariablesConstants.VARIABLE_VOTE_WAIVE_COUNT);
            abstainCount += 1;
            if (miRootExecution != null) {
                Double abstainRate = abstainCount / (double) total;
                miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_WAIVE_COUNT, abstainCount);
                miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_WAIVE_RATE, abstainRate);
            }
        } else if (UserTaskOperation.VOTE_REJECT.getCode().equals(operation)) {
            Integer rejectCount = getLoopVariable(execution, BpmVariablesConstants.VARIABLE_VOTE_REJECT_COUNT);
            List<String> vetoTaskIds = (List<String>) execution.getVariable(BpmVariablesConstants.VARIABLE_VOTE_VETO_EXECUTION_IDS);
            boolean isVetoTask = CollectionUtils.isNotEmpty(vetoTaskIds) && vetoTaskIds.contains(execution.getId());
            if (BooleanUtils.isTrue(isVetoTask)) {
                // 一票否决处理为所有人都投否决票
                miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_MUST_REJECTED, true);
            } else {
                rejectCount += 1;
            }
            if (miRootExecution != null) {
                Double rejectRate = rejectCount / (double) total;
                miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_REJECT_COUNT, rejectCount);
                miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_REJECT_RATE, rejectRate);
            }
        }
        if (completedCount + 1 >= total && null != miRootExecution) {
            miRootExecution.setVariableLocal(BpmVariablesConstants.VARIABLE_NUMBER_COMPLETED_INSTANCES,
                    completedCount + 1);
            Boolean mustRejected = (Boolean) miRootExecution.getVariable(BpmVariablesConstants.VARIABLE_VOTE_MUST_REJECTED);
            if (!super.completionConditionSatisfied(miRootExecution) || BooleanUtils.isTrue(mustRejected)) {
                rejectProcessInstance(miRootExecution);
                return true;
            }
        }
        return false;
    }

    void rejectProcessInstance(DelegateExecution execution) {
        RepositoryService repositoryService = CommandContextUtil.getProcessEngineConfiguration().getRepositoryService();
        RuntimeService runtimeService = CommandContextUtil.getProcessEngineConfiguration().getRuntimeService();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(execution.getProcessDefinitionId());
        if (Objects.nonNull(bpmnModel)) {
            List<EndEvent> endEvents = bpmnModel.getMainProcess().findFlowElementsOfType(EndEvent.class);
            if (CollectionUtils.isNotEmpty(endEvents)) {
                // 选取一个流程跳转的目标节点
                String distFlowElementId = endEvents.get(0).getId();
                // 获取要结束掉的执行实例
                List<Execution> executions = runtimeService.createExecutionQuery()
                        .parentId(execution.getProcessInstanceId()).list();
                List<String> executionIds = executions.stream().map(Execution::getId).collect(Collectors.toList());
                runtimeService.setVariable(execution.getProcessInstanceId(),
                        BpmVariablesConstants.VARIABLE_PROC_INST_STATUS,
                        UserTaskOperation.REJECT.getCode());
                // 执行跳转
                runtimeService.createChangeActivityStateBuilder()
                        .moveExecutionsToSingleActivityId(executionIds, distFlowElementId)
                        .changeState();
            }
        }
    }
}
