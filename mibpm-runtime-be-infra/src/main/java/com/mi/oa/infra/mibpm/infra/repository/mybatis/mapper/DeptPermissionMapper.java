package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DeptPermissionPo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 14:03
 */
@Mapper
public interface DeptPermissionMapper extends BaseMapper<DeptPermissionPo> {
    @Delete("DELETE FROM mi_bpm_dashboard_permission_dept WHERE user_orpid = #{userOrpid} AND dept_id = #{deptId}")
    int deleteDeptPermission(@Param("userOrpid") String userOrpid, @Param("deptId") String deptId);

    @Delete("DELETE FROM mi_bpm_dashboard_permission_dept WHERE user_orpid = #{userOrpid}")
    int deleteDeptAllPermission(@Param("userOrpid") String userOrpid);

    @Select("SELECT * FROM mi_bpm_dashboard_permission_dept WHERE user_orpid = #{userOrpid} AND dept_id = #{deptId}")
    DeptPermissionPo selectInitDeptId(@Param("userOrpid") String userOrpid, @Param("deptId") String deptId);

    @Select("SELECT * FROM mi_bpm_dashboard_permission_dept WHERE user_orpid = #{userOrpid}")
    List<DeptPermissionPo> selectDeptPermission(@Param("userOrpid") String userOrpid);
}
