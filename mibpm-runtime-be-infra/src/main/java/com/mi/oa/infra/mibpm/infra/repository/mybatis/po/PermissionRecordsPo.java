package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import com.mi.oa.infra.oaucf.newauth.core.authority.accesss.authority.PermissionType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/16 16:28
 */
@Data
@TableName("mi_bpm_permission_records")
public class PermissionRecordsPo extends BasePO<PermissionRecordsPo> {
    /**
     *用户名
     */
    private String userOrpid;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 数据权限Id
     */
    private String permissionId;

    /**
     * 清单Id
     */
    private String businessKey;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 权限类型
     */
    private PermissionIdType permissionIdType;

}
