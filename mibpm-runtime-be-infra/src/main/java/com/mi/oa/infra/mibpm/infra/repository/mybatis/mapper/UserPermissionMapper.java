package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.UserPermissionPo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 14:01
 */
@Mapper
public interface UserPermissionMapper extends BaseMapper<UserPermissionPo> {

    @Delete("DELETE FROM mi_bpm_dashboard_permission_assignee WHERE user_orpid = #{userOrpid} AND permission_user_id = #{permissionUserId}")
    int deleteUserPermission(@Param("userOrpid") String userOrpid, @Param("permissionUserId") String permissionUserId);

    @Delete("DELETE FROM mi_bpm_dashboard_permission_assignee WHERE user_orpid = #{userOrpid}")
    int deleteUserAllPermission(@Param("userOrpid") String userOrpid);

    @Select("SELECT * FROM mi_bpm_dashboard_permission_assignee WHERE user_orpid = #{userOrpid}")
    List<UserPermissionPo> selectUserPermission(@Param("userOrpid") String userOrpid);

    @Select("SELECT * FROM mi_bpm_dashboard_permission_assignee WHERE user_orpid = #{userOrpid} AND permission_user_id = #{permissionUserId}")
    UserPermissionPo selectAssigneeId(@Param("userOrpid") String userOrpid, @Param("permissionUserId") String permissionUserId);
}
