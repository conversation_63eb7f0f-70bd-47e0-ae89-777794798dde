package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.MiBpmProperties;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskListQueryReq;
import com.mi.oa.infra.mibpm.eventbus.EventThreadPoolFactory;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.TaskInstPoConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.NotifiedTaskMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.NotifiedTaskPo;
import com.mi.oa.infra.mibpm.infra.task.repository.TaskQueryRepository;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Repository;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/30 10:45 AM
 **/
@Repository
@Slf4j
public class TaskQueryRepositoryImpl implements TaskQueryRepository {

    @Autowired
    private TaskInstPoConverter taskInstPoConverter;
    @Autowired
    private NotifiedTaskMapper notifiedTaskMapper;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private ModelMetaRepository modelMetaRepository;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private MiBpmProperties miBpmProperties;
    @Autowired
    private MiTaskRepository miTaskRepository;

    private final Executor executor = new ThreadPoolExecutor(30, 100, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(10),
            new ThreadFactoryBuilder().setNameFormat("task-query-%d").build(),
            new EventThreadPoolFactory.BlockingPolicy());

    @Override
    public PageModel<TaskDo> queryTodoTaskList(TaskListQueryReq req, long pageNum, long pageSize) {
        BpmUser user = accountRemoteService.getUser(req.getUserName());
        if (StringUtils.isNotBlank(req.getUserName()) && (null == user || StringUtils.isBlank(user.getUid()))) {
            return PageModel.build(Lists.newArrayList(), pageSize, pageNum, 0L);
        }
        if (CollectionUtils.isNotEmpty(req.getStatusList())
                && !req.getStatusList().contains(ProcessInstanceStatus.RUNNING)) {
            return PageModel.build(Lists.newArrayList(), pageSize, pageNum, 0L);
        }
        // 将语言上下文通过参数传入子线程
        req.setLanguage(LocaleContextHolder.getLocale().getLanguage());

        if (req.isNewQueryFlag()) {
            Page<MiTaskDo> pendingMiTasks = miTaskRepository
                    .getPendingMiTasks(req.convertToMiTaskQuery(pageNum, pageSize));
            List<TaskDo> taskList = taskInstPoConverter.miTaskToDoList(pendingMiTasks.getRecords());
            taskInstPoConverter.fillTaskDo(taskList, true);
            return PageModel.build(taskList, pageSize, pageNum, pendingMiTasks.getTotal());
        }

        CompletableFuture<List<TaskDo>> taskLisFuture = CompletableFuture.supplyAsync(() -> {
            TaskQuery taskQuery = buildTodoTasksQuery(req, user);
            long firstResult = (pageNum - 1) * pageSize;
            List<Task> tasks = taskQuery.listPage((int) firstResult, (int) pageSize);
            List<TaskDo> taskList = tasks.parallelStream().map(taskInstPoConverter::taskPoToDo)
                    .collect(Collectors.toList());
            taskInstPoConverter.fillTaskDo(taskList, true);
            return taskList;
        }, executor);

        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> {
            return queryTodoTaskCount(req);
        }, executor);

        CompletableFuture<Void> allFuture = CompletableFuture.allOf(taskLisFuture, countFuture);
        allFuture.join();
        List<TaskDo> taskList = taskLisFuture.join();
        Long taskCount = countFuture.join();

        return PageModel.build(taskList, pageSize, pageNum, taskCount);
    }

    @Override
    public Long queryTodoTaskCount(TaskListQueryReq req) {
        BpmUser user = accountRemoteService.getUser(req.getUserName());
        if (StringUtils.isNotBlank(req.getUserName()) && (null == user || StringUtils.isBlank(user.getUid()))) {
            return 0L;
        }

        TaskQuery taskQuery = buildTodoTasksQuery(req, user);
        return taskQuery.count();
    }

    @Override
    public PageModel<TaskDo> queryCcTaskList(TaskListQueryReq req, long pageNum, long pageSize) {
        Page<NotifiedTaskPo> iPage = new Page<>(pageNum, pageSize);
        List<ProcessInstanceStatus> statusList = req.getStatusList();
        QueryWrapper<NotifiedTaskPo> wrapper = new QueryWrapper<NotifiedTaskPo>()
                .in("n.ASSIGNEE_", req.getCurrentUserName(), req.getCurrentUserId())
                .eq(null != req.getReviewed(), "n.REVIEWED_", req.getReviewed());

        // 屏蔽已下线流程的任务
        if (Objects.nonNull(miBpmProperties.getRuntime())
                && CollectionUtils.isNotEmpty(miBpmProperties.getRuntime().getOfflineBpmnModel())) {
            wrapper.notIn("n.PROCESS_KEY_", miBpmProperties.getRuntime().getOfflineBpmnModel());
        }

        if (StringUtils.isNotBlank(req.getModelCode())) {
            wrapper.eq(null != req.getModelCode(), "n.PROCESS_KEY_", req.getModelCode());
        } else if (StringUtils.isNotBlank(req.getCategoryCode())) {
            List<String> modelCodes = modelMetaRepository
                    .queryModelCodeByCategoryCode(Collections.singletonList(req.getCategoryCode()));
            wrapper.in("n.PROCESS_KEY_", modelCodes);
        }
        if (StringUtils.isNotBlank(req.getProcessInstanceName())) {
            wrapper.like("p.NAME_", req.getProcessInstanceName());
        }
        if (StringUtils.isNotBlank(req.getUserName())) {
            BpmUser user = accountRemoteService.getUser(req.getUserName());
            if (null == user) {
                return PageModel.build(Lists.newArrayList(), pageSize, pageNum, 0L);
            }
            wrapper.in("p.START_USER_ID_", user.getUid(), user.getUserName());
        }
        if (null != req.getTaskCreateTimeStart() && null != req.getTaskCreateTimeEnd()) {
            wrapper.ge("n.CREATE_TIME_", req.getTaskCreateTimeStart().toEpochSecond());
            wrapper.lt("n.CREATE_TIME_", req.getTaskCreateTimeEnd().toEpochSecond());
        }
        wrapper.orderBy(null != req.getByTaskCreateTimeAsc(),
                null != req.getByTaskCreateTimeAsc() && req.getByTaskCreateTimeAsc(), "n.ID_");
        Page<NotifiedTaskPo> page;
        if (CollectionUtils.isNotEmpty(statusList) &&
                !statusList.containsAll(Arrays.asList(ProcessInstanceStatus.values()))) {
            if (CollectionUtils.isNotEmpty(statusList)) {
                Set<String> operation = new HashSet<>();
                if (!CollectionUtils.isEmpty(statusList)) {
                    operation.addAll(ProcessInstanceStatus.getOperation(statusList));
                }
                if (!operation.isEmpty()) {
                    wrapper.in("v.TEXT_", operation);
                }
                if (!CollectionUtils.isEmpty(statusList) && statusList.size() == 1
                        && statusList.contains(ProcessInstanceStatus.RUNNING)) {
                    wrapper.isNull("p.END_TIME_");
                } else if (!CollectionUtils.isEmpty(statusList) && statusList.size() == 1
                        && statusList.contains(ProcessInstanceStatus.COMPLETED)) {
                    wrapper.isNotNull("p.END_TIME_");
                }
            }
            page = notifiedTaskMapper.getPageWithVariable(iPage, wrapper);
        } else {
            page = notifiedTaskMapper.getPage(iPage, wrapper);
        }
        final Set<String> processInstanceId = page.getRecords().stream()
                .map(NotifiedTaskPo::getProcessInstanceId)
                .collect(Collectors.toSet());
        if (page.getTotal() == 0) {
            return PageModel.build(Lists.newArrayList(), pageSize, pageNum, 0L);
        }
        final Set<String> taskDefKeySet = page.getRecords().stream()
                .map(NotifiedTaskPo::getTaskDefinitionKey)
                .collect(Collectors.toSet());

        HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery()
                .processInstanceIdIn(processInstanceId)
                .locale(LocaleContextHolder.getLocale().getLanguage())
                .taskDefinitionKeys(taskDefKeySet);
        if (null != req.getByTaskCreateTimeAsc() && req.getByTaskCreateTimeAsc()) {
            query.orderByTaskCreateTime().asc();
        } else {
            query.orderByTaskCreateTime().desc();
        }
        List<HistoricTaskInstance> historicTaskInstances = query.list();
        List<TaskDo> taskList = buildTaskDoList(page.getRecords(), historicTaskInstances);
        taskInstPoConverter.fillTaskDo(taskList, true);
        return PageModel.build(taskList, pageSize, pageNum, page.getTotal());
    }

    @Override
    public List<TaskDo> listTasksByProcInstIds(List<String> procInstIds) {
        if (CollectionUtils.isEmpty(procInstIds)) {
            return new ArrayList<>();
        }
        List<Task> list = taskService.createTaskQuery().processInstanceIdIn(procInstIds).list();
        return list.stream().map(taskInstPoConverter::taskPoToDo).collect(Collectors.toList());
    }

    private TaskQuery buildTodoTasksQuery(TaskListQueryReq req, BpmUser initiator) {
        String currentUserId = escapeSql(req.getCurrentUserId());
        String currentUserName = escapeSql(req.getCurrentUserName());
        String processInstanceName = escapeSql(req.getProcessInstanceName());
        String modelCode = escapeSql(req.getModelCode());
        ZonedDateTime createTime = req.getTaskCreateTime();
        ZonedDateTime taskCreateTimeStart = req.getTaskCreateTimeStart();
        ZonedDateTime taskCreateTimeEnd = req.getTaskCreateTimeEnd();
        String categoryCode = escapeSql(req.getCategoryCode());
        Set<String> userIds = new LinkedHashSet<>();
        String language = req.getLanguage();
        if (StringUtils.isNotBlank(currentUserId)) {
            userIds.add(currentUserId);
        }
        if (StringUtils.isNotBlank(currentUserName)) {
            userIds.add(currentUserName);
        }

        TaskQuery taskQuery = taskService.createTaskQuery();
        // 屏蔽已下线流程的任务
        if (Objects.nonNull(miBpmProperties.getRuntime())
                && CollectionUtils.isNotEmpty(miBpmProperties.getRuntime().getOfflineBpmnModel())) {
            taskQuery.processDefinitionKeyNotIn(miBpmProperties.getRuntime().getOfflineBpmnModel());
        }

        if (CollectionUtils.isNotEmpty(userIds)) {
            taskQuery.or().taskAssigneeIds(userIds).taskCandidateUser(currentUserId).endOr();
        }
        if (StringUtils.isNotEmpty(modelCode)) {
            taskQuery.processDefinitionKey(modelCode);
        } else if (StringUtils.isNotBlank(categoryCode)) {
            List<String> modelCodes = modelMetaRepository.queryModelCodeByCategoryCode(Collections.singletonList(categoryCode));
            taskQuery.processDefinitionKeyIn(modelCodes);
        }

        if (StringUtils.isNotBlank(processInstanceName)) {
            taskQuery.processVariableValueLike(BpmVariablesConstants.VARIABLE_PROC_INST_NAME,
                    "%" + escapeSql(processInstanceName) + "%");
        }

        if (Objects.nonNull(initiator)) {
            taskQuery.processVariableValueEquals(BpmVariablesConstants.VARIABLE_INITIATOR, initiator.getUserName());
        }
        // 默认最大间隔30天
        long dateRange = 30L;
        // 任务到达先后排序
        if (null != req.getByTaskCreateTimeAsc() && req.getByTaskCreateTimeAsc()) {
            if (null != createTime) {
                // 引擎接口没有大于等于，减一秒， a < 2 <=> a<=1
                taskQuery.taskCreatedAfter(Date.from(createTime.minusSeconds(1L).truncatedTo(ChronoUnit.SECONDS).toInstant()));
                taskQuery.taskCreatedBefore(
                        Date.from(createTime.truncatedTo(ChronoUnit.SECONDS).plusDays(dateRange).toInstant()));
            }
            taskQuery.orderByTaskCreateTime().asc();
        } else if (null != req.getByTaskCreateTimeAsc() && !req.getByTaskCreateTimeAsc()) {
            if (null != createTime) {
                taskQuery.taskCreatedAfter(
                        Date.from(createTime.truncatedTo(ChronoUnit.SECONDS).minusDays(dateRange).toInstant()));
                // 引擎接口没有小于等于，加一秒， a > 1 <=> a>=2
                taskQuery.taskCreatedBefore(Date.from(createTime.plusSeconds(1L).truncatedTo(ChronoUnit.SECONDS).toInstant()));
            }
            taskQuery.orderByTaskCreateTime().desc();
        } else if (null != req.getByTaskDueDateAsc() && req.getByTaskDueDateAsc()) {
            taskQuery.orderByTaskDueDate().asc();
        } else if (null != req.getByTaskDueDateAsc() && !req.getByTaskDueDateAsc()) {
            taskQuery.orderByTaskDueDate().desc();
        } else {
            taskQuery.orderByTaskCreateTime().asc();
        }
        if (null != taskCreateTimeStart && null != taskCreateTimeEnd) {
            taskQuery.taskCreatedAfter(Date.from(taskCreateTimeStart.toInstant()));
            taskQuery.taskCreatedBefore(Date.from(taskCreateTimeEnd.toInstant()));
        }
        if (StringUtils.isNotBlank(language)) {
            taskQuery.locale(language);
        } else {
            taskQuery.locale(LocaleContextHolder.getLocale().getLanguage());
        }
        return taskQuery;
    }

    private String escapeSql(String str) {
        if (org.apache.commons.lang.StringUtils.isEmpty(str)) {
            return "";
        }
        str = str.trim();
        str = org.apache.commons.lang.StringUtils.replace(str, "'", "''").replace("\\", "\\\\");
        return str;
    }

    private List<TaskDo> buildTaskDoList(List<NotifiedTaskPo> notifiedTaskPos,
                                         List<HistoricTaskInstance> historicTaskInstances) {
        LinkedHashMap<String, TaskDo> taskDoList = historicTaskInstances.stream().map(taskInstPoConverter::taskPoToDo)
                .collect(Collectors.toMap(i -> i.getProcessInstanceId() + ":" + i.getTaskDefinitionKey(), i -> i,
                        (k1, k2) -> k2, LinkedHashMap::new));
        List<TaskDo> taskDos = new ArrayList<>();
        for (NotifiedTaskPo notifiedTaskPo : notifiedTaskPos) {
            String key = notifiedTaskPo.getProcessInstanceId() + ":" + notifiedTaskPo.getTaskDefinitionKey();
            TaskDo taskDo = taskDoList.get(key);
            if (null != taskDo) {
                taskDo.setReviewed(notifiedTaskPo.getReviewed() == 1);
                taskDo.setNotifiedTaskId(notifiedTaskPo.getId());
                taskDos.add(taskDo);
            } else {
                log.info("notified task empty key {}", key);
            }
        }
        return Lists.newArrayList(taskDos);
    }

}
