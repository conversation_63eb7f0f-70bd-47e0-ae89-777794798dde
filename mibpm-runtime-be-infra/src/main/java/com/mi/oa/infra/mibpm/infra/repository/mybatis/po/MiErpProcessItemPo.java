package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @FileName MiErpProcessItemPo
 * @Description
 * <AUTHOR>
 * @date 2025-04-21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "mi_erp_process_item")
public class MiErpProcessItemPo {
    // 流程KEY值,流程分类KEY值
    @TableId
    private String processKey;
    // 分类中文名称
    private String processName;
    // 分类英文名称
    private String processEnName;
    // 所属父流程分类ID CATE 表示流程分类 其他表示真实流程
    private String parentKey;
    // 是否支持APP审批
    private Integer supportApp;
    // 标记是否使用自由表单
    private Integer useFreeForm;
    // 更新用户
    private String updateUser;
    // 更新时间
    private Date updateTime;
    // 是否支持小程序审批
    private Integer supportMiniapp;
    // 所属应用编码
    private String appCode;
    // 排序
    private Integer processSeq;
}
