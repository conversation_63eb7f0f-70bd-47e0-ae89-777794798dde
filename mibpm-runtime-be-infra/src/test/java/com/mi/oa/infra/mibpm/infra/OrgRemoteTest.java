package com.mi.oa.infra.mibpm.infra;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.infra.remote.entity.PosUserDto;
import com.mi.oa.infra.mibpm.infra.remote.entity.PositionAuthDto;
import com.mi.oa.infra.mibpm.infra.remote.sdk.OrgRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.PositionRemoteService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.ListVO;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.BatchQueryDataPostReq;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.BatchQueryDataPostResponse;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.QueryPosReq;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.organization.rep.OrgVO;
import com.mi.oa.infra.organization.service.OrgService;
import com.mi.oa.infra.uc.common.model.ResourceNewDto;
import static org.assertj.core.api.Assertions.assertThat;
import org.assertj.core.api.ListAssert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/10 7:47 PM
 **/
@SpringBootTest(classes = {InfraTestApplication.class})
@RunWith(SpringRunner.class)
public class OrgRemoteTest {

    @Autowired
    private OrgRemoteService orgRemoteService;
    @Autowired
    private PositionRemoteService positionRemoteService;
    @Autowired
    private OrgService orgService;

    @Test
    public void testOrgRemote() {
        List<BpmUser> users = orgRemoteService.listUserByOrg("IT550203");
        ListAssert<BpmUser> userListAssert = assertThat(users);
        System.out.println(users);
    }

    @Test
    public void testOrgCodeRemote() {
//        BpmUser users = orgRemoteService.getOrgOwner("IT550203");
//        BaseResp<OrgVO> detail = orgService.getDetail("IT550203", true);
        BaseResp<List<OrgVO>> ancestor = orgService.getAncestor("MIGROUP", "IT550203", 1);
        List<OrgVO> listVOBaseResp = orgRemoteService.listOrg("IT550203");
        ListVO<OrgVO> listVO = orgService.getList("MIGROUP", "IT550203", "true").getData();
        ListVO<OrgVO> list2 = orgService.batchGetList("MIGROUP", "IT550203", 1, true, true, true).getData();
        System.out.println(GsonUtils.toJsonWtihNullField(listVOBaseResp));
        System.out.println(GsonUtils.toJsonWtihNullField(listVO));
        System.out.println(GsonUtils.toJsonWtihNullField(list2));
        System.out.println(GsonUtils.toJsonWtihNullField(ancestor));
    }

    @Test
    public void testPosRemote() {
        List<BpmUser> users = orgRemoteService.listUserByJob("YF0005", "IT43", "MIGROUP");
        ListAssert<BpmUser> userListAssert = assertThat(users);
        System.out.println(users);
    }

    @Test
    public void testPosUserRemote() {
        QueryPosReq req = new QueryPosReq();
        req.setJobCode("purchase");
        req.setOrgTreeCode("POSCLASS");
        List<ResourceNewDto> dtos = new ArrayList<>();
        req.setDimensionReqs(dtos);
        ResourceNewDto resourceNewDto = new ResourceNewDto();
        resourceNewDto.setDimensionCode("MATERIAL-PN");
        resourceNewDto.setResourceCode("1230102000157");
        dtos.add(resourceNewDto);
        ResourceNewDto resourceNewDto1 = new ResourceNewDto();
        resourceNewDto1.setDimensionCode("MATERIAL-PN");
        resourceNewDto1.setResourceCode("1611104000227");
        dtos.add(resourceNewDto1);
        req.setMaterialQueryParent(true);
        List<PositionAuthDto> resp = positionRemoteService.listPositions(req);
        System.out.println(resp);
        assertThat(resp);
    }

    @Test
    public void testPosUserListRemote() {
        List<String> posCodes = new ArrayList<>();
        posCodes.add("purchase126");
        posCodes.add("purchase127");
        List<PosUserDto> resp = positionRemoteService.listPosUser("POSCLASS", posCodes);
        System.out.println(resp);
        assertThat(resp);
    }

    @Test
    public void testPosV3() {
        List<BatchQueryDataPostReq.DataDetail> dimensionReqs = new ArrayList<>();
        BatchQueryDataPostReq.DataDetail dataDetail = new BatchQueryDataPostReq.DataDetail();
        dataDetail.setDimensionCode("MATERIAL");
        dataDetail.setResourceCodeList(Lists.newArrayList("1230102000337A"));
        dimensionReqs.add(new BatchQueryDataPostReq.DataDetail());
        BatchQueryDataPostReq req = BatchQueryDataPostReq.builder()
                .appCode("pwxl2orO8ARA")
                .bizUnit("1110")
                .dataDetailList(dimensionReqs)
                .orgTreeCode("MIGROUP")
                .jobCodePath(Lists.newArrayList("sourcing"))
                .jobCode("sourcing")
                .build();
        List<BatchQueryDataPostResponse.PostUserDto> postUserDtos = positionRemoteService.queryPostV3(req);
        List<String> collect = postUserDtos.stream().map(i -> i.getUserName()).collect(Collectors.toList());
        System.out.println(GsonUtils.toJsonWtihNullField(collect));
    }
}
