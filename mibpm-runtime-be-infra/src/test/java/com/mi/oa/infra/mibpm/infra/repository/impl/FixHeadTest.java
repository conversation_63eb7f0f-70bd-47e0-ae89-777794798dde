package com.mi.oa.infra.mibpm.infra.repository.impl;

import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.mockito.InjectMocks;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class FixHeadTest {

    @InjectMocks
    private ProcessInstanceExportRepositoryImpl processInstanceExportRepository;

    @Test
    public void testGetExportFormDataFixedHead() throws Exception {
        // 使用反射调用私有方法
        Method method = ProcessInstanceExportRepositoryImpl.class.getDeclaredMethod("getExportFormDataFixedHead");
        method.setAccessible(true);
        List<List<String>> heads = (List<List<String>>) method.invoke(processInstanceExportRepository);

        // 验证返回的表头列表
        assertNotNull(heads);
        assertEquals(6, heads.size(), "应该有6个表头项");

        // 验证每个表头项的内容
        assertEquals(Collections.singletonList("业务ID"), heads.get(0));
        assertEquals(Collections.singletonList("表单标题"), heads.get(1));
        assertEquals(Collections.singletonList("发起时间"), heads.get(2));
        assertEquals(Collections.singletonList("发起人ID"), heads.get(3));
        assertEquals(Collections.singletonList("发起人姓名"), heads.get(4));
        assertEquals(Collections.singletonList("结束时间"), heads.get(5));
    }

}
