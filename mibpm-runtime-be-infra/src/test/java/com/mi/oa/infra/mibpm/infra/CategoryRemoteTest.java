package com.mi.oa.infra.mibpm.infra;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.TaskQueryMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.TaskQueryPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/24 3:03 PM
 **/
@SpringBootTest(classes = {InfraTestApplication.class})
@RunWith(SpringRunner.class)
@EnableCaching
public class CategoryRemoteTest {

    @Autowired
    private TaskQueryMapper taskQueryMapper;


    @Test
    public void testTaskQuery() {
        QueryWrapper<TaskQueryPo> queryWrapper = new QueryWrapper<TaskQueryPo>().select("PROC_DEF_ID_ as procDefId", "count(*) as count")
                .eq("ASSIGNEE_", "dingxiaoyan");
        List<Map<String, Object>> maps = taskQueryMapper.selectMaps(queryWrapper);
        System.out.println(maps);
    }
}
