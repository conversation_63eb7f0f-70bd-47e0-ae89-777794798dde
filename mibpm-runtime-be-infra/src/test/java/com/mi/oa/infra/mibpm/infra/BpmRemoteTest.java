package com.mi.oa.infra.mibpm.infra;

import com.mi.oa.infra.mibpm.infra.remote.entity.MibpmTaskDetailResp;
import com.mi.oa.infra.mibpm.infra.remote.entity.TaskCategory;
import com.mi.oa.infra.mibpm.infra.remote.entity.TaskListResp;
import com.mi.oa.infra.mibpm.infra.remote.sdk.MiBpmRemoteService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/4/18 4:50 PM
 **/
@SpringBootTest(classes = {InfraTestApplication.class})
@RunWith(SpringRunner.class)
public class BpmRemoteTest {

    @Autowired
    private MiBpmRemoteService miBpmRemoteService;

    @Test
    public void testTaskDetail() {
        MibpmTaskDetailResp bpmFormContent = miBpmRemoteService.getBpmTaskDetail("qiuzhipeng", "32259269",
                "30430660", "HROfferApprove");
        System.out.println(bpmFormContent);
    }

    @Test
    public void testHrCategory() {
        List<TaskCategory> taskCategories = miBpmRemoteService.hrCategoryList("chaizhi", 1, "zh");
        System.out.println(taskCategories);
    }

    @Test
    public void testHrTodoList() {
        TaskListResp taskCategories = miBpmRemoteService.waitTaskList("chaizhi", "", "dimission", 1L, 10L, "zh");
        System.out.println(taskCategories);
    }
}
