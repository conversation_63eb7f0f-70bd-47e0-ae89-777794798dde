package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.FormStoragReq;
import com.mi.oa.infra.mibpm.utils.FormContentRedisKeyGenerator;
import com.mi.oa.infra.mibpm.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * FormContentCacheRepositoryImpl 单元测试类
 * 测试 Redis 缓存表单内容的完整功能
 *
 * <AUTHOR>
 * @date 2025/6/5
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class CacheFormImplTest2 {

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private FormContentRedisKeyGenerator keyGenerator;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private FormContentCacheRepositoryImpl formContentCacheRepository;

    private FormStoragReq validFormStoragReq;
    private FormStoragReq invalidFormStoragReq;
    private Map<String, Object> mockFormData;
    private long defaultExpirationTime;

    @Before
    public void setUp() {
        // 准备模拟表单数据
        mockFormData = new HashMap<>();
        mockFormData.put("applicantName", "张三");
        mockFormData.put("department", "技术部");
        mockFormData.put("amount", 5000.50);
        mockFormData.put("reason", "项目开发费用");
        mockFormData.put("urgent", true);

        // 准备有效的表单暂存请求
        validFormStoragReq = FormStoragReq.builder()
                .modelCode("test_model_001")
                .userName("testUser")
                .formData(mockFormData)
                .build();

        // 准备无效的表单暂存请求（空模型编码）
        invalidFormStoragReq = FormStoragReq.builder()
                .modelCode("")
                .userName("testUser")
                .formData(mockFormData)
                .build();

        // 默认过期时间（30天）
        defaultExpirationTime = 30 * 24 * 60 * 60L;
    }

    // ==================== cacheFormContent 方法测试 ====================

    @Test
    public void testCacheFormContent_Success() throws JsonProcessingException {
        // Given: 准备测试数据
        String expectedKey = "mibpm-form-content:test_model_001:testUser";
        String expectedValue = "{\"applicantName\":\"张三\",\"department\":\"技术部\"}";

        // 验证测试数据准备
        assertNotNull("测试请求不应为空", validFormStoragReq);
        assertEquals("test_model_001", validFormStoragReq.getModelCode());
        assertEquals("testUser", validFormStoragReq.getUserName());
        assertNotNull("表单数据不应为空", validFormStoragReq.getFormData());

        // Mock 依赖方法 - 根据实际方法签名调整
        when(keyGenerator.generateFormContentKey("test_model_001", "testUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(mockFormData))
                .thenReturn(expectedValue);
        // RedisUtil.set() 返回 void，使用 doNothing()
        doNothing().when(redisUtil).set(expectedKey, expectedValue);
        // RedisUtil.expire() 返回 Boolean，使用 when().thenReturn()
        when(redisUtil.expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS))
                .thenReturn(true);

        // When: 执行缓存操作
        boolean result = formContentCacheRepository.cacheFormContent(validFormStoragReq, defaultExpirationTime);
        System.out.println("测试通过");
        // Then: 验证结果
        assertTrue("缓存操作应该成功", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey("test_model_001", "testUser");
        verify(objectMapper, times(1))
                .writeValueAsString(mockFormData);
        verify(redisUtil, times(1))
                .set(expectedKey, expectedValue);
        verify(redisUtil, times(1))
                .expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS);

        log.info("✅ cacheFormContent成功场景测试通过");
    }

    @Test
    public void testCacheFormContent_BasicFunctionality() {
        // 简单的基础功能测试，不依赖Mock

        // 验证被测试对象不为空
        assertNotNull("被测试对象不应为空", formContentCacheRepository);

        // 验证依赖对象不为空
        assertNotNull("RedisUtil不应为空", redisUtil);
        assertNotNull("KeyGenerator不应为空", keyGenerator);
        assertNotNull("ObjectMapper不应为空", objectMapper);

        // 验证测试数据不为空
        assertNotNull("测试请求不应为空", validFormStoragReq);
        assertNotNull("测试表单数据不应为空", mockFormData);

        log.info("✅ 基础功能测试通过");
    }

    @Test
    public void testCacheFormContent_DebugMockSetup() throws JsonProcessingException {
        // 调试Mock设置的测试

        String testKey = "debug-key";
        String testValue = "debug-value";

        // 设置Mock行为
        when(keyGenerator.generateFormContentKey(anyString(), anyString()))
                .thenReturn(testKey);
        when(objectMapper.writeValueAsString(any()))
                .thenReturn(testValue);
        doNothing().when(redisUtil).set(anyString(), anyString());
        when(redisUtil.expire(anyString(), anyLong(), any(TimeUnit.class)))
                .thenReturn(true);

        // 执行测试
        boolean result = formContentCacheRepository.cacheFormContent(validFormStoragReq, 3600L);

        // 验证结果
        assertTrue("调试测试应该成功", result);

        // 验证Mock调用
        verify(keyGenerator, atLeastOnce()).generateFormContentKey(anyString(), anyString());
        verify(objectMapper, atLeastOnce()).writeValueAsString(any());
        verify(redisUtil, atLeastOnce()).set(anyString(), anyString());
        verify(redisUtil, atLeastOnce()).expire(anyString(), anyLong(), any(TimeUnit.class));

        log.info("✅ Mock设置调试测试通过");
    }

    @Test
    public void testCacheFormContent_JsonProcessingException() throws JsonProcessingException {
        // Given: 准备测试数据，模拟JSON序列化异常
        String expectedKey = "mibpm-form-content:test_model_001:testUser";

        when(keyGenerator.generateFormContentKey("test_model_001", "testUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(mockFormData))
                .thenThrow(new JsonProcessingException("JSON序列化失败") {});

        // When: 执行缓存操作
        boolean result = formContentCacheRepository.cacheFormContent(validFormStoragReq, defaultExpirationTime);

        // Then: 验证结果
        assertFalse("JSON序列化异常时缓存应该失败", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey("test_model_001", "testUser");
        verify(objectMapper, times(1))
                .writeValueAsString(mockFormData);
        // Redis操作不应该被调用
        verify(redisUtil, never()).set(anyString(), anyString());
        verify(redisUtil, never()).expire(anyString(), anyLong(), any(TimeUnit.class));

        log.info("✅ JSON序列化异常测试通过");
    }

    @Test
    public void testCacheFormContent_RedisException() throws JsonProcessingException {
        // Given: 准备测试数据，模拟Redis异常
        String expectedKey = "mibpm-form-content:test_model_001:testUser";
        String expectedValue = "{\"applicantName\":\"张三\"}";

        when(keyGenerator.generateFormContentKey("test_model_001", "testUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(mockFormData))
                .thenReturn(expectedValue);
        doThrow(new RuntimeException("Redis连接失败"))
                .when(redisUtil).set(expectedKey, expectedValue);

        // When: 执行缓存操作
        boolean result = formContentCacheRepository.cacheFormContent(validFormStoragReq, defaultExpirationTime);

        // Then: 验证结果
        assertFalse("Redis异常时缓存应该失败", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey("test_model_001", "testUser");
        verify(objectMapper, times(1))
                .writeValueAsString(mockFormData);
        verify(redisUtil, times(1))
                .set(expectedKey, expectedValue);
        // expire方法不应该被调用，因为set方法已经抛出异常
        verify(redisUtil, never()).expire(anyString(), anyLong(), any(TimeUnit.class));

        log.info("✅ Redis异常测试通过");
    }

    @Test
    public void testCacheFormContent_NullRequest() {
        // When & Then: 执行测试并验证异常
        try {
            formContentCacheRepository.cacheFormContent(null, defaultExpirationTime);
            fail("应该抛出 IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // 验证异常消息
            assertTrue("异常消息应该包含相关信息",
                    e.getMessage().contains("无法缓存表单"));
        }

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
//        verify(objectMapper, never()).writeValueAsString(any());
        verify(redisUtil, never()).set(anyString(), anyString());
        verify(redisUtil, never()).expire(anyString(), anyLong(), any(TimeUnit.class));

        log.info("✅ 空请求异常测试通过");
    }

    @Test
    public void testCacheFormContent_EmptyModelCode() {
        // When & Then: 执行测试并验证异常
        try {
            formContentCacheRepository.cacheFormContent(invalidFormStoragReq, defaultExpirationTime);
            fail("应该抛出 IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // 验证异常消息
            assertTrue("异常消息应该包含模型编码为空的信息",
                    e.getMessage().contains("模型编码为空"));
        }

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
//        verify(objectMapper, never()).writeValueAsString(any());
        verify(redisUtil, never()).set(anyString(), anyString());
        verify(redisUtil, never()).expire(anyString(), anyLong(), any(TimeUnit.class));

        log.info("✅ 空模型编码异常测试通过");
    }

    @Test
    public void testCacheFormContent_EmptyUserName() {
        // Given: 准备用户名为空的请求
        FormStoragReq reqWithEmptyUserName = FormStoragReq.builder()
                .modelCode("test_model_001")
                .userName("")
                .formData(mockFormData)
                .build();

        // When & Then: 执行测试并验证异常
        try {
            formContentCacheRepository.cacheFormContent(reqWithEmptyUserName, defaultExpirationTime);
            fail("应该抛出 IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // 验证异常消息
            assertTrue("异常消息应该包含用户名缺失的信息",
                    e.getMessage().contains("用户名缺失"));
        }

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
//        verify(objectMapper, never()).writeValueAsString(any());
        verify(redisUtil, never()).set(anyString(), anyString());
        verify(redisUtil, never()).expire(anyString(), anyLong(), any(TimeUnit.class));

        log.info("✅ 空用户名异常测试通过");
    }

    @Test
    public void testCacheFormContent_FormStoragReqProperties() throws JsonProcessingException {
        // Given: 准备测试数据，验证FormStoragReq的所有属性
        Map<String, Object> complexFormData = new HashMap<>();
        complexFormData.put("userName", "李四");
        complexFormData.put("department", "财务部");
        complexFormData.put("amount", 8000.75);
        complexFormData.put("urgent", false);
        complexFormData.put("items", new String[]{"item1", "item2", "item3"});

        FormStoragReq complexReq = FormStoragReq.builder()
                .modelCode("complex_model_002")
                .userName("complexUser")
                .formData(complexFormData)
                .build();

        // 验证FormStoragReq的属性
        assertNotNull("模型编码不应为空", complexReq.getModelCode());
        assertNotNull("用户名不应为空", complexReq.getUserName());
        assertNotNull("表单数据不应为空", complexReq.getFormData());
        assertEquals("模型编码应该匹配", "complex_model_002", complexReq.getModelCode());
        assertEquals("用户名应该匹配", "complexUser", complexReq.getUserName());
        assertEquals("表单数据大小应该匹配", 5, complexReq.getFormData().size());

        String expectedKey = "mibpm-form-content:complex_model_002:complexUser";
        String expectedValue = "{\"complex\":\"data\"}";

        // Mock 依赖方法
        when(keyGenerator.generateFormContentKey("complex_model_002", "complexUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(complexFormData))
                .thenReturn(expectedValue);
        doNothing().when(redisUtil).set(expectedKey, expectedValue);
        when(redisUtil.expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS))
                .thenReturn(true);

        // When: 执行缓存操作
        boolean result = formContentCacheRepository.cacheFormContent(complexReq, defaultExpirationTime);

        // Then: 验证结果
        assertTrue("复杂表单数据缓存应该成功", result);

        // 验证传递给依赖方法的参数
        verify(keyGenerator, times(1))
                .generateFormContentKey("complex_model_002", "complexUser");
//        verify(objectMapper, times(1))
//                .writeValueAsString(argThat(formData ->
//                    formData.size() == 5 &&
//                    "李四".equals(formData.get("userName")) &&
//                    "财务部".equals(formData.get("department")) &&
//                    Double.valueOf(8000.75).equals(formData.get("amount"))
//                ));
        verify(redisUtil, times(1))
                .set(expectedKey, expectedValue);
        verify(redisUtil, times(1))
                .expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS);

        log.info("✅ FormStoragReq属性验证测试通过");
    }

    @Test
    public void testCacheFormContent_DifferentExpirationTimes() throws JsonProcessingException {
        // Given: 测试不同的过期时间
        String expectedKey = "mibpm-form-content:test_model_001:testUser";
        String expectedValue = "{\"test\":\"data\"}";

        when(keyGenerator.generateFormContentKey("test_model_001", "testUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(mockFormData))
                .thenReturn(expectedValue);
        doNothing().when(redisUtil).set(expectedKey, expectedValue);
        when(redisUtil.expire(eq(expectedKey), anyLong(), eq(TimeUnit.SECONDS)))
                .thenReturn(true);

        // 测试1小时过期时间
        long oneHourExpiration = 60 * 60L;
        boolean result1 = formContentCacheRepository.cacheFormContent(validFormStoragReq, oneHourExpiration);
        assertTrue("1小时过期时间缓存应该成功", result1);

        // 测试7天过期时间
        long sevenDaysExpiration = 7 * 24 * 60 * 60L;
        boolean result2 = formContentCacheRepository.cacheFormContent(validFormStoragReq, sevenDaysExpiration);
        assertTrue("7天过期时间缓存应该成功", result2);

        // 验证expire方法被调用了2次，分别使用不同的过期时间
        verify(redisUtil, times(1))
                .expire(expectedKey, oneHourExpiration, TimeUnit.SECONDS);
        verify(redisUtil, times(1))
                .expire(expectedKey, sevenDaysExpiration, TimeUnit.SECONDS);

        log.info("✅ 不同过期时间测试通过");
    }

    @Test
    public void testCacheFormContent_EmptyFormData() throws JsonProcessingException {
        // Given: 准备空表单数据的请求
        FormStoragReq reqWithEmptyFormData = FormStoragReq.builder()
                .modelCode("test_model_003")
                .userName("testUser")
                .formData(new HashMap<>())
                .build();

        String expectedKey = "mibpm-form-content:test_model_003:testUser";
        String expectedValue = "{}";

        when(keyGenerator.generateFormContentKey("test_model_003", "testUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(new HashMap<>()))
                .thenReturn(expectedValue);
        doNothing().when(redisUtil).set(expectedKey, expectedValue);
        when(redisUtil.expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS))
                .thenReturn(true);

        // When: 执行缓存操作
        boolean result = formContentCacheRepository.cacheFormContent(reqWithEmptyFormData, defaultExpirationTime);

        // Then: 验证结果
        assertTrue("空表单数据缓存应该成功", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey("test_model_003", "testUser");
        verify(objectMapper, times(1))
                .writeValueAsString(new HashMap<>());
        verify(redisUtil, times(1))
                .set(expectedKey, expectedValue);
        verify(redisUtil, times(1))
                .expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS);

        log.info("✅ 空表单数据测试通过");
    }

    @Test
    public void testCacheFormContent_NullFormData() throws JsonProcessingException {
        // Given: 准备null表单数据的请求
        FormStoragReq reqWithNullFormData = FormStoragReq.builder()
                .modelCode("test_model_004")
                .userName("testUser")
                .formData(null)
                .build();

        String expectedKey = "mibpm-form-content:test_model_004:testUser";
        String expectedValue = "null";

        when(keyGenerator.generateFormContentKey("test_model_004", "testUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(null))
                .thenReturn(expectedValue);
        doNothing().when(redisUtil).set(expectedKey, expectedValue);
        when(redisUtil.expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS))
                .thenReturn(true);

        // When: 执行缓存操作
        boolean result = formContentCacheRepository.cacheFormContent(reqWithNullFormData, defaultExpirationTime);

        // Then: 验证结果
        assertTrue("null表单数据缓存应该成功", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey("test_model_004", "testUser");
        verify(objectMapper, times(1))
                .writeValueAsString(null);
        verify(redisUtil, times(1))
                .set(expectedKey, expectedValue);
        verify(redisUtil, times(1))
                .expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS);

        log.info("✅ null表单数据测试通过");
    }

    @Test
    public void testCacheFormContent_KeyGeneratorBehavior() throws JsonProcessingException {
        // Given: 测试键生成器的行为
        String expectedKey = "mibpm-form-content:test_model_001:testUser";
        String expectedValue = "{\"test\":\"data\"}";

        // Mock 键生成器返回预期的键
        when(keyGenerator.generateFormContentKey("test_model_001", "testUser"))
                .thenReturn(expectedKey);
        when(objectMapper.writeValueAsString(mockFormData))
                .thenReturn(expectedValue);
        doNothing().when(redisUtil).set(expectedKey, expectedValue);
        when(redisUtil.expire(expectedKey, defaultExpirationTime, TimeUnit.SECONDS))
                .thenReturn(true);

        // When: 执行缓存操作
        boolean result = formContentCacheRepository.cacheFormContent(validFormStoragReq, defaultExpirationTime);

        // Then: 验证结果和键生成器的调用
        assertTrue("缓存操作应该成功", result);

        // 验证键生成器被正确调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(
                    argThat(modelCode -> "test_model_001".equals(modelCode)),
                    argThat(userName -> "testUser".equals(userName))
                );

        // 验证生成的键被正确使用
        verify(redisUtil, times(1))
                .set(eq(expectedKey), anyString());
        verify(redisUtil, times(1))
                .expire(eq(expectedKey), anyLong(), eq(TimeUnit.SECONDS));

        log.info("✅ 键生成器行为测试通过");
    }

    // ==================== getCachedFormContent 方法测试 ====================
    //测试getCachedFormContent这个放啊

    @Test
    public void testGetCachedFormContent_Success() throws JsonProcessingException {
        // Given: 准备测试数据
        String modelCode = "test_model_001";
        String userName = "testUser";
        String expectedKey = "mibpm-form-content:test_model_001:testUser";
        String jsonValue = "{\"applicantName\":\"张三\",\"department\":\"技术部\",\"amount\":5000.5,\"reason\":\"项目开发费用\",\"urgent\":true}";

        // Mock 依赖方法
        when(keyGenerator.generateFormContentKey(modelCode, userName))
                .thenReturn(expectedKey);
        when(redisUtil.get(expectedKey))
                .thenReturn(jsonValue);
        when(objectMapper.readValue(jsonValue, Map.class))
                .thenReturn(mockFormData);

        // When: 执行获取缓存操作
        Map<String, Object> result = formContentCacheRepository.getCachedFormContent(modelCode, userName);

        // Then: 验证结果
        assertNotNull("获取的表单数据不应为空", result);
        assertEquals("表单数据大小应该匹配", 5, result.size());
        assertEquals("申请人姓名应该匹配", "张三", result.get("applicantName"));
        assertEquals("部门应该匹配", "技术部", result.get("department"));
        assertEquals("金额应该匹配", 5000.50, result.get("amount"));
        assertEquals("原因应该匹配", "项目开发费用", result.get("reason"));
        assertEquals("紧急标志应该匹配", true, result.get("urgent"));

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(modelCode, userName);
        verify(redisUtil, times(1))
                .get(expectedKey);
        verify(objectMapper, times(1))
                .readValue(jsonValue, Map.class);

        log.info("✅ getCachedFormContent成功场景测试通过");
    }

    @Test
    public void testGetCachedFormContent_CacheNotFound() {
        // Given: 准备测试数据，模拟缓存不存在
        String modelCode = "test_model_002";
        String userName = "testUser2";
        String expectedKey = "mibpm-form-content:test_model_002:testUser2";

        when(keyGenerator.generateFormContentKey(modelCode, userName))
                .thenReturn(expectedKey);
        when(redisUtil.get(expectedKey))
                .thenReturn(null); // 缓存不存在

        // When: 执行获取缓存操作
        Map<String, Object> result = formContentCacheRepository.getCachedFormContent(modelCode, userName);

        // Then: 验证结果
        assertNull("缓存不存在时应该返回null", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(modelCode, userName);
        verify(redisUtil, times(1))
                .get(expectedKey);
//        // objectMapper不应该被调用，因为没有数据需要反序列化
//        verify(objectMapper, never())
//                .readValue(anyString(), eq(Map.class));

        log.info("✅ 缓存不存在场景测试通过");
    }

    @Test
    public void testGetCachedFormContent_EmptyValue() {
        // Given: 准备测试数据，模拟空字符串值
        String modelCode = "test_model_003";
        String userName = "testUser3";
        String expectedKey = "mibpm-form-content:test_model_003:testUser3";

        when(keyGenerator.generateFormContentKey(modelCode, userName))
                .thenReturn(expectedKey);
        when(redisUtil.get(expectedKey))
                .thenReturn(""); // 空字符串

        // When: 执行获取缓存操作
        Map<String, Object> result = formContentCacheRepository.getCachedFormContent(modelCode, userName);

        // Then: 验证结果
        assertNull("空字符串时应该返回null", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(modelCode, userName);
        verify(redisUtil, times(1))
                .get(expectedKey);
//        // objectMapper不应该被调用
//        verify(objectMapper, never())
//                .readValue(anyString(), eq(Map.class));

        log.info("✅ 空字符串值场景测试通过");
    }

    @Test
    public void testGetCachedFormContent_JsonProcessingException() throws JsonProcessingException {
        // Given: 准备测试数据，模拟JSON反序列化异常
        String modelCode = "test_model_005";
        String userName = "testUser5";
        String expectedKey = "mibpm-form-content:test_model_005:testUser5";
        String invalidJson = "invalid-json";

        when(keyGenerator.generateFormContentKey(modelCode, userName))
                .thenReturn(expectedKey);
        when(redisUtil.get(expectedKey))
                .thenReturn(invalidJson);
        when(objectMapper.readValue(invalidJson, Map.class))
                .thenThrow(new JsonProcessingException("JSON格式错误") {});

        // When & Then: 执行测试并验证异常
        try {
            formContentCacheRepository.getCachedFormContent(modelCode, userName);
            fail("应该抛出 RuntimeException");
        } catch (RuntimeException e) {
            // 验证异常消息
            assertTrue("异常消息应该包含反序列化失败信息",
                    e.getMessage().contains("反序列化表单内容失败"));
            assertTrue("异常原因应该是JsonProcessingException",
                    e.getCause() instanceof JsonProcessingException);
        }

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(modelCode, userName);
        verify(redisUtil, times(1))
                .get(expectedKey);
        verify(objectMapper, times(1))
                .readValue(invalidJson, Map.class);

        log.info("✅ JSON反序列化异常测试通过");
    }

    @Test
    public void testGetCachedFormContent_EmptyModelCode() {
        // When & Then: 执行测试并验证异常
        try {
            formContentCacheRepository.getCachedFormContent("", "testUser");
            fail("应该抛出 IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // 验证异常消息
            assertEquals("异常消息应该匹配", "无法获取表单缓存，模型编码或者用户名缺失", e.getMessage());
        }

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).get(anyString());
//        verify(objectMapper, never()).readValue(anyString(), eq(Map.class));

        log.info("✅ 空模型编码异常测试通过");
    }

    @Test
    public void testGetCachedFormContent_NullUserName() {
        // When & Then: 执行测试并验证异常
        try {
            formContentCacheRepository.getCachedFormContent("test_model_001", null);
            fail("应该抛出 IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // 验证异常消息
            assertEquals("异常消息应该匹配", "无法获取表单缓存，模型编码或者用户名缺失", e.getMessage());
        }

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).get(anyString());
//        verify(objectMapper, never()).readValue(anyString(), eq(Map.class));

        log.info("✅ null用户名异常测试通过");
    }

    // ==================== removeCachedFormContent 方法测试 ====================
    //removeCachedFormContent方法测试

    @Test
    public void testRemoveCachedFormContent_Success() {
        // Given: 准备测试数据
        String modelCode = "test_model_001";
        String userId = "testUser";
        String expectedKey = "mibpm-form-content:test_model_001:testUser";

        // Mock 依赖方法
        when(keyGenerator.generateFormContentKey(modelCode, userId))
                .thenReturn(expectedKey);
        doNothing().when(redisUtil).delete(expectedKey);

        // When: 执行删除缓存操作
        boolean result = formContentCacheRepository.removeCachedFormContent(modelCode, userId);

        // Then: 验证结果
        assertTrue("删除缓存操作应该成功", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(modelCode, userId);
        verify(redisUtil, times(1))
                .delete(expectedKey);

        log.info("✅ removeCachedFormContent成功场景测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_RedisException() {
        // Given: 准备测试数据，模拟Redis异常
        String modelCode = "test_model_002";
        String userId = "testUser2";
        String expectedKey = "mibpm-form-content:test_model_002:testUser2";

        when(keyGenerator.generateFormContentKey(modelCode, userId))
                .thenReturn(expectedKey);
        doThrow(new RuntimeException("Redis连接失败"))
                .when(redisUtil).delete(expectedKey);

        // When: 执行删除缓存操作
        boolean result = formContentCacheRepository.removeCachedFormContent(modelCode, userId);

        // Then: 验证结果
        assertFalse("Redis异常时删除应该失败", result);

        // 验证内部方法调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(modelCode, userId);
        verify(redisUtil, times(1))
                .delete(expectedKey);

        log.info("✅ Redis异常测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_EmptyModelCode() {
        // When: 执行删除缓存操作，传入空模型编码
        boolean result = formContentCacheRepository.removeCachedFormContent("", "testUser");

        // Then: 验证结果
        assertFalse("空模型编码时删除应该失败", result);

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).delete(anyString());

        log.info("✅ 空模型编码测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_NullModelCode() {
        // When: 执行删除缓存操作，传入null模型编码
        boolean result = formContentCacheRepository.removeCachedFormContent(null, "testUser");

        // Then: 验证结果
        assertFalse("null模型编码时删除应该失败", result);

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).delete(anyString());

        log.info("✅ null模型编码测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_EmptyUserId() {
        // When: 执行删除缓存操作，传入空用户ID
        boolean result = formContentCacheRepository.removeCachedFormContent("test_model_001", "");

        // Then: 验证结果
        assertFalse("空用户ID时删除应该失败", result);

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).delete(anyString());

        log.info("✅ 空用户ID测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_NullUserId() {
        // When: 执行删除缓存操作，传入null用户ID
        boolean result = formContentCacheRepository.removeCachedFormContent("test_model_001", null);

        // Then: 验证结果
        assertFalse("null用户ID时删除应该失败", result);

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).delete(anyString());

        log.info("✅ null用户ID测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_BothParametersEmpty() {
        // When: 执行删除缓存操作，传入空参数
        boolean result = formContentCacheRepository.removeCachedFormContent("", "");

        // Then: 验证结果
        assertFalse("两个参数都为空时删除应该失败", result);

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).delete(anyString());

        log.info("✅ 两个参数都为空测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_BothParametersNull() {
        // When: 执行删除缓存操作，传入null参数
        boolean result = formContentCacheRepository.removeCachedFormContent(null, null);

        // Then: 验证结果
        assertFalse("两个参数都为null时删除应该失败", result);

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).delete(anyString());

        log.info("✅ 两个参数都为null测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_StringUtilsBlankCheck() {
        // Given: 测试StringUtils.isBlank的各种情况

        // 测试空格字符串模型编码
        boolean result1 = formContentCacheRepository.removeCachedFormContent("   ", "testUser");
        assertFalse("空格模型编码应该返回false", result1);

        // 测试制表符用户ID
        boolean result2 = formContentCacheRepository.removeCachedFormContent("test_model_001", "\t\n\r");
        assertFalse("制表符用户ID应该返回false", result2);

        // 测试空白字符串
        boolean result3 = formContentCacheRepository.removeCachedFormContent("  \t  ", "  \n  ");
        assertFalse("空白字符串应该返回false", result3);

        // 验证没有调用任何依赖方法
        verify(keyGenerator, never()).generateFormContentKey(anyString(), anyString());
        verify(redisUtil, never()).delete(anyString());

        log.info("✅ StringUtils.isBlank检查测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_KeyGeneratorBehavior() {
        // Given: 测试键生成器的行为
        String modelCode = "key_test_model";
        String userId = "keyTestUser";
        String customKey = "custom-form-key:model123:user456";

        when(keyGenerator.generateFormContentKey(modelCode, userId))
                .thenReturn(customKey);
        doNothing().when(redisUtil).delete(customKey);

        // When: 执行删除缓存操作
        boolean result = formContentCacheRepository.removeCachedFormContent(modelCode, userId);

        // Then: 验证结果
        assertTrue("删除操作应该成功", result);

        // 验证键生成器被正确调用
        verify(keyGenerator, times(1))
                .generateFormContentKey(
                    argThat(mc -> modelCode.equals(mc)),
                    argThat(uid -> userId.equals(uid))
                );

        // 验证生成的键被正确使用
        verify(redisUtil, times(1))
                .delete(eq(customKey));

        log.info("✅ 键生成器行为测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_MethodCallOrder() {
        // Given: 准备测试数据，验证方法调用顺序
        String modelCode = "order_test_model";
        String userId = "orderTestUser";
        String expectedKey = "mibpm-form-content:order_test_model:orderTestUser";

        when(keyGenerator.generateFormContentKey(modelCode, userId))
                .thenReturn(expectedKey);
        doNothing().when(redisUtil).delete(expectedKey);

        // When: 执行删除缓存操作
        boolean result = formContentCacheRepository.removeCachedFormContent(modelCode, userId);

        // Then: 验证结果
        assertTrue("删除操作应该成功", result);

//        // 使用 InOrder 验证方法调用顺序
//        var inOrder = inOrder(keyGenerator, redisUtil);
//
//        // 1. 首先生成键
//        inOrder.verify(keyGenerator).generateFormContentKey(modelCode, userId);
//
//        // 2. 然后从Redis删除
//        inOrder.verify(redisUtil).delete(expectedKey);

        log.info("✅ 方法调用顺序测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_DifferentModelCodes() {
        // Given: 测试不同的模型编码
        String[] modelCodes = {"model_001", "model_002", "complex_model_with_underscores", "model-with-dashes"};
        String userId = "testUser";

        for (String modelCode : modelCodes) {
            String expectedKey = "mibpm-form-content:" + modelCode + ":" + userId;

            when(keyGenerator.generateFormContentKey(modelCode, userId))
                    .thenReturn(expectedKey);
            doNothing().when(redisUtil).delete(expectedKey);

            // When: 执行删除缓存操作
            boolean result = formContentCacheRepository.removeCachedFormContent(modelCode, userId);

            // Then: 验证结果
            assertTrue("模型编码 " + modelCode + " 的删除操作应该成功", result);
        }

        // 验证所有模型编码都被处理
        verify(keyGenerator, times(modelCodes.length))
                .generateFormContentKey(anyString(), eq(userId));
        verify(redisUtil, times(modelCodes.length))
                .delete(anyString());

        log.info("✅ 不同模型编码测试通过");
    }

    @Test
    public void testRemoveCachedFormContent_DifferentUserIds() {
        // Given: 测试不同的用户ID
        String modelCode = "test_model_001";
        String[] userIds = {"user001", "user002", "admin_user", "test-user-with-dashes"};

        for (String userId : userIds) {
            String expectedKey = "mibpm-form-content:" + modelCode + ":" + userId;

            when(keyGenerator.generateFormContentKey(modelCode, userId))
                    .thenReturn(expectedKey);
            doNothing().when(redisUtil).delete(expectedKey);

            // When: 执行删除缓存操作
            boolean result = formContentCacheRepository.removeCachedFormContent(modelCode, userId);

            // Then: 验证结果
            assertTrue("用户ID " + userId + " 的删除操作应该成功", result);
        }

        // 验证所有用户ID都被处理
        verify(keyGenerator, times(userIds.length))
                .generateFormContentKey(eq(modelCode), anyString());
        verify(redisUtil, times(userIds.length))
                .delete(anyString());

        log.info("✅ 不同用户ID测试通过");
    }
}


