package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.mi.oa.infra.mibpm.common.enums.ExportStatusEnum;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceExportReq;
import com.mi.oa.infra.mibpm.domain.procinst.converter.ProcessInstanceExportConverter;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceExportHistoryDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.ExportConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.TaskInstPoConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.DataExportHistoryMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DataExportHistoryPo;
import com.mi.oa.infra.mibpm.sdk.dto.ModelDto;
import com.mi.oa.infra.oaucf.fds.utils.FDSUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.File;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/19
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({FDSUtils.class})
public class ExportProcessInstanceAndFormTest {
    @InjectMocks
    private ProcessInstanceExportRepositoryImpl exportService; // 替换为你实际的类名
    @Mock
    private HistoricProcInstRepository historicProcInstRepository;

    @Mock
    private AccountRemoteService accountRemoteService;

    @Mock
    private TaskInstPoConverter taskInstPoConverter;

    @Mock
    private ProcessInstanceExportConverter converter;
    @Mock
    private MiTaskRepositoryImpl miTaskRepository;
    @Mock
    private ExportConverter exportConverter;
    @Mock
    private DataExportHistoryMapper dataExportHistoryMapper;
    @Mock
    private ModelRemoteService modelRemoteService;




    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(FDSUtils.class);
        // 假设有 newFilePath 字段，使用反射或set方法注入一个临时目录路径
        String tempDir = System.getProperty("java.io.tmpdir") + File.separator + "export_test" + File.separator;
        new File(tempDir).mkdirs();  // 创建目录，确保存在

        exportService.setExportDir(tempDir); ;
        // 打印所有 Mock 对象
        System.out.println("historicProcInstRepository: " + historicProcInstRepository);
        System.out.println("accountRemoteService: " + accountRemoteService);
        System.out.println("taskInstPoConverter: " + taskInstPoConverter);
        System.out.println("converter: " + converter);
        System.out.println("miTaskRepository: " + miTaskRepository);

        // 验证 exportService 是否注入了 Mock 对象
        System.out.println("exportservice: " + exportService);

    }

    @Test
    public void TestExportFormSuccess(){
        ProcessInstanceExportReq req = new ProcessInstanceExportReq();
        req.setModelCode("testModelCode");
        ModelDto modelDto = new ModelDto();
        modelDto.setModelCode(req.getModelCode());
        modelDto.setName("testModel");
        when(modelRemoteService.queryByCode(req.getModelCode())).thenReturn(modelDto);
        ProcessInstanceExportHistoryDo result = new ProcessInstanceExportHistoryDo();
        result.setModelCode("modelCode");
        result.setExportTime(ZonedDateTime.now());
        result.setRequestMessage("modelCode:modelCode");
        result.setExportStatus(ExportStatusEnum.SUCCESS.getCode());
        result.setCompleteTime(ZonedDateTime.now());
        result.setUrl("http://fakeurl.com/test_export.xlsx");
        DataExportHistoryPo dataExportHistoryPo = new DataExportHistoryPo();
        dataExportHistoryPo.setModelCode("testModel");
        when(exportConverter.doToPo(any(ProcessInstanceExportHistoryDo.class)))
                .thenReturn(dataExportHistoryPo);
        when(dataExportHistoryMapper.insert(any())).thenReturn(1);
        exportService.exportProcessInstanceAndForm(req);
    }


}
