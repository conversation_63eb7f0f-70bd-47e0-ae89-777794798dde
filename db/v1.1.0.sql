create table mi_bpm_operation_history
(
    id               bigint unsigned not null auto_increment primary key comment '主键',
    process_inst_id  varchar(64)     not null default '' comment '流程实例id',
    task_id          varchar(64)     not null default '' comment '任务id',
    task_def_key     varchar(64)     not null default '' comment '任务定义key',
    task_name        varchar(64)     not null default '' comment '任务名称',
    operation        varchar(64)     not null default '' comment '操作',
    assignee         varchar(64)     not null default '' comment '操作人',
    comment          varchar(512)    not null default '' comment '评论',
    target_user      varchar(512)     not null default '' comment '目标用户',
    target_task_def  varchar(64)     not null default '' comment '目标任务定义',
    target_task_id   varchar(64)     not null default '' comment '目标任务id',
    target_task_name varchar(64)     not null default '' comment '目标任务名',
    create_user      varchar(32)              default '' not null comment '创建人',
    create_time      bigint(11) unsigned      default 0 not null comment '创建时间',
    update_user      varchar(32)              default '' not null comment '最后更新人',
    update_time      bigint(11) unsigned      default 0 not null comment '最后更新时间'
) engine = 'innodb'
  charset = 'utf8mb4' comment '操作记录表';

alter table mi_bpm_operation_history add index IDX_PRO_INST_ID(process_inst_id);
alter table mi_bpm_operation_history add index IDX_TASK_ID(task_id);