-- 复制 mi_erp_process_item 表中的数据
INSERT INTO mi_erp_process_item (process_key,
                                 process_name,
                                 process_en_name,
                                 parent_key,
                                 support_app,
                                 use_free_form,
                                 update_user,
                                 update_time,
                                 support_miniapp,
                                 app_code,
                                 process_seq)
SELECT CONCAT(process_key, '1') AS process_key,
       process_name,
       process_en_name,
       parent_key,
       support_app,
       use_free_form,
       update_user,
       update_time,
       support_miniapp,
       app_code,
       process_seq
FROM mi_erp_process_item
WHERE process_key = 'smartHardwareOTAApprove';

-- 复制 mi_erp_process_template_setting 表中的数据
INSERT INTO mi_erp_process_template_setting (process_key,
                                             process_name,
                                             task_detail_auth_user,
                                             business_get_url,
                                             operate_callback_url,
                                             operate_callback_method,
                                             last_task_expression,
                                             sign_task_expression,
                                             transfer_task_expression,
                                             send_lark_type,
                                             send_mail_type,
                                             use_common_mail,
                                             callback_type,
                                             create_time,
                                             create_user,
                                             update_user,
                                             update_time,
                                             create_app_id,
                                             create_app_key,
                                             call_back_app_id,
                                             call_back_app_key,
                                             developer_mail,
                                             continue_on_error,
                                             call_back_num,
                                             call_back_interval,
                                             open_idempotent,
                                             delegate_task_expression,
                                             online_preview,
                                             auto_skip,
                                             default_reject_to,
                                             status)
SELECT CONCAT(process_key, '1') AS process_key,
       process_name,
       task_detail_auth_user,
       business_get_url,
       operate_callback_url,
       operate_callback_method,
       last_task_expression,
       sign_task_expression,
       transfer_task_expression,
       send_lark_type,
       send_mail_type,
       use_common_mail,
       callback_type,
       create_time,
       create_user,
       update_user,
       update_time,
       create_app_id,
       create_app_key,
       call_back_app_id,
       call_back_app_key,
       developer_mail,
       continue_on_error,
       call_back_num,
       call_back_interval,
       open_idempotent,
       delegate_task_expression,
       online_preview,
       auto_skip,
       default_reject_to,
       status
FROM mi_erp_process_template_setting
WHERE process_key = 'smartHardwareOTAApprove';

-- 复制 act_de_model 表中的数据
INSERT INTO act_de_model (id,
                          name,
                          model_key,
                          description,
                          model_comment,
                          created,
                          created_by,
                          last_updated,
                          last_updated_by,
                          version,
                          model_editor_json,
                          thumbnail,
                          model_type,
                          tenant_id,
                          model_status)
SELECT UUID()                 AS id, -- 生成新的UUID
       name,
       CONCAT(model_key, '1') AS model_key,
       description,
       model_comment,
       created,
       created_by,
       last_updated,
       last_updated_by,
       version,
       model_editor_json,
       thumbnail,
       model_type,
       tenant_id,
       model_status
FROM act_de_model
WHERE model_key = 'smartHardwareOTAApprove';

