ALTER TABLE mi_erp_delegation
    ADD COLUMN create_user VARCHAR(50) NOT NULL COMMENT '记录创建人',
    ADD COLUMN delegation_reason LONGTEXT NOT NULL COMMENT '委托理由',
    ADD COLUMN delegation_scope INT NOT NULL DEFAULT 0 COMMENT '表示流程的范围或适用性，0:不限  1:属于 2:不属于',
    ADD COLUMN initiating_dept_code VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '表示流程发起人的部门',
    ADD COLUMN create_time DATETIME NOT NULL COMMENT '记录创建时间（使用时间戳datetime）';

UPDATE mi_erp_delegation
SET initiating_dept_code = ''
WHERE initiating_dept_code = '0';

UPDATE mi_erp_delegation
SET create_user = ''
WHERE create_user = '0';

ALTER TABLE mi_erp_delegation
    MODIFY COLUMN initiating_dept_code VARCHAR (255) NOT NULL DEFAULT '' COMMENT '表示流程发起人的部门',
    MODIFY COLUMN create_user VARCHAR (50) NOT NULL DEFAULT '' COMMENT '记录创建人';

ALTER TABLE mi_erp_delegation CHANGE initiating_dept_code initiating_dept_codes

-- 这里开始更新数据库的SQL（都是SQL审核通过后的）

-- 添加需要的列：
ALTER TABLE mi_erp_delegation
    ADD COLUMN delegation_reason VARCHAR(255) NOT NULL DEFAULT '' COMMENT '委托理由',
    ADD COLUMN delegation_scope INT NOT NULL DEFAULT 0 COMMENT '表示流程的范围或适用性，0:不限  1:属于 2:不属于',
    ADD COLUMN initiating_dept_codes VARCHAR(1024) NOT NULL DEFAULT '' COMMENT '表示流程发起人的部门',
    ADD COLUMN delegation_process_instance_id VARCHAR(255) NOT NULL DEFAULT '' COMMENT '委托流程实例化Id',
    ADD COLUMN create_user VARCHAR(50) NOT NULL DEFAULT '' COMMENT '记录创建人',
    ADD COLUMN create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间（使用datetime）',
    ADD COLUMN version TINYINT(4) NOT NULL DEFAULT 1 COMMENT '版本标识，0表示旧数据，1表示新数据';
-- 更新旧数据version为0
UPDATE mi_erp_delegation SET version = 0;
-- 更新 create_user 为空的记录，将其设置为 update_user
UPDATE mi_erp_delegation
SET create_user = update_user
WHERE create_user = '';
-- 更新 create_time 为空的记录，将其设置为 update_time
UPDATE mi_erp_delegation
SET create_time = update_time
WHERE create_time = '0000-00-00 00:00:00';
-- 更新数据库update_time为自动记录当前时间
-- ALTER TABLE `mi_erp_delegation`
--     MODIFY COLUMN update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON
-- UPDATE CURRENT_TIMESTAMP COMMENT '数据最后更新时间';



-- 根据modelCode将有效的委托信息拆分为多条，并写回数据库
INSERT INTO mi_erp_delegation (user_id, delegation_user_id, process_key, start_time, end_time, status, update_user,
                               update_time, model_code, type, create_user, delegation_reason, delegation_scope,
                               initiating_dept_codes, create_time, delegation_process_instance_id, version)
SELECT
    t.user_id,
    t.delegation_user_id,
    t.process_key,
    t.start_time,
    t.end_time,
    t.status,
    t.update_user,
    t.update_time,
    CONCAT('["', j.model_code, '"]') AS model_code,
    t.type,
    t.create_user,
    t.delegation_reason,
    t.delegation_scope,
    t.initiating_dept_codes,
    t.create_time,
    t.delegation_process_instance_id,
    1 AS version
FROM mi_erp_delegation t
         CROSS JOIN JSON_TABLE(
        t.model_code,
        "$[*]"
            COLUMNS(
    model_code VARCHAR(4096) PATH "$"
  )
) j
WHERE t.end_time > CURRENT_TIMESTAMP AND t.status = 1;