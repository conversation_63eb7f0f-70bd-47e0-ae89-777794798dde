#目前使用的sonar，暂时不用
sonar:
  stage: sonar
  image: hub.pf.xiaomi.com/infoarch/maven-openjdk8-onbuild-sonar:latest
  script:
    - curl -s https://git.n.xiaomi.com/oaucf-share/oaucf-script-utils/raw/master/sonar_analyze_gitn.sh | bash /dev/stdin -Dsonar.projectKey=oa:infra:mibpm -Dsonar.projectName=oa:infra:mibpm
  only:
    - master
  artifacts:
    paths:
      - "**/target"
      - "target"


# fusion-cli是云平台提供的命令行工具, 提供了sonar扫描的能力，暂时不使用此能力
#.fusion-cli: &fusion-cli
#  image:
#    name: cr.d.xiaomi.net/container/xiaomi_alpine_fusion_cli
#  before_script:
#    - fusion-cli sonar config --token ${CI_SONAR_TOKEN}

# sonar阶段会依赖build和test的结果, 另外pom.xml里需要配置jacoco-maven-plugin，暂时不用
#sonar:
#  stage: sonar
#  dependencies:
#    - build
#    - test
#  <<: *fusion-cli
#  script:
#    - fusion-cli sonar scan --badges --report --sources mibpm-runtime-be-interface/mibpm-runtime-be-api/src/main,mibpm-runtime-be-interface/mibpm-runtime-be-task/src/main,mibpm-runtime-be-infra/src/main,mibpm-runtime-be-application/src/main --tests mibpm-runtime-be-interface/mibpm-runtime-be-api/src/test -- -Dsonar.java.binaries=mibpm-runtime-be-interface/mibpm-runtime-be-api/target/classes,mibpm-runtime-be-interface/mibpm-runtime-be-task/target/classes,mibpm-runtime-be-infra/target/classes,mibpm-runtime-be-application/target/classes -Dsonar.coverage.jacoco.xmlReportPaths=mibpm-runtime-be-interface/mibpm-runtime-be-api/target/jacoco.xml -Dsonar.junit.reportPaths=mibpm-runtime-be-interface/mibpm-runtime-be-api/target/surefire-reports
#  only:
#    refs:
#      - merge_requests
#      - master
