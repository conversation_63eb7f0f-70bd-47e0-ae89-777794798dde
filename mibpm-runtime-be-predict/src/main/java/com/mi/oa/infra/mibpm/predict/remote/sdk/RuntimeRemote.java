package com.mi.oa.infra.mibpm.predict.remote.sdk;

import com.mi.id.oaucf.feign.BaseResp;
import com.mi.oa.infra.mibpm.predict.remote.model.ProcInstMonitorVo;
import feign.Param;
import feign.RequestLine;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/22 15:43
 **/
public interface RuntimeRemote {

    @RequestLine("GET /api/v1/proc-monitor/detail/executions/{processInsId}")
    BaseResp<ProcInstMonitorVo> queryDetail(@Param("processInsId") String processInsId);

    @RequestLine("GET /api/v1/proc-monitor/variables/{processInsId}")
    BaseResp<List<ProcInstMonitorVo.VariableVo>> queryVariables(@Param("processInsId") String processInsId);
}
