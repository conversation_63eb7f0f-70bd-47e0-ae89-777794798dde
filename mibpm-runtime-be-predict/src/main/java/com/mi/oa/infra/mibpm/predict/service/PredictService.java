package com.mi.oa.infra.mibpm.predict.service;

import com.mi.oa.infra.mibpm.predict.domain.model.PredictDo;
import com.mi.oa.infra.mibpm.predict.service.dto.PredictTaskDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/21 14:30
 **/
public interface PredictService {

    PredictDo startPredict(String processInstId);

    PredictDo startPredict(String processInstId, Map<String, Object> variables, List<String> currentTaskKeys, String processDefinitionId);

    /**
     * 获取预测结果
     *
     * @param processInstId 流程实例id
     * @param taskKeys      任务key
     * @param sync          是否同步返回
     * @return
     */
    List<PredictTaskDto> getPredictResult(String processInstId, String taskKeys, boolean sync);
}
