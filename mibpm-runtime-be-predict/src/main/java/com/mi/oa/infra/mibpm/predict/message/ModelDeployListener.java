package com.mi.oa.infra.mibpm.predict.message;

import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.infra.repository.deploy.entity.DeployEvent;
import com.mi.oa.infra.mibpm.predict.infra.PredictDefinitionRepository;
import com.mi.oa.infra.mibpm.predict.infra.PredictRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = "${rocketmq.task-predict-deploy-group}",
        topic = "${rocketmq.task-predict-deploy-topic}",
        consumeMode = ConsumeMode.CONCURRENTLY, messageModel = MessageModel.BROADCASTING)
public class ModelDeployListener implements RocketMQListener<DeployEvent> {

    @Autowired
    private PredictRepository predictRepository;
    @Autowired
    private PredictDefinitionRepository predictDefinitionRepository;

    @Override
    public void onMessage(DeployEvent deployEvent) {

        log.info("consume deploy event, event = [{}]", deployEvent);

        String bpmnCode = ModelType.MODEL_TYPE_BPMN.getPrefix();
        String dmnCode = ModelType.MODEL_TYPE_DECISION_TABLE.getPrefix();
        if (null == deployEvent.getModelType() || bpmnCode.equals(deployEvent.getModelType())) {
            String procDefId = deployEvent.getProcDefId();
            log.info("predict consume event procDefId={}", procDefId);
            predictRepository.loadProcessDefinition(procDefId);
        } else if (dmnCode.equals(deployEvent.getModelType())) {
            String modelCode = deployEvent.getProcessKey();
            if (StringUtils.isNotBlank(modelCode)
                    && StringUtils.isNotBlank(deployEvent.getDmnJson())) {
                log.info("predict consume event modelCode={}", modelCode);
                predictDefinitionRepository.deployDmn(modelCode, deployEvent.getDmnJson());
            }
        }
    }
}
