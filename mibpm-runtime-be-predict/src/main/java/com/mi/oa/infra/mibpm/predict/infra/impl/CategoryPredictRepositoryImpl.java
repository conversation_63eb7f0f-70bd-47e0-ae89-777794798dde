package com.mi.oa.infra.mibpm.predict.infra.impl;

import com.mi.oa.infra.mibpm.infra.category.entity.CategoryDto;
import com.mi.oa.infra.mibpm.infra.category.repository.CategoryRepository;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/5/15 21:34
 **/
@Repository
@Primary
public class CategoryPredictRepositoryImpl implements CategoryRepository {
    @Override
    public List<CategoryDto> findAll() {
        return new ArrayList<>();
    }
}
