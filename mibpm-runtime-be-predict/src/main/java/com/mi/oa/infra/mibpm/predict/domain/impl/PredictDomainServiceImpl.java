package com.mi.oa.infra.mibpm.predict.domain.impl;

import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.predict.domain.PredictDomainService;
import com.mi.oa.infra.mibpm.predict.domain.model.PredictDo;
import com.mi.oa.infra.mibpm.predict.domain.model.PredictTaskDo;
import com.mi.oa.infra.mibpm.predict.infra.PredictRepository;
import com.mi.oa.infra.mibpm.predict.infra.converter.PredictRepositoryConverter;
import com.mi.oa.infra.mibpm.predict.remote.RuntimeRemoteService;
import com.mi.oa.infra.mibpm.predict.remote.converter.RuntimeRemoteConverter;
import com.mi.oa.infra.mibpm.predict.remote.model.ProcInstMonitorVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/21 15:43
 **/
@Service
@Slf4j
public class PredictDomainServiceImpl implements PredictDomainService {

    @Autowired
    private RuntimeRemoteService runtimeRemoteService;
    @Autowired
    private PredictRepository predictRepository;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private PredictRepositoryConverter predictRepositoryConverter;
    @Autowired
    private RuntimeRemoteConverter runtimeRemoteConverter;

    @Override
    public PredictDo getPredictDo(String processInstId) {
        PredictDo predictDo = new PredictDo();
        ProcInstMonitorVo procInstMonitorVo = runtimeRemoteService.queryDetail(processInstId);
        ProcessInstanceDo originProcessInstance = runtimeRemoteConverter.dtoToDo(procInstMonitorVo);
        if (null == originProcessInstance) {
            return predictDo;
        }
        List<ProcInstMonitorVo.TaskInstanceVo> historicTasks = procInstMonitorVo.getHistoricTasks();
        List<PredictTaskDo> history = new ArrayList<>();
        List<PredictTaskDo> current = new ArrayList<>();
        for (ProcInstMonitorVo.TaskInstanceVo historicTask : historicTasks) {
            PredictTaskDo predictTaskDo = runtimeRemoteConverter.taskToDo(historicTask);
            if (null == historicTask.getEndTime()) {
                current.add(predictTaskDo);
            } else {
                history.add(predictTaskDo);
            }
        }
        predictDo.setCurrentTasks(current);
        predictDo.setHistoricTasks(history);
        predictDo.setProcInstId(processInstId);
        predictDo.setProcDefId(originProcessInstance.getProcessDefinitionId());
        if (null == procInstMonitorVo.getEndTime()) {
            predictRepository.loadPredictTasksFromCache(predictDo);
        }
        return predictDo;
    }

    @Override
    public PredictDo startPredict(String processInstId, Map<String, Object> variables, List<String> currentTaskKeys, String processDefinitionId) {
        PredictDo predictDo = new PredictDo();
        predictDo.setProcDefId(processDefinitionId);
        predictDo.setProcInstId(processInstId);
        if (null != variables && !variables.isEmpty()) {
            // 排除掉status, 预测不需要传过来的status
            if (variables.containsKey(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS)) {
                String status = String.valueOf(variables.get(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS));
                // 加签需要重新查询信息，不直接使用当前信息
                if (UserTaskOperation.SIGN.getCode().equals(status)) {
                    currentTaskKeys = new ArrayList<>();
                } else {
                    variables.remove(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS);
                    if (!variables.isEmpty()) {
                        predictDo.setVariables(variables);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(currentTaskKeys)) {
            predictDo = getPredictDo(processInstId);
            currentTaskKeys = predictDo.getCurrentTasks().stream().map(PredictTaskDo::getTaskDefKey).distinct().collect(
                    Collectors.toList());
        }
        loadPredictProcess(predictDo);
        if (MapUtils.isEmpty(predictDo.getVariables())) {
            loadVariables(predictDo);
        }
        List<PredictTaskDo> predictTaskDos = predictRepository.startPredict(processInstId, predictDo.getVariables(),
                currentTaskKeys, predictDo.getPredictProcessInstance(), processDefinitionId);
        predictDo.setPredictTasks(predictTaskDos);
        return predictDo;
    }

    @Override
    public void startPredict(PredictDo predictDo) {
        predictRepository.predictTasks(predictDo);
    }

    @Override
    public void loadVariables(PredictDo predictDo) {
        if (null != predictDo) {
            String procInstId = predictDo.getProcInstId();
            if (StringUtils.isBlank(procInstId)) {
                return;
            }
            Map<String, Object> variables = runtimeRemoteService.queryVariables(procInstId);
            predictDo.setVariables(variables);
        }
    }

    @Override
    public void loadPredictProcess(PredictDo predictDo) {
        if (null != predictDo) {
            String processInstId = predictDo.getProcInstId();
            if (StringUtils.isBlank(processInstId)) {
                return;
            }
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceBusinessKey(processInstId).singleResult();
            ProcessInstanceDo predictProcessInstance;
            // query relationship
            //  if null create
            if (null == processInstance) {
                predictProcessInstance = predictRepository.startProcessInstance(
                        predictDo.getProcDefId(), predictDo.getProcInstId(), predictDo.getVariables());
            } else {
                predictProcessInstance = predictRepositoryConverter.poToDo(processInstance);
            }
            log.info("load predict process procInstId={} predictProcInstId={}", predictDo.getProcInstId(),
                    predictProcessInstance.getProcessInstanceId());
            predictDo.setPredictProcessInstance(predictProcessInstance);
        }
    }
}
