package com.mi.oa.infra.mibpm.predict.remote.converter;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.predict.domain.model.PredictTaskDo;
import com.mi.oa.infra.mibpm.predict.remote.model.ProcInstMonitorVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/22 16:08
 **/
@Mapper(componentModel = "spring")
public interface RuntimeRemoteConverter {

    ProcessInstanceDo dtoToDo(ProcInstMonitorVo vo);

    @Mappings(value = {
            @Mapping(source = "taskDefinitionKey", target = "taskDefKey")
    })
    PredictTaskDo taskToDo(ProcInstMonitorVo.TaskInstanceVo task);
}
