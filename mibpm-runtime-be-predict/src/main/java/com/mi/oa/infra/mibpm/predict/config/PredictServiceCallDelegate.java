package com.mi.oa.infra.mibpm.predict.config;

import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;

/**
 * 服务节点委托类
 *
 * <AUTHOR>
 * @date 2022/4/27 20:40
 */
public class PredictServiceCallDelegate implements JavaDelegate {


    public PredictServiceCallDelegate() {

    }

    @Override
    public void execute(DelegateExecution execution) {
        // do nothing
    }

}
