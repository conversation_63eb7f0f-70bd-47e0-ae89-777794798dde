mibpm:
  runtime:
    url: https://bpm-infra.test.mioffice.cn/runtime
    offline-bpmn-model:
    appId: ${oaucf.app.appId}
    server-app-id: ${oaucf.app.appId}
    appSecret: ${oaucf.app.appSecret}
    enabled: true
    env: TEST
    read-timeout: 5
    connect-timeout: 5
  repository:
    enabled: true
    appId: ${oaucf.app.appId}
    appSecret: ${oaucf.app.appSecret}
    url: https://bpm-infra.test.mioffice.cn/repository
  approval:
    task-url: https://ams.test.mioffice.cn/apps/approval/approvalDetail?taskId=
  lark:
    callback-url: https://jtest.mioffice.cn/mibpm/lark/notify/cardEvent
  mibpm-v2:
    appid: mibpm_1008
    appkey@kc-sid: oa-infra.g
    appkey: GCDo1WzeDcjTjClW2wNqda+gnmvlQEsrlpQJiWhcig2f5BgSYj33L78RRFWtd/gZuY+2N4P/GBBH9sN5vetDFocaRpdNupHWGBRg5zomR9l4P6+QJp1RWng33/sJ6AA=
    host: http://jtest.mioffice.cn/mibpm/api/
    auth-url: http://jtest.mioffice.cn/mibpm/auth/getAccessToken
    detail-jsp-url: https://jtest.mioffice.cn/mibpm/client/taskDetails?taskId=%s&procInstId=%s&bpmType=%s
    detail-form-url: https://jtest.mioffice.cn/bpm/#/approvalDetail?taskId=%s&procInstId=%s&bpmType=%s&procDefIdPrefix=%s
  predict:
    enabled: true
    url: http://localhost:8088/predict
    appId: ${oaucf.app.appId}
    server-app-id: ${oaucf.app.appId}
    appSecret: ${oaucf.app.appSecret}
    env: TEST
    white-list-keys:
