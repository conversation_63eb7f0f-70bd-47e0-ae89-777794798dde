# idm
oaucf:
  app:
    appId: MAOWJeROwokI
    appSecret@kc-sid: oa-infra.g
    appSecret: GCCx0B5vSsU3O/cinJHhKdWkNcSAyvhV81rGouH65NnqrBgSriOB6agtQmyNiBJGii9dS4f/GBDdG3UEMAhI348kutDLI53EGBQiSunIEvNPJ6SjMHW7o/5Fw3F86wA=
  swagger:
    #是否开启
    enabled: true
    #标题
    title: MIBPM API文档
    #描述
    description: MIBPM API文档
    #版本
    version: 1.0.0
    #许可证
    license: Apache License, Version 2.0
    #许可证URL
    licenseUrl: https://www.apache.org/licenses/LICENSE-2.0.html
    #联系人
    contact:
      #邮箱
      email: <EMAIL>
      #名称
      name: 王琛
      url:
    #扫描的包路径
    base-package: com.mi.oa.infra
    #会解析的url规则
    basePath: /**
    #排除的url规则
    excludePath: /error
  auth:
    #当前应用 appId (工作台生成)
    appId: ${oaucf.app.appId}
    #当前应用 appSecret (工作台生成)
    appSecret: ${oaucf.app.appSecret}
    #环境配置
    env: TEST
    #认证配置
    auth:
      #CAS认证配置
      cas:
        #是否开启CAS认证
        enable: true
        #Cas 认证类型(目前只支持 ValidationType.CAS)
        validation-type: cas
        #Cas 客户端发送Renew
        send-renew: false
        #Cas 服务端域名
        server-url-prefix: https://castest.mioffice.cn/
        #Cas 服务端登录请求路径
        server-login-url: https://castest.mioffice.cn/login
        #Cas 客户端(业务系统)域名
        client-host-url: http://localhost:8088
        #Cas 客户端 登录认证请求URL
        authenticationUrl: /api/v1/auth/login
        #Cas 客户端 登录成功重定向URL
        login-success-url: /doc.html
        #Cas 客户端 登出URL
        logoutPath: /api/v1/auth/logout
      #飞书认证配置
      lark:
        #是否开启飞书认证
        enable: true
        #飞书认证：飞书认证域名
        url: https://open.f.mioffice.cn/
        #飞书颁发的app-id
        app-id: cli_a0327b651b6b9063
        #飞书颁发的app-secret
        app-secret@kc-sid: oa-infra.g
        app-secret: GDBizUvEWUJWSc5t+OmqOhQrND1P5VJaNXDuyzxiITdV5uYGB3AQDPWxLP4ccfSct9EYEmI99y+/EURVrXf4GbmPtjeD/xgQWfg/Q/TwSd+sApGAtnj6JBgUG9na8rNG0E9wIYEinW3ZmAUrHkMA
      #应用认证配置
      app:
        #是否开启应用认证
        enable: true
        appAuthSkip: true
        #应用认证方式:local 本地，remote 远程
        type: remote
        #本地认证 appId:appSecret
        app-secret-map:
          uc-authority-client: uc-authority-client-secret
        #应用认证方式:local 本地，remote 远程
        #type: remote
        #远程认证：远程认证域名
        remoteHost: https://workbench-ee.test.mioffice.cn
    # 权限配置
    authority:
      appCode: ${oaucf.app.appId}
      enable: true
    token:
      #是否使用远程(认证中心)生成校验token
      remoteCheck: true
      #是否使用远程(认证中心)生成校验token
      #remoteCheck: true
      #远程(认证中心)服务地址
      remoteHost: https://workbench-ee.test.mioffice.cn
      #Token 的过期时间(天)
      days: 1
      #token认证,不验证URL
      excludes:
        - /api/v1/getAccessToken
        - /api/v1/lark/notify/cardEvent
  cors:
    enable: true
    allowed-cors-origins:
      - http://workbench-ee.test.mioffice.cn
      - http://localhost:3001
      - http://localhost:3002
      - http://*************:3001
      - http://ams.test.mioffice.cn
      - https://ams.test.mioffice.cn
  idm-api:
    enabled: true
    url: http://api.id.mioffice.cn
    app-id: mibpm
    app-secret@kc-sid: oa-infra.g
    app-secret: GDCe9SWY3NzqX7U5UomRyQ5xYV/nuQYUqtpIzAO/5INO0rM8LrIOFXpzu5njW4vmrzkYEsmyMO3Uf0FTtZEM5c/IqaFy/xgQ3Lj0R27NSvuBH6D4f+WoRhgU+ws4SGziGjasIPfwRyBDXwKweOEA
  organization:
    enabled: true
    appId: MPlUtWIdINAl
    appSecret@kc-sid: oa-infra.g
    appSecret: GCAE6R/KoH42R4LkbRQn3zoy+5nQmLAHvSUlMqZ3vJNucxgSriOB6agtQmyNiBJGii9dS4f/GBDvh09MEEpOAY0dum4sEIP4GBQ538OGVAYCKnqv3QH5To0unX9lDgA=
    url: https://org.infra.mioffice.cn
  workbench: #工作台
    url: https://workbench-ee.test.mioffice.cn
    appId: ${oaucf.app.appId} # 工作台申请的应用 ID
    appSecret: ${oaucf.app.appSecret} # 工作台申请的应用 secret
  form:
    enabled: true
    url: https://bpm-infra.test.mioffice.cn/form                     # api的url
    connectTimeout: 10                                               # 连接超时时间 单位秒
    readTimeout: 60                                                  # 读取超时时间 单位秒
  mybatis-plus:
    open-insert-fill: true
    open-update-fill: true
  onequery:
    enabled: true
    url: http://one-query-data.test.mioffice.cn/onequery
# UC
uc:
  admin:
    sdk:
      service:
        enabled: true
        url: http://uc-infra.test.mioffice.cn/admin
        service-id: ${oaucf.app.appId}
        service-secret: ${oaucf.app.appSecret}
# rocket mq
rocketmq:
  name-server: staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
  producer:
    group: bpm-runtime-api-call-dev
    sendMessageTimeout: 300000
    access-key: AKVAIJQ5T56N42L763
    secret-key@kc-sid: oa-infra.g
    secret-key: GDBGtUpdacVL64J6OqWL1atGpEGiOUPNqW9Hm/vM7S0lh+3BQBZIUQ+q17uoIE9nB8wYEuE8bWM700BItKge3N2B71i8/xgQ82QQivEgRaSsMciXRnV79RgUrSMINY555G/x1ZruETaQV9yTsBYA
  consumer:
    group: bpm-runtime-api-call-dev
    access-key: AKVAIJQ5T56N42L763
    secret-key@kc-sid: oa-infra.g
    secret-key: GDAEQrq7J7kq/I+iWFdbMAUxbYwYjBwu1eayS3fhw2a5JhMDEX5NeO9/liF3Kfjwi0cYEgmgxDjb/E72vTd62kpzvHfo/xgQmI8fS6qTTnO9/jI/KvV7BRgUd5DBcmottTs3iveMpTpQHjjS00EA
  #topics
  api-call-topic: bpm-runtime-api-call-dev
  api-call-group: bpm-runtime-api-call-group-dev
  appbadge-topic: bpm-runtime-appbadge-dev
  appbadge-group: bpm-runtime-appbadge-group-dev
# ums
ums:
  open:
    sdk:
      app-config:
        app-id: S00174
        app-secret@kc-sid: oa-infra.g
        app-secret: GFDf+HEESKOsiAuJSDCJ6t3YdZpxDJzTVtTL9WVuHuvFvGe/FBBeiLJ+PupTI5fn0cdEgBfe2W/kUuVbBM37xy5bg923yOLTGHyBLxUF/Z3QERgSK5pIz23dRFqH8bpQ2wIia6T/GBC4dcIfNg5OCbST58JmubPeGBQLhVHrwY+KTtsMKA8KFsbVHRcqyQA=
      env: prod
      timeout: 30000
      templateBizId: TLB050700001
      botBizId: B0507
larksuite:
  api-base-path: https://open.f.mioffice.cn/
  app-short-name: myAppName
#  app-id: cli_9f0d9f3668bc5062
#  app-secret@kc-sid: oa-infra.g
#  app-secret: GDAF6PPENzpjsTMpIvccvNp/fvNRo86qifcr0U/ZADJ3MQPT7FbZVmkI5q4yzjzuF+kYEq4jgemoLUJsjYgSRoovXUuH/xgQyfO8ZdGTRGqdcjCmk8KmEBgUggqf2H2E8CKRB7YdiCk9fjjuMmQA
  app-id: cli_a0327b651b6b9063
  #飞书颁发的app-secret
  app-secret@kc-sid: oa-infra.g
  app-secret: GDBizUvEWUJWSc5t+OmqOhQrND1P5VJaNXDuyzxiITdV5uYGB3AQDPWxLP4ccfSct9EYEmI99y+/EURVrXf4GbmPtjeD/xgQWfg/Q/TwSd+sApGAtnj6JBgUG9na8rNG0E9wIYEinW3ZmAUrHkMA
  is-isv: false
mibpm:
  repository:
    enabled: true
    appId: ${oaucf.app.appId}
    appSecret: ${oaucf.app.appSecret}
    url: https://bpm-infra.test.mioffice.cn/repository
  approval:
    task-url: https://ams.test.mioffice.cn/apps/approval/approvalDetail?taskId=
  lark:
    callback:
      url: https://jtest.mioffice.cn/mibpm/lark/notify/cardEvent
#hrod
hrod:
  appId: 8502ea1d-3753-45a2-aa99-6dc3f6ec8897
  appKey@kc-sid: oa-infra.g
  appKey: GDDKT9cpPMFslzrFFGY9VhXrqJEdJLaCV6SoGeBHlH4TDabNTj5NEvV2YxBukpd2e2kYEtd+67jxdUbjqQnljgJkMJir/xgQ5upRDRHjTBiE8TNifQEgchgUPuyPW8XleTsnNUanksqJQVdnV1UA
  host: https://api.hrod.mioffice.cn
mi:
  bpm:
    appid: mibpm_1008
    appkey@kc-sid: oa-infra.g
    appkey: GCDo1WzeDcjTjClW2wNqda+gnmvlQEsrlpQJiWhcig2f5BgSYj33L78RRFWtd/gZuY+2N4P/GBBH9sN5vetDFocaRpdNupHWGBRg5zomR9l4P6+QJp1RWng33/sJ6AA=
    host: http://jtest.mioffice.cn/mibpm/api/
    miniapp:
      host: http://api.test.mi.com/mioffice/workflow/mibpm/api/
repository:
  appId: 4pN0ape8lvgd
bpm:
  auth-link: http://jtest.mioffice.cn/mibpm/auth/getAccessToken
  detail:
    jsp:
      link: https://jtest.mioffice.cn/mibpm/client/taskDetails?taskId=%s&procInstId=%s&bpmType=%s
    form:
      link: https://jtest.mioffice.cn/bpm/#/approvalDetail?taskId=%s&procInstId=%s&bpmType=%s&procDefIdPrefix=%s
mdd:
  form:
    enabled: true
    url: http://localhost:8081/mdd
feign:
  compression:
    response:
      enabled: true
      useGzipDecoder: true