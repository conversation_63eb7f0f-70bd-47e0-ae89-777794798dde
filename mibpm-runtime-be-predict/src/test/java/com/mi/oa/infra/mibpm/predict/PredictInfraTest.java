package com.mi.oa.infra.mibpm.predict;

import com.mi.id.oaucf.feign.BaseResp;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.predict.infra.PredictRepository;
import com.mi.oa.infra.mibpm.predict.remote.model.ProcInstMonitorVo;
import com.mi.oa.infra.mibpm.predict.remote.sdk.RuntimeRemote;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants.VARIABLE_INITIATOR;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/21 15:49
 **/
@SpringBootTest(classes = {PredictTestApplication.class})
@RunWith(SpringRunner.class)
public class PredictInfraTest {

    @Autowired
    private PredictRepository predictRepository;
    @Autowired
    private RuntimeRemote runtimeRemote;

    @Test
    public void testCreateProcessInstance() {
        Map<String, Object> variables = new HashMap<>();
        variables.put(VARIABLE_INITIATOR, "dingxiaoyan");
        ProcessInstanceDo s = predictRepository.startProcessInstance("bpmn_756614441211801600:2:38957818", "123",
                variables);
        System.out.println("process instance id " + s);
        List<Task> tasks = predictRepository.listTasks(s.getProcessInstanceId());
        System.out.println(tasks);
    }

    @Test
    public void testRuntimeRemote() {
        BaseResp<ProcInstMonitorVo> procInstMonitorVoBaseResp = runtimeRemote.queryDetail("39005003");
        System.out.println(GsonUtils.toJsonFilterNullField(procInstMonitorVoBaseResp.getData()));
    }
}
