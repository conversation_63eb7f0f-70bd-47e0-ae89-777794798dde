package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.converter.ApplyPermissionVoConverter;
import com.mi.oa.infra.mibpm.api.converter.DeletePermissionVoConverter;
import com.mi.oa.infra.mibpm.api.converter.PermissionRecordVoConverter;
import com.mi.oa.infra.mibpm.api.converter.RequirePermissionVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.ApplyPermissionReqVo;
import com.mi.oa.infra.mibpm.api.model.vo.DeletePermissionReqVo;
import com.mi.oa.infra.mibpm.api.model.vo.PermissionRecordReqVo;
import com.mi.oa.infra.mibpm.api.model.vo.PermissionReqVo;
import com.mi.oa.infra.mibpm.application.message.dto.req.ApplyPermissionReq;
import com.mi.oa.infra.mibpm.application.message.dto.req.DeletePermissionReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.AssigneePermissionResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.DeptPermissionResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.PermissionRecordsDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RequirePermissionDto;
import com.mi.oa.infra.mibpm.application.userconfig.errorcode.PermissionManageErrorCodeEnum;
import com.mi.oa.infra.mibpm.application.userconfig.service.UserDashboardPermissionService;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/15 14:39
 */

@Api(tags = "权限管理")
@WebLog
@RestController
@RequestMapping("/api/v1/user-permission")
public class PermissionController {

    @Autowired
    UserDashboardPermissionService userDashboardPermissionService;
    @Autowired
    ApplyPermissionVoConverter applyPermissionVoConverter;
    @Autowired
    RequirePermissionVoConverter requirePermissionVoConverter;
    @Autowired
    PermissionRecordVoConverter permissionRecordVoConverter;
    @Autowired
    DeletePermissionVoConverter deletePermissionVoConverter;
    @Autowired
    ModelsAuthorityRemote modelsAuthorityRemote;

    @ApiOperation("初始默认部门权限")
    @GetMapping("init/permissions")
    public BaseResp<Void> initPermission() {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            userDashboardPermissionService.initUserPermission();
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
        return BaseResp.success();
    }

    @ApiOperation("查询用户已获得的权限")
    @GetMapping("/permissions/info")
    public BaseResp<PageVO<PermissionReqVo>> getPermissionInfo(@RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                               @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                                               @RequestParam(value = "deptId", required = false) @ApiParam(value = "部门ID") String deptId) {
        PageVO<RequirePermissionDto> page = userDashboardPermissionService.queryPermissionPage(
                pageNum, pageSize, deptId);
        List<PermissionReqVo> pageVo = requirePermissionVoConverter.dtoToVo(page.getList());
        return BaseResp.success(PageVO.build(pageVo, page.getPageSize(), page.getPageNum(), page.getTotal()));
    }

    @ApiOperation("查询权限操作记录")
    @GetMapping("/permissions/records")
    public BaseResp<PageVO<PermissionRecordReqVo>> getPermissionRecords(@RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                                        @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                                                        @RequestParam(value = "deptId", required = false) @ApiParam(value = "部门ID") String deptId) {
        PageVO<PermissionRecordsDto> records = userDashboardPermissionService.queryPermissionRecordsPage(
                pageNum, pageSize, deptId);
        List<PermissionRecordReqVo> recordsVo = permissionRecordVoConverter.dtoToVo(records.getList());
        return BaseResp.success(PageVO.build(recordsVo, records.getPageSize(), records.getPageNum(), records.getTotal()));
    }

    @ApiOperation("申请权限")
    @PostMapping("/apply/permission")
    public BaseResp<Void> applyPermission(@RequestBody ApplyPermissionReqVo applyPermissionReqVo) {
        if (applyPermissionReqVo.getUserOrpid() == null || applyPermissionReqVo.getUserOrpid().isEmpty()) {
            applyPermissionReqVo.setUserOrpid(IdentityUtil.currentUserName());
        }
        ApplyPermissionReq applyPermissionReq = applyPermissionVoConverter.voToDto(applyPermissionReqVo);
        userDashboardPermissionService.applyPermission(applyPermissionReq);
        return BaseResp.success();
    }

    @ApiOperation("删除权限")
    @PostMapping("/remove/permission")
    public BaseResp<Void> deletePermission(@RequestBody DeletePermissionReqVo deletePermissionReqVo) {
        DeletePermissionReq deletePermissionReq = deletePermissionVoConverter.voToDto(deletePermissionReqVo);
        deletePermissionReq.setUserOrpid(IdentityUtil.currentUserName());
        userDashboardPermissionService.removePermission(deletePermissionReq);
        return BaseResp.success();
    }

    @ApiOperation("管理员查询部门权限")
    @GetMapping("admin/query-deptId")
    public BaseResp<List<DeptPermissionResp>> queryDeptId(@RequestParam(value = "userName") @ApiParam(value = "用户名") String userOrpid) {
            return BaseResp.success(userDashboardPermissionService.queryDeptId(userOrpid));
    }

    @ApiOperation("管理员查询直属下级权限")
    @GetMapping("admin/query-assigneeId")
    public BaseResp<List<AssigneePermissionResp>> queryAssigneeId(@RequestParam(value = "userName") @ApiParam(value = "用户名") String userOrpid) {
        return BaseResp.success(userDashboardPermissionService.queryAssigneeId(userOrpid));
    }

    @ApiOperation("管理员删除部门权限")
    @PostMapping("admin/delete-deptId")
    public BaseResp<Void> removeDeptId(@RequestParam(value = "userName") @ApiParam(value = "用户名") String userOrpid,
                                       @RequestParam(value = "deptId", required = false) @ApiParam(value = "部门ID") String deptId) {
        userDashboardPermissionService.removeDeptId(userOrpid, deptId);
        return BaseResp.success();
    }

    @ApiOperation("管理员删除直属下级权限")
    @PostMapping("admin/delete-assigneeId")
    public BaseResp<Void> removeAssigneeId(@RequestParam(value = "userName") @ApiParam(value = "用户名") String userOrpid,
                                           @RequestParam(value = "permissionUserId", required = false) @ApiParam(value = "直属下级ID") String permissionUserId) {
        userDashboardPermissionService.removeAssigneeId(userOrpid, permissionUserId);
        return BaseResp.success();
    }

    @ApiOperation("管理员删除直属下级权限")
    @PostMapping("admin/delete-dashboard")
    public BaseResp<Void> removeDashboardAssigneeId(@RequestParam(value = "userName") @ApiParam(value = "用户名") String userOrpid,
                                           @RequestParam(value = "permissionUserId", required = false) @ApiParam(value = "直属下级ID") String permissionUserId) {
        userDashboardPermissionService.removeDashboard(userOrpid, permissionUserId);
        return BaseResp.success();
    }

    @ApiOperation("管理员添加部门权限")
    @PostMapping("admin/add-deptId")
    public BaseResp<Void> addDeptId(@RequestParam(value = "userName") @ApiParam(value = "用户名") String userOrpid,
                                    @RequestParam(value = "deptId") @ApiParam(value = "部门ID") String deptId) {
        userDashboardPermissionService.addDeptId(userOrpid, deptId);
        return BaseResp.success();
    }

    @ApiOperation("管理员添加直属下级权限")
    @PostMapping("admin/add-assigneeId")
    public BaseResp<Void> addAssigneeId(@RequestParam(value = "userName") @ApiParam(value = "用户名") String userOrpid,
                                        @RequestParam(value = "permissionUserId") @ApiParam(value = "直属下级ID") String permissionUserId) {
        userDashboardPermissionService.addAssigneeId(userOrpid, permissionUserId);
        return BaseResp.success();
    }
}
