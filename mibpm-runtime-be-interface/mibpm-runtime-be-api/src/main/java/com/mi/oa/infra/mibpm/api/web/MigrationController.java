package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.application.proinst.service.FormMigrationService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/19 16:43
 */
@Api(tags = "迁移工具")
@WebLog
@RestController
@RequestMapping("/api/v1/migration")
public class MigrationController {

    @Autowired
    private FormMigrationService processInstanceFormDataService;

    @ApiOperation("迁移表单定义")
    @GetMapping("/form/def")
    public BaseResp<Map<String, Boolean>> migrateFormDefinition(String processDefinitionKey) {
        processInstanceFormDataService.migrateFormDefinition(processDefinitionKey);
        return BaseResp.success();
    }

    @ApiOperation("迁移表单数据")
    @GetMapping("/form/data")
    public BaseResp<Void> migrateFormData(String processDefinitionKey) {
        processInstanceFormDataService.migrateFormData(processDefinitionKey);
        return BaseResp.success();
    }
}
