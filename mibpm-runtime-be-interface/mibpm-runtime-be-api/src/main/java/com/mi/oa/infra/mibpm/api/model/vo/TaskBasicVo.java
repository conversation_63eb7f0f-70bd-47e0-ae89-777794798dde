package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.FormSummaryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/10 16:37
 **/
@Data
public class TaskBasicVo {

    @ApiModelProperty("任务ID")
    protected String taskId;
    @ApiModelProperty("任务名称")
    protected String taskName;
    @ApiModelProperty("任务定义ID")
    protected String taskDefinitionId;
    @ApiModelProperty("任务定义KEY")
    protected String taskDefinitionKey;
    @ApiModelProperty("流程发起人")
    protected BpmUser processInstanceStartUser;
    @ApiModelProperty("流程实例名称")
    protected String processInstanceName;
    @ApiModelProperty("任务处理人")
    protected BpmUser assignee;
    @ApiModelProperty("流程定义ID")
    protected String processDefinitionId;
    @ApiModelProperty("流程实例ID")
    protected String processInstanceId;
    @ApiModelProperty("业务编码")
    protected String businessKey;
    @ApiModelProperty("任务创建时间")
    protected ZonedDateTime createTime;
    @ApiModelProperty("任务完成时间")
    protected ZonedDateTime endTime;
    @ApiModelProperty("当前流程状态")
    protected ProcessInstanceStatus processInstanceStatus;
    @ApiModelProperty("流程发起时间")
    private ZonedDateTime processStartTime;
    @ApiModelProperty("是否为2.0版本的流程")
    private Integer oldType;
    @ApiModelProperty("摘要")
    protected List<FormSummaryDto> summary;
    protected String processCode;
}
