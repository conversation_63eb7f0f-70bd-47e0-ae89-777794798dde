package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.mibpm.api.model.vo.DeletePermissionReqVo;
import com.mi.oa.infra.mibpm.application.message.dto.req.DeletePermissionReq;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2024/08/01 09:42
 */
@Mapper(componentModel = "spring")
public interface DeletePermissionVoConverter {
    DeletePermissionReq voToDto(DeletePermissionReqVo deletePermissionReqVo);
}
