package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.converter.DelegationVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.ApplyDelegationVo;
import com.mi.oa.infra.mibpm.api.model.vo.DelegationApproveUpdateVo;
import com.mi.oa.infra.mibpm.api.model.vo.LeaveDelegationVo;
import com.mi.oa.infra.mibpm.application.delegation.dto.ApplyDelegationDTO;
import com.mi.oa.infra.mibpm.application.delegation.dto.DelegationApproveUpdateDTO;
import com.mi.oa.infra.mibpm.application.delegation.dto.QueryDelegationDTO;
import com.mi.oa.infra.mibpm.application.delegation.service.DelegationService;
import com.mi.oa.infra.mibpm.application.delegation.vo.CancelDelegationVo;
import com.mi.oa.infra.mibpm.application.delegation.vo.QueryDelegationInfoResp;
import com.mi.oa.infra.mibpm.common.enums.DelegationSourceEnum;
import com.mi.oa.infra.mibpm.common.model.LeaveDelegationInfo;
import com.mi.oa.infra.mibpm.common.model.QueryProcessTopCategory;
import com.mi.oa.infra.mibpm.common.model.QueryProcessVo;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/22
 * @Description 委托管理
 */
@Api(tags = "委托管理")
@RestController
@RequestMapping("/api/v1/delegation")
@Slf4j
public class DelegationController {
    @Autowired
    private DelegationVoConverter delegationVoConverter;
    @Autowired
    private DelegationService delegationService;

    @PostMapping("/apply")
    public BaseResp<String> apply(@RequestBody ApplyDelegationVo applyDelegationVo) {
        if (applyDelegationVo.getIsLeave() == null) {
            applyDelegationVo.setIsLeave(false);
        }
        ApplyDelegationDTO applyDelegationDTO = delegationVoConverter.voToDto(applyDelegationVo);
        // 检测是否是请假委托
        applyDelegationDTO.setDelegationSourceEnum(applyDelegationVo.getIsLeave() ?
                DelegationSourceEnum.LEAVE_DELEGATION : DelegationSourceEnum.NORMAL_APPLY_DELEGATION);
        // 检测是否是管理员委托
        if (applyDelegationVo.getDelegationRequirementProposer() != null &&
                !applyDelegationVo.getDelegationRequirementProposer().isEmpty()) {
            applyDelegationDTO.setDelegationSourceEnum(DelegationSourceEnum.ADMIN_SET_DELEGATION);
        }
        // 处理正常委托申请逻辑
        delegationService.applyDelegation(applyDelegationDTO, applyDelegationVo.getIsLeave());
        return BaseResp.success("委托代批申请成功");
    }

    @PostMapping("/update/approveDelegation")
    public BaseResp<String> updateApprove(@RequestBody DelegationApproveUpdateVo delegationApproveUpdateVo) {
        DelegationApproveUpdateDTO delegationApproveUpdateDTO =
                delegationVoConverter.voToDto(delegationApproveUpdateVo);
        delegationService.updateApproveDelegation(delegationApproveUpdateDTO);
        return BaseResp.success("委托审批结束，委托状态更新成功");
    }

    @PostMapping("/leave-apply")
    public BaseResp<String> leaveApply(@RequestBody LeaveDelegationVo leaveDelegationVo) {
        LeaveDelegationInfo leaveDelegationInfo = delegationVoConverter.voToDto(leaveDelegationVo);
        // 处理请假委托申请逻辑
        delegationService.applyLeaveDelegation(leaveDelegationInfo, true);
        return BaseResp.success("请假委托申请成功");
    }

    @GetMapping("/queryAll")
    public BaseResp<PageVO<QueryDelegationInfoResp>> queryAll(
            @RequestParam(value = "delegatorId", required = false) @ApiParam(value = "委托人") String delegatorId,
            @RequestParam(value = "approveId", required = false) @ApiParam(value = "被委托人") String approveId,
            @RequestParam(value = "modelName", required = false) @ApiParam(value = "流程名词") String modelName,
            @RequestParam(value = "modelCode", required = false) @ApiParam(value = "流程id") String modelCode,
            @RequestParam(value = "delegationStartTime", required = false) @ApiParam(value = "委托开始时间")
            String delegationStartTime,
            @RequestParam(value = "delegationEndTime", required = false) @ApiParam(value = "委托结束时间")
            String delegationEndTime,
            @RequestParam(value = "createStartTime", required = false) @ApiParam(value = "委托创建起始时间")
            String createStartTime,
            @RequestParam(value = "createEndTime", required = false) @ApiParam(value = "委托创建结束时间")
            String createEndTime,
            @RequestParam(value = "delegationStatus", required = false) @ApiParam(value = "委托状态")
            Integer delegationStatus,
            @RequestParam(value = "byStartTimeAsc", required = false) @ApiParam(value = "按委托开始时间升序")
            Boolean byStartTimeAsc,
            @RequestParam(value = "byEndTimeAsc", required = false) @ApiParam(value = "按委托结束时间升序")
            Boolean byEndTimeAsc,
            @RequestParam(value = "byCreateTimeAsc", required = false) @ApiParam(value = "按委托创建时间升序")
            Boolean byCreateTimeAsc,
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码")
            Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小")
            Integer pageSize) {
        QueryDelegationDTO queryDelegationDTO = QueryDelegationDTO.builder()
                .delegatorId(delegatorId)
                .approveId(approveId)
                .delegationStartTime(delegationStartTime)
                .delegationEndTime(delegationEndTime)
                .createStartTime(createStartTime)
                .createEndTime(createEndTime)
                .delegationStatus(delegationStatus)
                .modelName(modelName)
                .modelCode(modelCode)
                .byStartTimeAsc(byStartTimeAsc)
                .byEndTimeAsc(byEndTimeAsc)
                .byCreateTimeAsc(byCreateTimeAsc)
                .build();
        PageModel<QueryDelegationInfoResp> repsDelegationInfoPageModel =
                delegationService.queryDelegationsInfo(queryDelegationDTO, pageNum,
                        pageSize);
        return BaseResp.success(delegationVoConverter.pageModeToVoResp(repsDelegationInfoPageModel));
    }

    @DeleteMapping("/cancel")
    public BaseResp<String> cancelDelegation(@RequestBody CancelDelegationVo cancelDelegationVo) {
        delegationService.cancelDelegation(cancelDelegationVo);
        // 处理取消逻辑
        return BaseResp.success("取消委托成功");
    }

    @GetMapping("/process/queryWeb")
    public BaseResp<PageVO<QueryProcessVo>> queryProcessWeb(
            @RequestParam(value = "processName", required = false) @ApiParam(value = "流程名称") String processName,
            @RequestParam(value = "modelCode", required = false) @ApiParam(value = "流程id") String modelCode,
            @RequestParam(value = "categoryCode", required = false) @ApiParam(value = "二级分类的code（传all代表全部）")
            String categoryCode,
            @RequestParam(value = "isMine", required = false) @ApiParam(value = "是否只看我审批过的")
            String isMine,
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码")
            Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小")
            Integer pageSize) {

        PageModel<QueryProcessVo> pageModel =
                delegationService.queryProcessWeb(processName, modelCode, categoryCode, isMine,
                        pageNum, pageSize);
        return BaseResp.success(PageVO.build(pageModel.getList(), pageModel.getPageSize(), pageModel.getPageNum(),
                pageModel.getTotal()));
    }

    @GetMapping("/process/queryMobile")
    public BaseResp<List<QueryProcessTopCategory>> queryProcessMobile(
            @RequestParam(value = "isMine", required = false) @ApiParam(value = "是否只看我审批过的")
            String isMine) {
        List<QueryProcessTopCategory> queryProcessTopCategories = delegationService.queryProcessMobile(isMine);
        return BaseResp.success(queryProcessTopCategories);
    }

}
