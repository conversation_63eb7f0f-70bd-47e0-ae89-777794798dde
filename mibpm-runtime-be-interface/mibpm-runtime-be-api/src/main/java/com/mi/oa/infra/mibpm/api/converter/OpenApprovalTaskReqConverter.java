package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenCcTaskReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenClaimTaskReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenCompleteTaskReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenDelegateTaskReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenReceiveTaskReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenRejectTaskReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenReturnAfterSubmitReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenReturnTaskReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenReturnToSubmitReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenSignAddReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenTransferTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.CcTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ClaimTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.DelegateTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReceiveTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.RejectTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnAfterSubmitReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnToSubmitReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.SignTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.TransferTaskReq;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/7 11:52
 */
@Mapper(componentModel = "spring")
public interface OpenApprovalTaskReqConverter {

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(target = "oldType", constant = "0"),
            @Mapping(source = "variables", target = "variables", qualifiedByName = "initVariables"),
            @Mapping(source = "formData", target = "formData")
    })
    CompleteTaskReq toCompleteTaskReq(OpenCompleteTaskReq openCompleteTaskReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(target = "oldType", constant = "0")
    })
    RejectTaskReq toRejectTaskReq(OpenRejectTaskReq openRejectTaskReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(target = "oldType", constant = "0")
    })
    TransferTaskReq toTransferTaskReq(OpenTransferTaskReq openTransferTaskReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(target = "oldType", constant = "0")
    })
    CcTaskReq toOpenCcTaskReq(OpenCcTaskReq openCcTaskReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(target = "oldType", constant = "0")
    })
    ReturnToSubmitReq toReturnToSubmitReq(OpenReturnToSubmitReq openReturnToSubmitReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(target = "oldType", constant = "0")
    })
    ReturnAfterSubmitReq toReturnAfterSubmitReq(OpenReturnAfterSubmitReq openReturnAfterSubmitReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(target = "oldType", constant = "0")
    })
    ReturnTaskReq toReturnTaskReq(OpenReturnTaskReq openReturnTaskReq);

    ReceiveTaskReq toReceiveTaskReq(OpenReceiveTaskReq openReceiveTaskReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
    })
    ClaimTaskReq toClaimTaskReq(OpenClaimTaskReq openClaimTaskReq);

    @Mappings({
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator")
    })
    DelegateTaskReq toDelegateTaskReq(OpenDelegateTaskReq openDelegateTaskReq);

    @Mappings({
            @Mapping(source = "operator", target = "operator", qualifiedByName = "initOperator"),
            @Mapping(target = "client", constant = "API", resultType = ClientEnum.class),
    })
    SignTaskReq toSignTaskReq(OpenSignAddReq openSignAddReq);

    default SignAddTypeEnum map(String signType) {
        return SignAddTypeEnum.findByCode(signType);
    }

    @Named("initOperator")
    static BpmUser initOperator(String operator) {
        AccountRemoteService accountRemoteService = SpringContextUtil.getBean(AccountRemoteService.class);
        return accountRemoteService.getUser(operator);
    }

    @Named("initVariables")
    static Map<String, Object> initVariables(Map<String, Object> variables) {
        if (Objects.isNull(variables)) {
            return new LinkedHashMap<>();
        } else {
            return new LinkedHashMap<>(variables);
        }
    }
}
