package com.mi.oa.infra.mibpm.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class WhaleFlagInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取flag参数
        String flag = request.getParameter("flag");
        log.info("WhaleFlagInterceptor preHandle flag={}", flag);
        // 将flag参数存入Request属性中
        request.setAttribute("flag", flag);
        return true;
    }
}