package com.mi.oa.infra.mibpm.api.web.openapi;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenFDSResp;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.fds.errorcode.FdsErrorCodeEnum;
import com.mi.oa.infra.oaucf.fds.utils.FDSUtils;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.xiaomi.infra.galaxy.fds.model.HttpMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/9 09:53
 */
@Api(tags = "Open API 附件管理")
@WebLog
@RestController
@RequestMapping("/openapi/v2/files")
public class OpenFileController {

    @ApiOperation("获取预签名地址")
    @GetMapping("/presigned-uri")
    public BaseResp<OpenFDSResp> get(String fileName, @RequestParam(required = false) String httpMethod) {
        //初始化时用生成的新的文件名称
        String nowFileName = FDSUtils.buildNewFileName(fileName);
        HttpMethod nowHttpMethod = null;
        if (StringUtils.isNotEmpty(httpMethod) && "post".equalsIgnoreCase(httpMethod)) {
            nowHttpMethod = HttpMethod.POST;
        }
        //生成对应的预签名路径
        String presignedUri = FDSUtils.generatePresignedUri(nowFileName, nowHttpMethod);
        if (StringUtils.isNotEmpty(presignedUri)) {
            return BaseResp.success(OpenFDSResp.builder().uri(presignedUri)
                    .fileName(nowFileName)
                    .originFileName(fileName)
                    .build());
        } else {
            return BaseResp.error(FdsErrorCodeEnum.FDS_NOT_GET_PRESIGNED_URI);
        }
    }
}
