package com.mi.oa.infra.mibpm.api.web.openapiv3;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.flowable.external.api.ProcessDefinitionRepresentation;
import com.mi.flowable.external.api.ProcessInstanceCheckRepresentation;
import com.mi.flowable.external.api.ProcessInstanceRepresentation;
import com.mi.info.comb.common.x5protocol.core.dto.X5Request;
import com.mi.info.comb.common.x5protocol.core.util.X5ProtocolCoreUtils;
import com.mi.oa.infra.mibpm.application.task.service.ExternalTaskApplicationService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Api(tags = "Open API 统一待办-V3")
@WebLog
@RestController
@RequestMapping("/openapi/v3/external")
public class OpenExternalTaskController {

    @Autowired
    private ExternalTaskApplicationService externalTaskApplicationService;

    /**
     * 创建三方审批定义
     */
    @ResponseBody
    @RequestMapping(value = "/api/external/v1/process-definition/create", method = RequestMethod.POST, produces = "application/json")
    public Map<String, Object> createProcessDefinition(@RequestParam("data") String data) {
        log.info("创建三方审批定义,req={}", data);
        ObjectMapper objectMapper = new ObjectMapper();
        X5Request x5Request = X5ProtocolCoreUtils.parseX5Request(data, objectMapper);
        String body = x5Request.getBody();
        X5Request.Header header = x5Request.getHeader();
        String appId = header.getAppId();
        ProcessDefinitionRepresentation processDefinitionRepresentation = null;
        try {
            processDefinitionRepresentation = GsonUtils.fromJson(body, ProcessDefinitionRepresentation.class);
            log.info("创建三方审批定义,req={}", GsonUtils.toJsonWtihNullField(processDefinitionRepresentation));
            externalTaskApplicationService.createProcessDefinition(processDefinitionRepresentation, appId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        Map<String, String> res = new HashMap<>(1);
        res.put("process_key", processDefinitionRepresentation.getProcessKey());
        return success(res);
    }

    @ResponseBody
    @RequestMapping(value = "/api/external/v1/process-instance/create", method = RequestMethod.POST, produces = "application/json")
    public Map<String, Object> createProcessInstance(@RequestParam("data") String data) {
        log.info("流程实例同步,req={}", data);
        ObjectMapper objectMapper = new ObjectMapper();
        X5Request x5Request = X5ProtocolCoreUtils.parseX5Request(data, objectMapper);
        String escapedBody = x5Request.getBody();
        String body = StringEscapeUtils.unescapeXml(escapedBody);
        X5Request.Header header = x5Request.getHeader();
        String appId = header.getAppId();
        ProcessInstanceRepresentation processInstanceRepresentation = GsonUtils.fromJson(body, ProcessInstanceRepresentation.class);
        if (processInstanceRepresentation == null) {
            return success(null);
        }
        try {
            log.info("流程实例同步,instanceId={},req={}", processInstanceRepresentation.getInstanceId(), JacksonUtils.bean2Json(processInstanceRepresentation));
            externalTaskApplicationService.createProcessInstance(processInstanceRepresentation, appId, processInstanceRepresentation.getInstanceId());
        } catch (Exception e) {
            log.error("流程实例同步异常,instanceId={}", processInstanceRepresentation.getInstanceId(), e);
            throw new RuntimeException(e);
        }
        return success(null);
    }

    /**
     * 三方流程实例校验
     */
    @ResponseBody
    @RequestMapping(value = "/api/external/v1/process-instance/check", method = RequestMethod.POST, produces = "application/json")
    public BaseResp<Void> checkProcessInstance(@RequestParam("data") String data) {
        log.info("流程实例校验,req={}", data);
        ObjectMapper objectMapper = new ObjectMapper();
        X5Request x5Request = X5ProtocolCoreUtils.parseX5Request(data, objectMapper);
        String body = x5Request.getBody();
        X5Request.Header header = x5Request.getHeader();
        String appId = header.getAppId();
        try {
            ProcessInstanceCheckRepresentation processInstanceCheckRepresentation = GsonUtils.fromJson(body,
                    ProcessInstanceCheckRepresentation.class);
            externalTaskApplicationService.checkProcessInstance(processInstanceCheckRepresentation, appId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }

        return BaseResp.success();
    }

    @PostMapping("/complete")
    public BaseResp<Void> completeExternalTask(@RequestParam("taskId") String taskId,
                                               @RequestParam("comment") String comment,
                                               @RequestParam("status") String status,
                                               @RequestParam("approveUser") String approveUser,
                                               @RequestParam(value = "needCallback", defaultValue = "false") boolean needCallback) {
        log.info("统一待办审批任务完成,taskId={}, status={}, userId={}", taskId, status, approveUser);
        externalTaskApplicationService.completeTaskAndCallback(taskId, comment, status, approveUser, needCallback);
        return BaseResp.success();
    }


    private Map<String, Object> success(Object body) {
        return apiResult("200", "success", body);
    }

    private Map<String, Object> apiResult(String code, String desc, Object body) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> header = new HashMap<>();
        header.put("code", code);
        header.put("desc", desc);
        result.put("header", header);
        result.put("body", body);
        return result;
    }
}
