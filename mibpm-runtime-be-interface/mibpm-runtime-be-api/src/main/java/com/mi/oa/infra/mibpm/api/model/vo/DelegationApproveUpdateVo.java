package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2024/8/26
 * @Description 
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DelegationApproveUpdateVo {
    String processInstId;
    ProcessInstanceStatus processInstanceStatus;
    String startTime;
    String endTime;
}