package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.infra.remote.entity.CompleteTaskDTO;
import com.mi.oa.infra.mibpm.infra.remote.sdk.MiBpmRemoteService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/9 4:48 PM
 **/
@Api(tags = "小程序透传")
@RestController
@RequestMapping("/api/v1/miniapp")
public class MiniAppController {
    @Autowired
    private MiBpmRemoteService miBpmRemoteService;

    @PostMapping("/detail")
    @ApiOperation("详情接口（透传）")
    public BaseResp<Map> taskDetail(@RequestBody Map map) {
        Map taskDetail = miBpmRemoteService.oldTaskDetail(map);
        return BaseResp.success(taskDetail);
    }

    @PostMapping("/complete")
    @ApiOperation("操作接口（透传）")
    public BaseResp<Map> completeTask(@RequestBody CompleteTaskDTO map) {
        Map<String, Object> result = miBpmRemoteService.completeTask(map);
        return BaseResp.success(result);
    }

    @PostMapping("/urge")
    @ApiOperation("催办（透传）")
    public BaseResp<Map> urge(@RequestBody Map map) {
        Map urge = miBpmRemoteService.oldUrge(map);
        return BaseResp.success(urge);
    }

    @PostMapping("/discuss")
    @ApiOperation("讨论（透传）")
    public BaseResp<Map> discuss(@RequestBody Map map) {
        Map discuss = miBpmRemoteService.oldDiscuss(map);
        return BaseResp.success(discuss);
    }

    @GetMapping("/getFdsFile")
    @ApiOperation("文件下载（透传）")
    public BaseResp<Object> fileDownload(@RequestParam("newFileName") String newFileName,
                                         @RequestParam(value = "fileType", required = false) String fileType,
                                         @RequestParam(value = "extension", required = false) String extension) {
        Map<String, Object> map = new HashMap<>();
        map.put("newFileName", newFileName);
        map.put("fileType", fileType);
        map.put("extension", extension);
        Object result = miBpmRemoteService.getFdsFile(map);
        return BaseResp.success(result);
    }


    @GetMapping("/**")
    @ApiOperation("通用透传")
    public BaseResp<Object> passRequest(HttpServletRequest request) {
        String pathInfo = request.getRequestURI();
        String url = pathInfo.replace("/runtime/api/v1/miniapp/", "");
        String queryString = request.getQueryString();
        String[] split = queryString.split("&");
        Map<String, Object> paramMap = new HashMap<>();
        for (String s : split) {
            if (StringUtils.isNotBlank(s)) {
                String[] param = s.split("=");
                if (param.length > 1) {
                    paramMap.put(param[0], param[1]);
                }
            }
        }
        Object passRequest = miBpmRemoteService.passRequest(url, paramMap);
        return BaseResp.success(passRequest);
    }
}
