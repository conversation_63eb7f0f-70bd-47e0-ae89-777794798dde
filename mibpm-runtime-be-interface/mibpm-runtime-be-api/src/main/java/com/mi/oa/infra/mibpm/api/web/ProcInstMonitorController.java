package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.converter.ProcInstMonitorVoConverter;
import com.mi.oa.infra.mibpm.api.converter.ProcInstVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.ProcInsVariablesMonitorVo;
import com.mi.oa.infra.mibpm.api.model.vo.ProcInstMonitorListVo;
import com.mi.oa.infra.mibpm.api.model.vo.ProcInstMonitorVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskMonitorVo;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.HistoricProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcInsVariablesMonitorResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcessInstanceMonitorResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.AdminTerminateProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcInstMonitorQueryReqDto;
import com.mi.oa.infra.mibpm.application.proinst.service.ProcInstMonitorService;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskMonitorQueryReqDto;
import com.mi.oa.infra.mibpm.application.task.dto.req.UpdateAssigneeListReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.UpdateVariablesReq;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskInstanceResp;
import com.mi.oa.infra.mibpm.application.task.service.HistoryProcessInstanceService;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceExportReq;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import javax.validation.Valid;

/**
 * 流程监控及干预
 *
 * @author: qiuzhipeng
 * @Date: 2022/5/23 14:46
 */

@Api(tags = "流程监控")
@WebLog
@Slf4j
@RestController
@RequestMapping("/api/v1/proc-monitor")
public class ProcInstMonitorController {

    @Autowired
    private ProcInstMonitorService procInstMonitorService;
    @Autowired
    private ProcInstVoConverter procInstVoConverter;
    @Autowired
    private ProcInstMonitorVoConverter procInstMonitorVoConverter;
    @Autowired
    private HistoryProcessInstanceService historyProcessInstanceService;

    @ApiOperation("流程列表")
    @GetMapping("/process")
    public BaseResp<PageVO<ProcInstMonitorListVo>> listProcInstPage(@RequestParam(value = "businessKey", required = false) @ApiParam(value = "业务key") String businessKey,
                                                                    @RequestParam(value = "processInstanceId", required = false) @ApiParam(value = "流程ID") String processInstanceId,
                                                                    @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
                                                                    @RequestParam(value = "processInstanceName", required = false) @ApiParam(value = "流程实例名称") String processInstanceName,
                                                                    @RequestParam(value = "startUserName", required = false) @ApiParam(value = "发起人用户邮箱前缀") String startUserName,
                                                                    @RequestParam(value = "status", required = false) @ApiParam(value = "流程状态") String status,
                                                                    @RequestParam(value = "createTimeStart", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String createTimeStart,
                                                                    @RequestParam(value = "createTimeEnd", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String createTimeEnd,
                                                                    @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                                    @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {

        ProcInstMonitorQueryReqDto processInstanceQueryReqDto = ProcInstMonitorQueryReqDto.builder()
                .processInstanceName(processInstanceName)
                .processInstanceId(processInstanceId)
                .modelCode(modelCode)
                .businessKey(businessKey)
                .status(StringUtils.isNotBlank(status) ? Arrays.asList(ProcessInstanceStatus.valueOf(status)) : null)
                .startUserName(startUserName)
                .createTimeStart(createTimeStart)
                .createTimeEnd(createTimeEnd)
                .build();

        PageModel<HistoricProcInstResp> historicProcInstPage = procInstMonitorService.queryProcessInstanceListPage(processInstanceQueryReqDto, pageNum, pageSize);
        return BaseResp.success(PageVO.build(procInstVoConverter.toProcInstMonitorListVo(historicProcInstPage.getList()), historicProcInstPage.getPageSize(),
                historicProcInstPage.getPageNum(), historicProcInstPage.getTotal()));
    }

    @ApiOperation("数据导出查看流程列表")
    @PostMapping("/process/export-list")
    public BaseResp<PageVO<ProcInstMonitorListVo>> exportProcessInstances(@RequestBody ProcInstMonitorQueryReqDto req) {

        PageModel<HistoricProcInstResp> historicProcInstPage = procInstMonitorService.queryProcessInstanceListPage(req, req.getPageNum(), req.getPageSize());
        return BaseResp.success(PageVO.build(procInstVoConverter.toProcInstMonitorListVo(historicProcInstPage.getList()), historicProcInstPage.getPageSize(),
                historicProcInstPage.getPageNum(), historicProcInstPage.getTotal()));
    }

    @ApiOperation("流程单详情")
    @GetMapping("/detail/{processInsId}")
    public BaseResp<ProcInstMonitorVo> detail(@PathVariable("processInsId") String processInsId) {
        ProcessInstanceMonitorResp processInstanceResp = procInstMonitorService.queryHistoricProcInst(processInsId);
        return BaseResp.success(procInstVoConverter.toProcInstMonitorVo(processInstanceResp));
    }

    @ApiOperation("流程单详情")
    @GetMapping("/detail/executions/{processInsId}")
    public BaseResp<ProcInstMonitorVo> detailWithExecutions(@PathVariable("processInsId") String processInsId) {
        ProcessInstanceMonitorResp processInstanceResp = procInstMonitorService.queryRuntimeProcInstAndExecutions(processInsId);
        return BaseResp.success(procInstVoConverter.toProcInstMonitorVo(processInstanceResp));
    }

    @ApiOperation("修改任务处理人")
    @PutMapping("/task/assignees")
    public BaseResp<Void> updateAssignee(@RequestBody UpdateAssigneeListReq updateAssigneeReq) {
        procInstMonitorService.setAssignees(updateAssigneeReq);
        return BaseResp.success();
    }

    @ApiOperation("加载流程变量")
    @GetMapping("/variables/{proInstanceId}")
    public BaseResp<List<ProcInsVariablesMonitorVo>> loadProcessVariables(@PathVariable("proInstanceId") String proInstanceId) {
        List<ProcInsVariablesMonitorResp> procInsVariablesMonitorResp = procInstMonitorService.queryInstanceVariables(proInstanceId);
        return BaseResp.success(procInstMonitorVoConverter.toProcInsVariablesMonitorVoList(procInsVariablesMonitorResp));
    }

    @ApiOperation("修改流程变量")
    @PutMapping("/variable")
    public BaseResp<Void> updateVariables(@RequestBody UpdateVariablesReq updateVariablesReq) {
        procInstMonitorService.updateInstanceVariables(updateVariablesReq);
        return BaseResp.success();
    }

    @ApiOperation("查询任务列表")
    @GetMapping("/tasks")
    public BaseResp<PageVO<TaskMonitorVo>> listTaskPage(@RequestParam(value = "assigneeName", required = false) @ApiParam(value = "处理人Name") String assigneeName,
                                                        @RequestParam(value = "taskId", required = false) @ApiParam(value = "任务实例ID") String taskId,
                                                        @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
                                                        @RequestParam(value = "finished", required = false) @ApiParam(value = "任务状态") Boolean finished,
                                                        @RequestParam(value = "taskCreateTimeStart", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTimeStart,
                                                        @RequestParam(value = "taskCreateTimeEnd", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTimeEnd,
                                                        @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                        @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        TaskMonitorQueryReqDto taskMonitorQueryReqDto = TaskMonitorQueryReqDto.builder()
                .currentUserName(assigneeName)
                .taskId(taskId)
                .modelCode(modelCode)
                .finished(finished)
                .taskCreateTimeStart(taskCreateTimeStart)
                .taskCreateTimeEnd(taskCreateTimeEnd)
                .byTaskCreateTimeAsc(false)
                .build();
        PageModel<TaskInstanceResp> taskInstanceRespPageModel = procInstMonitorService.queryTaskListPage(taskMonitorQueryReqDto, pageNum, pageSize);
        return BaseResp.success(PageVO.build(procInstMonitorVoConverter.toTaskMonitorVoList(taskInstanceRespPageModel.getList()), taskInstanceRespPageModel.getPageSize(),
                taskInstanceRespPageModel.getPageNum(), taskInstanceRespPageModel.getTotal()));
    }

    @ApiOperation("管理员终止流程实例")
    @PostMapping("/terminate")
    public BaseResp<Void> terminate(@Valid @RequestBody AdminTerminateProcInstReq terminateProcInstReq) {
        procInstMonitorService.terminateProcessInstance(terminateProcInstReq);
        return BaseResp.success();
    }

    @ApiOperation("导出审批记录")
    @PostMapping("/proc-insts/export")
    public BaseResp<Void> exportProcessInstances(@RequestBody ProcessInstanceExportReq req) {
        String user = IdentityUtil.currentUserName();
        log.info("导出审批记录 用户={}", user);
        historyProcessInstanceService.exportProcessInstance(req);
        log.info("开始 导出审批记录 用户={}", user);
        return BaseResp.success();
    }

    @ApiOperation("导出流程表单数据")
    @PostMapping("/proc-insts/form/export")
    public BaseResp<Void> exportProcessInstancesAndForm(@RequestBody ProcessInstanceExportReq req) {
        String user = IdentityUtil.currentUserName();
        log.info("导出表单数据 用户={}", user);
        historyProcessInstanceService.exportProcessInstanceAndForm(req);
        log.info("开始 导出表单数据 用户={}", user);
        return BaseResp.success();
    }
}
