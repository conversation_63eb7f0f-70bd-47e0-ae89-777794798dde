package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/15 16:15
 */
@Data
public class ApplyPermissionReqVo {
    /**
     * 申请权限用户名
     */
    private String userOrpid;

    /**
     * 申请权限类型：DEPT_TYPE-部门权限，USER_TYPE-人员权限
     */
    private PermissionIdType type;

    /**
     * 申请直属下级
     */
    private String userPermissionId;

    /**
     * 申请部门权限
     */
    private List<String> deptPermissionId;

    /**
     * 申请理由
     */
    private String reason;

}
