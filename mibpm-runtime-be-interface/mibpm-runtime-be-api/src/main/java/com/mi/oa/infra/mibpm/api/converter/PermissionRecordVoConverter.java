package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.mibpm.api.model.vo.PermissionRecordReqVo;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.PermissionRecordsDto;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/18 11:07
 */
@Mapper(componentModel = "spring")
public interface PermissionRecordVoConverter {
    List<PermissionRecordReqVo> dtoToVo(List<PermissionRecordsDto> dto);
}
