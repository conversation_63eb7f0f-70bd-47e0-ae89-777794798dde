package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/16 16:16
 * @Version 1.0
 */
@Data
@ApiModel("导出权限操作记录")
public class PermissionRecordReqVo {

    /**
     * 操作人详细信息
     */
    private BpmUser createUser;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 清单Id
     */
    private String businessKey;

    /**
     * 变动时间
     */
    private ZonedDateTime createTime;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限Id
     */
    private String permissionId;

}
