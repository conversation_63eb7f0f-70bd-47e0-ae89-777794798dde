package com.mi.oa.infra.mibpm.api.model.vo.v3;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/5 15:09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "发起流程实例响应", description = "发起流程实例响应")
public class OpenCreateProcInstRespV3Vo {

    /**
     * 流程实例业务编码
     */
    @ApiModelProperty(value = "流程实例业务侧编码")
    protected String businessKey;
    @ApiModelProperty(value = "流程实例ID")
    protected String processInstanceId;
}