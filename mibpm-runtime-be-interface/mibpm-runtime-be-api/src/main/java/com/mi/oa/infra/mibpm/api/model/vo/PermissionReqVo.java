package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/16 16:16
 * @Version 1.0
 */

@Data
@ApiModel("导出已获得的权限")
public class PermissionReqVo {
    /**
     * 权限Id
     */
    private String permissionId;

    /**
     * 权限名
     */
    private String permissionName;

    /**
     * 权限类型
     */
    private PermissionIdType permissionIdType;

}
