package com.mi.oa.infra.mibpm.api.web.openapiv3;

import com.mi.oa.infra.mibpm.application.message.dto.req.PermissionCallBackReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.PermissionCallBackBaseReq;
import com.mi.oa.infra.mibpm.application.userconfig.converter.ApplyDeptPermissionReqConverter;
import com.mi.oa.infra.mibpm.application.userconfig.converter.ApplyUserPermissionReqConverter;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DeptPermissionDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserPermissionDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.PermissionOperationRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/23 14:06
 */
@Api(tags = "权限申请的回调")
@RestController
@RequestMapping("/api/v1/permission-callback")
public class PermissionCallBackController {
    @Autowired
    PermissionOperationRepository permissionOperationRepository;
    @Autowired
    ApplyUserPermissionReqConverter applyUserPermissionReqConverter;
    @Autowired
    ApplyDeptPermissionReqConverter applyDeptPermissionReqConverter;
    @Autowired
    HistoricTaskRepository historicTaskRepository;
    @Autowired
    ProcessInstanceDomainService processInstanceDomainService;

    @ApiOperation("添加权限")
    @PostMapping("/addPermission")
    public BaseResp<Void> addPermission(@RequestBody PermissionCallBackBaseReq<PermissionCallBackReq> req) {
        PermissionCallBackReq formData = req.getFormData();
        if (req.getFormData().getUserOrpid() == null || req.getFormData().getUserOrpid().isEmpty()) {
            req.getFormData().setUserOrpid(IdentityUtil.currentUserName());
        }
        //获取流程的第一个节点的任务id
        ProcessInstanceDo processInstanceDo = processInstanceDomainService.queryProcessInstanceByBusinessKey(req.getBusinessKey());
        List<TaskDo> taskDos = historicTaskRepository.queryHistoricTasksByProcInstId(processInstanceDo.getProcessInstanceId());
        req.setTaskId(taskDos.get(0).getTaskId());
        if (formData.getType() == PermissionIdType.USER_TYPE) {
            String userPermissionId = formData.getUserPermissionId();
            UserPermissionDo userPermissionDo = applyUserPermissionReqConverter.dtoToDo(formData);
            userPermissionDo.setPermissionUserId(userPermissionId);
            if (req.getStartUser() != null) {
                String createUser = req.getStartUser().getUserName();
                userPermissionDo.setCreateUser(createUser);
                permissionOperationRepository.addPermissionAssignee(userPermissionDo);
                permissionOperationRepository.addApplyRecord(formData.getUserOrpid(), userPermissionId, req.getBusinessKey(),
                        req.getTaskId(), PermissionIdType.USER_TYPE, createUser);
            }
        } else if (formData.getType() == PermissionIdType.DEPT_TYPE) {
            List<String> deptPermissionIds = formData.getDeptPermissionId();
            for (String deptPermissionId : deptPermissionIds) {
                DeptPermissionDo deptPermissionDo = applyDeptPermissionReqConverter.dtoToDo(formData);
                deptPermissionDo.setDeptId(deptPermissionId);
                deptPermissionDo.setFilter("待填");
                if (req.getStartUser() != null) {
                    String createUser = req.getStartUser().getUserName();
                    deptPermissionDo.setCreateUser(createUser);
                    permissionOperationRepository.addPermissionDept(deptPermissionDo);
                    permissionOperationRepository.addApplyRecord(formData.getUserOrpid(), deptPermissionId, req.getBusinessKey(),
                            req.getTaskId(), PermissionIdType.DEPT_TYPE, createUser);
                }
            }
        }
        return BaseResp.success();
    }
}
