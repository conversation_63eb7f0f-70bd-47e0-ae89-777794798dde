package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.converter.TaskInstVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.HistoricTaskVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskVo;
import com.mi.oa.infra.mibpm.application.task.dto.req.Page;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskQueryPageReq;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskInstanceResp;
import com.mi.oa.infra.mibpm.application.task.service.HrTaskService;
import com.mi.oa.infra.mibpm.application.task.service.TaskService;
import com.mi.oa.infra.mibpm.application.task.service.TodoCenterService;
import com.mi.oa.infra.mibpm.common.constant.HrCodeConstants;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@WebLog
@RestController
@RequestMapping("/api/v2/tasks")
@Api(tags = "任务查询(MiTask)")
public class MiTaskQueryController {

    @Autowired
    private TaskService taskInstanceService;
    @Autowired
    private TaskInstVoConverter taskInstVoConverter;
    @Autowired
    private TodoCenterService todoCenterService;
    @Autowired
    private HrTaskService hrTaskService;

    @ApiOperation("查询待办任务列表")
    @GetMapping("/todo")
    public BaseResp<PageVO<TaskVo>> listTodoTaskPage(@RequestParam(value = "categoryCode", required = false) @ApiParam(value = "分类编码") String categoryCode,
                                                     @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
                                                     @RequestParam(value = "processInstanceName", required = false) @ApiParam(value = "流程实例名称") String processInstanceName,
                                                     @RequestParam(value = "userName", required = false) @ApiParam(value = "用户邮箱前缀") String userName,
                                                     @RequestParam(value = "taskCreateTime", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTime,
                                                     @RequestParam(value = "byTaskCreateTimeAsc", required = false) @ApiParam(value = "按任务到达时间正序排列") Boolean byTaskCreateTimeAsc,
                                                     @RequestParam(value = "byTaskDueDateTimeAsc", required = false) @ApiParam(value = "按任务任务过期时间正序排列") Boolean byTaskDueDateTimeAsc,
                                                     @RequestParam(value = "startTimeBegin", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTimeStart,
                                                     @RequestParam(value = "startTimeEnd", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTimeEnd,
                                                     @RequestParam(value = "statusList", required = false) @ApiParam(value = "流程状态") List<ProcessInstanceStatus> statusList,
                                                     @RequestParam(value = "clientType", required = false) @ApiParam(value = "客户端类型") String client,
                                                     @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                     @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        ClientEnum clientEnum = ClientEnum.findByCode(client);
        TaskQueryPageReq req = TaskQueryPageReq.builder().currentUserId(IdentityUtil.currentUid())
                .currentUserName(IdentityUtil.currentUserName())
                .taskCreateTime(taskCreateTime)
                .categoryCode(categoryCode)
                .modelCode(modelCode)
                .processInstanceName(processInstanceName)
                .taskCreateTimeStart(taskCreateTimeStart)
                .taskCreateTimeEnd(taskCreateTimeEnd)
                .statusList(statusList)
                .userName(userName)
                .byTaskCreateTimeAsc(byTaskCreateTimeAsc)
                .byTaskDueDateAsc(byTaskDueDateTimeAsc)
                .page(new Page(pageNum, pageSize))
                .clientType(clientEnum)
                .newQueryFlag(true)
                .build();
        PageVO<TaskInstanceResp> resp;
        if (StringUtils.isNotBlank(modelCode) && ClientEnum.MINI_APP.equals(
                clientEnum) && HrCodeConstants.HR_CODE_LIST.contains(modelCode)) {
            req.setModelCode(categoryCode);
            resp = hrTaskService.waitTaskList(req, pageNum, pageSize);
        } else if ((StringUtils.isNotBlank(categoryCode) && isExternalBpmType(categoryCode))
                || StringUtils.isNotBlank(modelCode) && isExternalBpmType(modelCode)) {
            resp = todoCenterService.waitTaskList(req, pageNum, pageSize);
        } else {
            resp = taskInstanceService.queryTodoTaskPage(req, pageNum, pageSize);
        }
        List<TaskVo> taskListVos = taskInstVoConverter.dtoToList(resp.getList());
        return BaseResp.success(PageVO.build(taskListVos, resp.getPageSize(), resp.getPageNum(), resp.getTotal()));
    }

    @ApiOperation("查询已办任务列表")
    @GetMapping("/reviewed")
    public BaseResp<PageVO<HistoricTaskVo>> listReviewedTaskPage(@RequestParam(value = "categoryCode", required = false) @ApiParam(value = "分类编码") String categoryCode,
                                                                 @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
                                                                 @RequestParam(value = "processInstanceName", required = false) @ApiParam(value = "流程实例名称") String processInstanceName,
                                                                 @RequestParam(value = "userName", required = false) @ApiParam(value = "用户邮箱前缀") String userName,
                                                                 @RequestParam(value = "taskCreateTime", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTime,
                                                                 @RequestParam(value = "startTimeBegin", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTimeStart,
                                                                 @RequestParam(value = "startTimeEnd", required = false) @ApiParam(value = "任务到达时间（unix毫秒时间戳）") String taskCreateTimeEnd,
                                                                 @RequestParam(value = "byTaskCreateTimeAsc", required = false) @ApiParam(value = "按任务到达时间正序排列") Boolean byTaskCreateTimeAsc,
                                                                 @RequestParam(value = "byTaskEndTimeAsc", required = false) @ApiParam(value = "按任务到达时间正序排列") Boolean byTaskEndTimeAsc,
                                                                 @RequestParam(value = "byTaskDueDateAsc", required = false) @ApiParam(value = "按任务过期时间正序排列") Boolean byTaskDueDateAsc,
                                                                 @RequestParam(value = "statusList", required = false) @ApiParam(value = "流程状态") List<ProcessInstanceStatus> statusList,
                                                                 @RequestParam(value = "clientType", required = false) @ApiParam(value = "客户端类型") String client,
                                                                 @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                                 @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        ClientEnum clientEnum = ClientEnum.findByCode(client);
        TaskQueryPageReq req = TaskQueryPageReq.builder().currentUserId(IdentityUtil.currentUid())
                .currentUserName(IdentityUtil.currentUserName())
                .taskCreateTime(taskCreateTime)
                .categoryCode(categoryCode)
                .modelCode(modelCode)
                .processInstanceName(processInstanceName)
                .userName(userName)
                .taskCreateTimeStart(taskCreateTimeStart)
                .taskCreateTimeEnd(taskCreateTimeEnd)
                .statusList(statusList)
                .byTaskEndTimeAsc(byTaskEndTimeAsc)
                .byTaskCreateTimeAsc(byTaskCreateTimeAsc)
                .byTaskDueDateAsc(byTaskDueDateAsc)
                .page(new Page(pageNum, pageSize))
                .clientType(clientEnum)
                .newQueryFlag(true)
                .build();
        PageVO<TaskInstanceResp> resp;
        if (StringUtils.isNotBlank(modelCode) && ClientEnum.MINI_APP.equals(
                clientEnum) && HrCodeConstants.HR_CODE_LIST.contains(modelCode)) {
            resp = hrTaskService.historyTaskList(req, pageNum, pageSize);
        } else if ((StringUtils.isNotBlank(categoryCode) && isExternalBpmType(categoryCode))
                || StringUtils.isNotBlank(modelCode) && isExternalBpmType(modelCode)) {
            resp = todoCenterService.historyTaskList(req, pageNum, pageSize);
        } else {
            resp = taskInstanceService.queryReviewedTaskPage(req, pageNum, pageSize);
        }
        List<HistoricTaskVo> taskListVos = taskInstVoConverter.dtoToHistoricList(resp.getList());
        return BaseResp.success(PageVO.build(taskListVos, resp.getPageSize(), resp.getPageNum(), resp.getTotal()));
    }

    private boolean isExternalBpmType(String bpmType) {
        return bpmType.startsWith("ext_");
    }
}
