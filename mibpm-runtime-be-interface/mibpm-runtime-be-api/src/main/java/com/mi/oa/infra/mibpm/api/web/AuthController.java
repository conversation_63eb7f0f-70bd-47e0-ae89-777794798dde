package com.mi.oa.infra.mibpm.api.web;

import com.google.common.collect.Lists;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.newauth.autoconfig.token.JwtTokenProperties;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description
 * @date 2022/4/19 8:34 PM
 **/
@RestController
@RequestMapping("/api/v1/auth")
@Slf4j
public class AuthController {

    @Value("${bpm.auth-link}")
    private String authLink;

    @GetMapping("getAccessToken")
    public BaseResp<String> getAccessToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String token = getToken(request);
        OkHttpClient okHttpClient = new OkHttpClient();
        Request okHttpRequest = new Request.Builder().url(authLink)
                .addHeader("bpm-auth", "Bearer " + token).build();
        Response execute = okHttpClient.newCall(okHttpRequest).execute();
        Headers headers = execute.headers();
        String cookie = headers.get("Set-Cookie");
        response.setHeader("Set-Cookie", parseSetCookie(cookie));
        return BaseResp.success();
    }


    private String parseSetCookie(String s) {
        if (StringUtils.isBlank(s)) {
            return "";
        }
        String[] headers = s.split("; ");
        if (headers.length > 0) {
            String session = headers[0];
            ArrayList<String> objects = Lists.newArrayList(session, "Domain=mioffice.cn", "Path=/mibpm/",
                    "SameSite=None",
                    "Secure");
            return StringUtils.join(objects, "; ");
        }
        return null;
    }

    public String getToken(HttpServletRequest request) {
        if (null == request) {
            return null;
        }
        String authHeader = request.getHeader(JwtTokenProperties.AUTH_HEADER_NAME);
        if (authHeader != null && authHeader.startsWith(JwtTokenProperties.BEARER_PREFIX)) {
            return authHeader.substring(7);
        }
        Cookie[] cookies = request.getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(JwtTokenProperties.TOKEN_NAME)) {
                    return cookie.getValue();
                }
            }
        }

        String token = request.getParameter(JwtTokenProperties.TOKEN_NAME);
        if (token != null) {
            return token;
        }
        return null;
    }
}
