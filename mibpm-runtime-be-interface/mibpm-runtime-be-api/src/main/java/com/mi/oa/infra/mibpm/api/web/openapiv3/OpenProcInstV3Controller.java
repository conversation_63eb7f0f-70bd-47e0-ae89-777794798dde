package com.mi.oa.infra.mibpm.api.web.openapiv3;

import com.mi.oa.infra.mibpm.api.converter.ProcInstVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.v3.OpenCreateProcInstRespV3Vo;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenCreateProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenCreateProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.service.OpenProcessInstanceService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/5/5 14:50
 */
@Slf4j
@Api(tags = "Open API 流程实例-V3")
@WebLog
@RestController
@RequestMapping("/openapi/v3/proc-insts")
public class OpenProcInstV3Controller {

    @Autowired
    private OpenProcessInstanceService openProcessInstanceService;
    @Autowired
    private ProcInstVoConverter procInstVoConverter;

    @ApiOperation("发起流程实例")
    @PostMapping("/create")
    public BaseResp<OpenCreateProcInstRespV3Vo> create(@Valid @RequestBody OpenCreateProcInstReq createProcInstReq) {
        log.info("发起流程实例, request = {}", GsonUtils.toJsonWtihNullField(createProcInstReq));
        OpenCreateProcInstResp resp = openProcessInstanceService.startProcessInstance(createProcInstReq);
        OpenCreateProcInstRespV3Vo vo = procInstVoConverter.dtoToVo3(resp);
        return BaseResp.success(vo);
    }

}
