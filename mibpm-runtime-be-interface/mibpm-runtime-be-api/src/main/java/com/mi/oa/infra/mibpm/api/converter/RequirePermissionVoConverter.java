package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.mibpm.api.model.vo.PermissionReqVo;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RequirePermissionDto;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 18:51
 */
@Mapper(componentModel = "spring")
public interface RequirePermissionVoConverter {
    List<PermissionReqVo> dtoToVo(List<RequirePermissionDto> dto);
}
