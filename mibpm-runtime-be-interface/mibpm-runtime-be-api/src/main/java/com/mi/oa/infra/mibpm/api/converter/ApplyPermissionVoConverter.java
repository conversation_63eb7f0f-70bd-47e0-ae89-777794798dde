package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.mibpm.api.model.vo.ApplyPermissionReqVo;
import com.mi.oa.infra.mibpm.application.message.dto.req.ApplyPermissionReq;
import org.mapstruct.Mapper;

/**
 * @<PERSON> lix<PERSON>han
 * @Date 2024/7/19 9:33
 */
@Mapper(componentModel = "spring")
public interface ApplyPermissionVoConverter {
    ApplyPermissionReq voToDto(ApplyPermissionReqVo applyPermissionReqVo);
}
