package com.mi.oa.infra.mibpm.api.web;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.gson.Gson;
import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.api.converter.DelegationVoConverter;
import com.mi.oa.infra.mibpm.application.delegation.dto.ApplyDelegationDTO;
import com.mi.oa.infra.mibpm.application.delegation.service.DelegationService;
import com.mi.oa.infra.mibpm.application.mitask.service.MiTaskExternalService;
import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.service.ApprovalService;
import com.mi.oa.infra.mibpm.common.constant.LarkMessageConstants;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.DelegationSourceEnum;
import com.mi.oa.infra.mibpm.common.enums.DelegationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.LeaveDelegationInfo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.common.model.CardBot;
import com.mi.oa.infra.mibpm.domain.task.errorcode.TaskDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.service.UserConfigDomainService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.utils.HttpClientUtils;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/22 7:40 PM
 **/
@RestController
@WebLog
@Slf4j
@RequestMapping("/api/v1/lark")
public class LarkCallbackController {

    @Value("${mibpm.lark.callback.url}")
    private String mibpmUrl;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private UserConfigDomainService userConfigDomainService;
    @Autowired
    private DelegationService delegationService;
    @Autowired
    private DelegationVoConverter delegationVoConverter;
    @Autowired
    private MiTaskExternalService miTaskExternalService;

    /**
     * 飞书校验字段
     */
    private static final String LARK_VALIDATE = "challenge";

    @ResponseBody
    @RequestMapping(value = "/notify/cardEvent", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @WebLog
    public Object larkEventCallBack(@RequestBody Map<String, Object> params, HttpServletRequest request)
            throws IOException {
        log.info("lark card call back notify: {}", GsonUtil.toJsonString(params));

        //飞书校验逻辑，当含有challenge字段时 原样返回该值 证明该接口有效
        if (params.containsKey(LARK_VALIDATE)) {
            Map<String, String> res = new HashMap<>(1);
            res.put(LARK_VALIDATE, (String) params.getOrDefault(LARK_VALIDATE, ""));
            return res;
        }
        Map<String, Object> action = (Map<String, Object>) params.get("action");
        Map<String, String> value = (Map<String, String>) action.get("value");
        String cardBotBOJson = value.get("cardBotBO");
        String actionKey = value.get("actionKey");
        if (StringUtils.isNotBlank(cardBotBOJson)) {
            CardBot cardBotBO = GsonUtils.fromJson(cardBotBOJson, CardBot.class);
            if (LarkMessageConstants.CARD_SOURCE_EXT.equals(cardBotBO.getSource())) {
                return miTaskExternalService.doExtCallBackHandler(cardBotBO, mibpmUrl, actionKey, params);
            } else {
                return HttpClientUtils.doPostJson(mibpmUrl, params);
            }
        } else {
            SimpleModule module = new SimpleModule();
            module.addDeserializer(I18n.class, new JsonDeserializer<I18n>() {
                @Override
                public I18n deserialize(JsonParser p, DeserializationContext ctxt)
                        throws IOException, JsonProcessingException {
                    TreeNode treeNode = p.getCodec().readTree(p);
                    String zhCn = ((TextNode) treeNode.get("zhCn")).asText();
                    String enUs = ((TextNode) treeNode.get("enUs")).asText();
                    String jaJp = ((TextNode) treeNode.get("jaJp")).asText();
                    return new I18n(zhCn, enUs, jaJp);
                }
            });
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.registerModule(module);
            LarkMessageDo larkMessageDo = objectMapper.readValue(value.get("larkMessageDo"), LarkMessageDo.class);

            switch (larkMessageDo.getEventType()) {
                case LEAVE_DELEGATION:
                    return leaveDelegationHandler(larkMessageDo, action);
                case TASK_VIEW_AUTH_APPLY:
                    // 处理任务权限查看申请
                    return taskViewAuthApplyHandler(larkMessageDo, actionKey);
                default:
                    return handler(larkMessageDo);
            }
        }
    }

    private Object taskViewAuthApplyHandler(LarkMessageDo larkMessageDo, String actionKey) {

        String contextData = larkMessageDo.getContextJsonData();

        if (LarkMessageConstants.APPROVED.equals(actionKey)) {
            ProcessViewAuthConfig processViewAuthConfig =
                    GsonUtil.parseObject(contextData, ProcessViewAuthConfig.class);
            if (Objects.nonNull(processViewAuthConfig)) {
                userConfigDomainService.saveProcessViewAuth(processViewAuthConfig);
            }
        }

        Actions done = new Actions();
        done.setActionKey(LarkMessageConstants.DONE);

        List<Actions> actions = Arrays.asList(done);
        larkMessageDo.setActions(actions);
        return larkMessageDomainService.buildLarkMessageCard(larkMessageDo).toObjectForJson();
    }

    private Object handler(LarkMessageDo larkMessageDo) {
        CompleteTaskReq req = new CompleteTaskReq();
        BpmUser user = accountRemoteService.getUser(larkMessageDo.getUsername());
        req.setOperator(user);
        req.setTaskId(larkMessageDo.getTaskId());
        req.setFastApproval(true);
        req.setClient(ClientEnum.MINI_APP);
        try {
            approvalService.approve(req);
        } catch (DomainException e) {
            // 任务已处理的不需要抛异常
            if (e.getErrCode().equals(TaskDomainErrorCodeEnum.TASK_NOT_EXISTS)) {
                log.info("任务已处理 taskId={}", larkMessageDo.getTaskId());
            } else {
                throw e;
            }
        }
        Actions done = new Actions();
        done.setActionKey(LarkMessageConstants.DONE);

        List<Actions> actions = Arrays.asList(done);
        larkMessageDo.setActions(actions);
        return larkMessageDomainService.buildLarkMessageCard(larkMessageDo).toObjectForJson();
    }

    private Object leaveDelegationHandler(LarkMessageDo larkMessageDo, Map<String, Object> action) {
        Actions done = new Actions();
        done.setActionKey(LarkMessageConstants.DONE);
        done.setActionName("已处理");

        if (action.get("url") == null || !StringUtils.isNotBlank((String) action.get("url"))) {
            try {
                LeaveDelegationInfo leaveDelegationInfo = new Gson().fromJson(larkMessageDo.getContextJsonData(),
                        LeaveDelegationInfo.class);
                ApplyDelegationDTO applyDelegationDTO = delegationVoConverter.dtoToDto(leaveDelegationInfo);
                applyDelegationDTO.setDelegationType(DelegationTypeEnum.ALL);
                applyDelegationDTO.setDelegationSourceEnum(DelegationSourceEnum.LEAVE_DELEGATION);
                delegationService.applyDelegation(applyDelegationDTO, true);
                larkMessageDo.setTemplateColor(TemplateColor.GREEN);
                larkMessageDo.setTitle(new I18n("请假委托代批提醒[委托成功]", "Leave Delegation Approval Reminder[Delegation " +
                        "Success]", ""));
                larkMessageDo.setEventType(EventIdentify.LEAVE_DELEGATION_SUCCESS);
            } catch (Exception e) {
                log.error("Error processing leave delegation", e);
                larkMessageDo.setTitle(new I18n("请假委托代批提醒[委托失败！请再次尝试]", "Leave Delegation Approval Reminder " +
                        "[Delegation Failed! Please Try Again]", ""));
                larkMessageDo.setTemplateColor(TemplateColor.RED);
                return larkMessageDomainService.buildLarkMessageCard(larkMessageDo).toObjectForJson();
            }
        }

        List<Actions> actions = Arrays.asList(done);
        larkMessageDo.setActions(actions);

        return larkMessageDomainService.buildLarkMessageCard(larkMessageDo).toObjectForJson();
    }

}
