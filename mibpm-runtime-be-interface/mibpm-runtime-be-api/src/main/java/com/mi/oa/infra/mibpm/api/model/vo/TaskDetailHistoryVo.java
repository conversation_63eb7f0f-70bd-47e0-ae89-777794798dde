package com.mi.oa.infra.mibpm.api.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/7 11:26
 **/
@Data
public class TaskDetailHistoryVo {
    @ApiModelProperty("流程实例id")
    private String processInstanceId;
    private ProcessInstanceStatus processInstanceStatus;
    private String modelCode;
    @ApiModelProperty("任务节点列表")
    private List<FlowNodeVo> nodeList;

    @Data
    public static class FlowNodeVo {
        @ApiModelProperty("任务定义key")
        private String nodeId;
        @ApiModelProperty("任务名称")
        private String name;
        @ApiModelProperty("审批方式")
        private UserTaskSignType signType;
        @ApiModelProperty("任务列表")
        private List<TaskInstanceVo> taskList;
        @ApiModelProperty("是否预测任务")
        @JsonProperty("isPredict")
        private boolean isPredict;
    }
}
