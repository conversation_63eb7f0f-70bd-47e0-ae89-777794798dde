package com.mi.oa.infra.mibpm.api.web.openapi;

import com.mi.oa.infra.form.core.api.dto.CreateFormInstReq;
import com.mi.oa.infra.form.core.api.dto.FormInstResp;
import com.mi.oa.infra.form.core.api.remote.FormInstService;
import com.mi.oa.infra.mibpm.api.converter.OpenFormInstRespConverter;
import com.mi.oa.infra.mibpm.api.converter.OpenSaveFormInstReqConverter;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenFormInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenSaveFormInstReq;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:52
 */
@Slf4j
@WebLog
@Api(tags = "Open API 表单实例")
@RestController
@RequestMapping("/openapi/v2/forms")
public class OpenFormInstController {

    @Autowired
    private FormInstService formInstService;
    @Autowired
    private OpenSaveFormInstReqConverter openSaveFormInstReqConverter;
    @Autowired
    private OpenFormInstRespConverter openFormInstRespConverter;

    @ApiOperation("保存表单实例")
    @PostMapping(value = "/instances")
    public BaseResp<OpenFormInstResp> createFormInst(@RequestBody OpenSaveFormInstReq openSaveFormInstReq) {
        log.info("保存表单实例, openSaveFormInstReq = {}", GsonUtils.toJsonWtihNullField(openSaveFormInstReq));
        CreateFormInstReq createFormInstReq = openSaveFormInstReqConverter.dtoToDto(openSaveFormInstReq);
        BaseResp<FormInstResp> resp = formInstService.createFormInst(createFormInstReq);
        if (resp.getCode() == 0) {
            return BaseResp.success(openFormInstRespConverter.dtoToDto(resp.getData()));
        }
        return BaseResp.error(resp.getCode(), resp.getMessage());
    }

    @ApiOperation("查看表单实例")
    @GetMapping(value = "/instances")
    public BaseResp<OpenFormInstResp> queryFormInst(@RequestParam(value = "businessKey") String businessKey,
                                                    @RequestParam(value = "taskDefinitionKey", required = false) String taskDefinitionKey) {
        BaseResp<FormInstResp> resp = formInstService.queryFormInst(businessKey, taskDefinitionKey);
        if (resp.getCode() == 0) {
            return BaseResp.success(openFormInstRespConverter.dtoToDto(resp.getData()));
        }
        return BaseResp.error(resp.getCode(), resp.getMessage());
    }
}
