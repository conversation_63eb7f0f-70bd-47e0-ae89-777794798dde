package com.mi.oa.infra.mibpm.api.common;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Operator;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/3/15 14:19
 */
@Aspect
@Component
public class LoginUserAspect {

    @Pointcut("@annotation(LoginUser)")
    public void loginUserPointCut() {

    }

    @Before(value = "loginUserPointCut()")
    public void doBefore(JoinPoint joinPoint) {
        //获取传入目标方法的参数
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (Operator.class.isAssignableFrom(arg.getClass())) {
                Operator operator = (Operator) arg;
                operator.setOperator(getLoginUser());
            }
        }
    }

    private BpmUser getLoginUser() {
        String uid = IdentityUtil.currentUid();
        return ((AccountRemoteService) SpringContextUtil.getBean(AccountRemoteService.class)).getUser(uid);
    }
}
