spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: bpm_w
    password@kc-sid: oa-infra.g
    password: GDBAA+w83voS47nSDKyxVu8u6kemZ0F8EWlbbde+rDJ9tefDhLtmoOuaWJRecAorMKAYEmI99y+/EURVrXf4GbmPtjeD/xgQUWBv1G16Qx2KlVSLnzpycBgUIxX9iYIGV8hocLdF+r5Wi5UgyCEA


#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ********************************************************************************************************************************************************************************************
#    username: mibpm_dev_wn
#    password@kc-sid: oa-infra.g
#    password: GDD4qGk/2S/5HA6h9sQBxDDkOvN626IouW/TMcT1vJ5Fg+0r2I/4cOcXZgPCWHDneDoYEuUVvJxyyE3Yg8RK3Nc/W5by/xgQsXVtoSrWQPaoz0ari0pJihgUc+1FPRBkuIhXzXFfFDlDfKVP9UIA
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: mibpmHikariCPPool
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

    druid:
      filter:
        stat:
          enabled: false