package com.mi.oa.infra.mibpm.domain.userconfig.service;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.userconfig.ability.UserConfigAbility;
import com.mi.oa.infra.mibpm.domain.userconfig.ability.UserConfigCheckAbility;
import com.mi.oa.infra.mibpm.domain.userconfig.ability.UserConfigFillAbility;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserConfigDo;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:31
 */
@Service
public class UserConfigDomainServiceImpl implements UserConfigDomainService {

    @Autowired
    private UserConfigAbility userConfigAbility;
    @Autowired
    private UserConfigFillAbility userConfigFillAbility;
    @Autowired
    private UserConfigCheckAbility userConfigCheckAbility;
    @Autowired
    private AccountRemoteService accountRemoteService;

    @Override
    public void addDelegation(UserConfigDo userConfigDo) {
        //填充基本信息
        userConfigFillAbility.fillDelegationMetaInfo(userConfigDo);
        // 填充用户信息
        userConfigFillAbility.fillUserInfo(userConfigDo);
        // 创建前检查委托是否合规
        userConfigCheckAbility.checkDelegation(userConfigDo);
        // 创建委托
        userConfigAbility.addDelegation(userConfigDo);
    }

    @Override
    public PageModel<DelegationConfig> queryDelegationPageList(DelegationConfig delegationConfigQuery, int page, int pageSize) {
        // 查询列表
        PageModel<DelegationConfig> delegationConfigPageModel =
                userConfigAbility.queryDelegationDo(delegationConfigQuery, page, pageSize);
        // 更新失效状态
        userConfigAbility.updateInvalidDelegation(delegationConfigPageModel.getList());
        return delegationConfigPageModel;
    }

    @Override
    public String queryDelegationUser(String userName, String modelCode, String initiatorOrgId) {
        BpmUser user = accountRemoteService.getUser(userName);
        if (Objects.isNull(user)) {
            return null;
        }
        return userConfigAbility.queryDelegationUser(user.getUserName(), modelCode, initiatorOrgId);
    }

    @Override
    public void cancelAllDelegationConfig(String userName) {
        userConfigAbility.cancelAllDelegationConfig(userName);
    }

    @Override
    public void transferDelegationByUser(String userName, String newUserName) {
        userConfigAbility.transferDelegationByUser(userName, newUserName);
    }

    @Override
    public UserConfigDo queryAutoNextApproval(String userId) {
        // 构造配置对象
        UserConfigDo userConfigDo = UserConfigDo.builder().build();
        // 填充用户信息
        userConfigFillAbility.fillUserInfo(userConfigDo);
        // 填充连续审批配置
        userConfigFillAbility.fillAutoNextApprovalConfig(userConfigDo);
        return userConfigDo;
    }

    @Override
    public void addMessagePushRule(UserConfigDo userConfigDo) {
        // 填充用户信息
        userConfigFillAbility.fillUserInfo(userConfigDo);
        userConfigAbility.addMessagePushRule(userConfigDo);
    }

    @Override
    public UserConfigDo queryMessagePushRule(String userId) {
        // 构造配置对象
        UserConfigDo userConfigDo = UserConfigDo.builder().userName(userId).build();
        // 填充用户信息
        userConfigFillAbility.fillUserInfo(userConfigDo);
        // 填充消息推送配置
        userConfigFillAbility.fillMessagePushRuleConfig(userConfigDo);
        return userConfigDo;
    }

    @Override
    public void updateUserConfig(UserConfigDo userConfigDo) {
        // 填充用户信息
        userConfigFillAbility.fillUserInfo(userConfigDo);
        // 更新委托配置
        userConfigAbility.updateDelegation(userConfigDo);
        // 更新连续审批配置
        userConfigAbility.updateAutoNextApproval(userConfigDo);
        // 更新消息推送配置
        userConfigAbility.updateMessagePushRule(userConfigDo);
    }

    @Override
    public void saveProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig) {
        userConfigAbility.saveProcessViewAuth(processViewAuthConfig);
    }

    @Override
    public boolean hasProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig) {
        return userConfigAbility.hasProcessViewAuth(processViewAuthConfig);
    }

    @Override
    public List<ProcessViewAuthConfig> queryProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig) {
        return null;
    }

    @Override
    public String getUserDefaultSignature(String userId) {
        return userConfigAbility.getUserDefaultSignature(userId);
    }
}
