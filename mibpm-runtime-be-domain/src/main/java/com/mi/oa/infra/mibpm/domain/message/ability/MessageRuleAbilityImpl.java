package com.mi.oa.infra.mibpm.domain.message.ability;

import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.MessagePushRuleConfig;
import com.mi.oa.infra.mibpm.infra.message.repository.MessageRuleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/16 21:22
 */

@Slf4j
@Service
public class MessageRuleAbilityImpl implements MessageRuleAbility {

    @Autowired
    private MessageRuleRepository messageRuleRepository;

    @Override
    public boolean isSendLarkMessage(LarkMessageDo larkMessageDo) {
        return messageRuleRepository.isSendLarkMessage(larkMessageDo);
    }

    @Override
    public boolean isSendLarkMessage(String userId) {
        return messageRuleRepository.isSendLarkMessage(userId);
    }

    @Override
    public boolean isSendTodoLarkMessage(LarkMessageDo larkMessageDo) {
        return messageRuleRepository.isSendTodoLarkMessage(larkMessageDo);
    }

    @Override
    public boolean isSendApprovalLarkMessage(LarkMessageDo larkMessageDo) {
        return messageRuleRepository.isSendApprovalLarkMessage(larkMessageDo);
    }

    @Override
    public boolean isSendCcTaskLarkMessage(LarkMessageDo larkMessageDo) {
        return messageRuleRepository.isSendCcTaskLarkMessage(larkMessageDo);
    }

    @Override
    public boolean isSendWaitNumLarkMessage(MessagePushRuleConfig messagePushRuleConfig, String time) {
        return messageRuleRepository.isSendWaitNumLarkMessage(messagePushRuleConfig, time);
    }
}
