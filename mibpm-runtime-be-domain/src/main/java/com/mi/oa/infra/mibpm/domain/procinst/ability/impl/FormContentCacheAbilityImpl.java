package com.mi.oa.infra.mibpm.domain.procinst.ability.impl;

import com.mi.oa.infra.mibpm.application.proinst.dto.req.FormStoragReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceCreateReq;
import com.mi.oa.infra.mibpm.domain.procinst.ability.FormContentCacheAbility;
import com.mi.oa.infra.mibpm.infra.procinst.repository.FormContentCacheRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * redis缓存表单内容的相关方方法
 */
@Slf4j
@Component
public class FormContentCacheAbilityImpl implements FormContentCacheAbility {

    private static final long DEFAULT_EXPIRATION_TIME_SECONDS = 30 * 24 * 60 * 60; // 30 days in seconds

    @Autowired
    private FormContentCacheRepository formContentCacheRepository;

    @Override
    public boolean cacheFormContent(FormStoragReq req) {
        return formContentCacheRepository.cacheFormContent(req, DEFAULT_EXPIRATION_TIME_SECONDS);
    }

    @Override
    public Map<String, Object> getCachedFormContent(String modelCode, String userName) {
        return formContentCacheRepository.getCachedFormContent(modelCode, userName);
    }

    @Override
    public boolean removeCachedFormContent(String modelCode, String userId) {
        return formContentCacheRepository.removeCachedFormContent(modelCode, userId);
    }
}
