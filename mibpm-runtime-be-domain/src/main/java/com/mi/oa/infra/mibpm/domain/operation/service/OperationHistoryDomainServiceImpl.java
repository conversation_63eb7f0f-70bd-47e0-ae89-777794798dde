package com.mi.oa.infra.mibpm.domain.operation.service;

import com.mi.oa.infra.mibpm.domain.operation.ability.OperationHistoryAbility;
import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.eventbus.Event;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/14 19:04
 **/
@Service
public class OperationHistoryDomainServiceImpl implements OperationHistoryDomainService {

    @Autowired
    private OperationHistoryAbility operationHistoryAbility;
    @Autowired
    private OperationHistoryFactory operationHistoryFactory;

    @Override
    public void save(OperationHistoryDo operationHistoryDo) {
        operationHistoryAbility.save(operationHistoryDo);
    }

    @Override
    public void save(Event event) {
        OperationHistoryDo historyDo = operationHistoryFactory.build(event);
        if (null != historyDo) {
            operationHistoryAbility.save(historyDo);
        }
    }
}
