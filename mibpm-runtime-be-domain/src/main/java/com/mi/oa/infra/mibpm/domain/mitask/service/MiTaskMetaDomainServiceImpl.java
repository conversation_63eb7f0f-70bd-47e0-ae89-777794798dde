package com.mi.oa.infra.mibpm.domain.mitask.service;

import com.mi.flowable.external.api.ExtHistoricProcessInstance;
import com.mi.flowable.external.api.ProcessInstanceCheckRepresentation;
import com.mi.flowable.external.api.ProcessInstanceRepresentation;
import com.mi.flowable.external.api.TaskRepresentation;
import com.mi.flowable.external.impl.ExtHistoricProcessInstanceQueryImpl;
import com.mi.flowable.external.impl.persistence.entity.ExtHistoricProcessInstanceEntity;
import com.mi.flowable.external.impl.persistence.entity.ExtHistoricProcessInstanceEntityManager;
import com.mi.flowable.external.impl.persistence.entity.ExtHistoricTaskInstanceEntity;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskMetaDo;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskMetaRepository;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.FlowableIllegalArgumentException;
import org.flowable.common.engine.impl.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MiTaskMetaDomainServiceImpl implements MiTaskMetaDomainService {

    @Autowired
    private MiTaskMetaRepository miTaskMetaRepository;

    @Override
    public void checkMetaDo(MiTaskMetaDo miTaskMetaDo) {
        if (miTaskMetaDo == null) {
            throw new FlowableIllegalArgumentException("The processDefinition cannot be null.");
        }
        if (StringUtils.isEmpty(miTaskMetaDo.getTenantId())) {
            throw new FlowableIllegalArgumentException("The tenantId cannot be empty.");
        }
        if (StringUtils.isEmpty(miTaskMetaDo.getProcessKey())) {
            throw new FlowableIllegalArgumentException("The process_key cannot be empty.");
        }
        if (StringUtils.isEmpty(miTaskMetaDo.getProcessName())) {
            throw new FlowableIllegalArgumentException("The process_name cannot be empty.");
        }
        if (StringUtils.isEmpty(miTaskMetaDo.getProcessEnName())) {
            throw new FlowableIllegalArgumentException("The process_en_name cannot be empty.");
        }
        if (StringUtils.isEmpty(miTaskMetaDo.getCategoryKey())) {
            throw new FlowableIllegalArgumentException("The category_key cannot be empty.");
        }
        if (StringUtils.isEmpty(miTaskMetaDo.getCategoryName())) {
            throw new FlowableIllegalArgumentException("The category_name cannot be empty.");
        }
    }

    @Override
    public void checkMiTaskDo(MiTaskDo miTaskDo) {
        if (miTaskDo == null) {
            throw new FlowableIllegalArgumentException("The processInstance cannot be null.");
        }
        if (StringUtils.isEmpty(miTaskDo.getTenantId())) {
            throw new FlowableIllegalArgumentException("The tenantId cannot be empty.");
        }
        if (StringUtils.isEmpty(miTaskDo.getModelCode())) {
            throw new FlowableIllegalArgumentException("The process_key cannot be empty.");
        }
        if (StringUtils.isEmpty(miTaskDo.getProcInstId())) {
            throw new FlowableIllegalArgumentException("The instance_id cannot be empty.");
        }
        ProcessInstanceStatus processInstanceStatusEnum = miTaskDo.getProcessInstanceStatus();
        if (null != processInstanceStatusEnum) {
            String processInstanceStatus = processInstanceStatusEnum.getCode();
            if (StringUtils.isEmpty(processInstanceStatus)) {
                throw new FlowableIllegalArgumentException("The status cannot be empty.");
            }
            if (!ProcessInstanceRepresentation.PENDING.equals(processInstanceStatus)
                    && !ProcessInstanceRepresentation.APPROVED.equals(processInstanceStatus)
                    && !ProcessInstanceRepresentation.REJECTED.equals(processInstanceStatus)
                    && !ProcessInstanceRepresentation.CANCELED.equals(processInstanceStatus)
                    && !ProcessInstanceRepresentation.DELETED.equals(processInstanceStatus)) {
                throw new FlowableIllegalArgumentException("The status is invalid.");
            }
        }

        if (miTaskDo.getStartTime() == null) {
            throw new FlowableIllegalArgumentException("The start_time cannot be null.");
        }
        if (miTaskDo.getUpdateTime() == null) {
            throw new FlowableIllegalArgumentException("The update_time cannot be null.");
        }
        List<TaskRepresentation> taskList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(taskList)) {
            for (TaskRepresentation taskRepresentation : taskList) {
                if (StringUtils.isEmpty(taskRepresentation.getTaskId())) {
                    throw new FlowableIllegalArgumentException("The task_id cannot be empty.");
                }
                String taskStatus = taskRepresentation.getStatus();
                if (StringUtils.isEmpty(taskStatus)) {
                    throw new FlowableIllegalArgumentException("The status cannot be empty.");
                }
                if (!TaskRepresentation.PENDING.equals(taskStatus)
                        && !TaskRepresentation.APPROVED.equals(taskStatus)
                        && !TaskRepresentation.REJECTED.equals(taskStatus)
                        && !TaskRepresentation.TRANSFERRED.equals(taskStatus)
                        && !TaskRepresentation.DONE.equals(taskStatus)) {
                    throw new FlowableIllegalArgumentException("The status is invalid.");
                }
                if (taskRepresentation.getUpdateTime() == null) {
                    throw new FlowableIllegalArgumentException("The update_time cannot be null.");
                }

            }
        }
    }

    @Override
    public ProcessInstanceCheckRepresentation checkMiTaskDoDiff(ProcessInstanceCheckRepresentation checkRepresentation) {

        if (checkRepresentation.getUpdateTimes() == null) {
            throw new FlowableIllegalArgumentException("The update_times cannot be null.");
        }

        // 提取第三方流程key
        List<String> businessKeys = checkRepresentation.getUpdateTimes()
                .stream()
                .map(ProcessInstanceCheckRepresentation.ProcessInstanceCheck::getInstanceId)
                .collect(Collectors.toList());
        // 提取第三方任务key
        List<String> taskKeys = new ArrayList<>();
        for (ProcessInstanceCheckRepresentation.ProcessInstanceCheck processInstanceCheck : checkRepresentation.getUpdateTimes()) {
            List<ProcessInstanceCheckRepresentation.TaskCheck> tasks = processInstanceCheck.getTasks();
            for (ProcessInstanceCheckRepresentation.TaskCheck task : tasks) {
                taskKeys.add(task.getTaskId());
            }
        }

        ExtHistoricProcessInstanceEntityManager historicProcessInstanceEntityManager =
                (ExtHistoricProcessInstanceEntityManager) SpringContextUtil.getBean(ExtHistoricProcessInstanceEntityManager.class);
        ExtHistoricProcessInstanceQueryImpl historicProcessInstanceQuery = historicProcessInstanceEntityManager.createExtHistoricProcessInstanceQuery();
        historicProcessInstanceQuery.tenantId(checkRepresentation.getTenantId());
        historicProcessInstanceQuery.businessKeys(businessKeys);
        historicProcessInstanceQuery.taskKeys(taskKeys);
        historicProcessInstanceQuery.includeTasks();
        List<ExtHistoricProcessInstance> historicProcessInstanceList = historicProcessInstanceQuery.list();

        Map<String, ExtHistoricProcessInstance> businessKeyMap = historicProcessInstanceList.stream()
                .collect(Collectors.toMap(ExtHistoricProcessInstance::getBusinessKey, historicProcessInstance -> historicProcessInstance));

        // *** processInstance diff start ***
        List<ProcessInstanceCheckRepresentation.ProcessInstanceCheck> diffTimes = new ArrayList<>();
        for (ProcessInstanceCheckRepresentation.ProcessInstanceCheck processInstanceCheck : checkRepresentation.getUpdateTimes()) {
            if (businessKeyMap.containsKey(processInstanceCheck.getInstanceId())) {
                ExtHistoricProcessInstanceEntity originProcessInstance = (ExtHistoricProcessInstanceEntity) businessKeyMap.get(processInstanceCheck.getInstanceId());

                Long procInsUpdateTime = processInstanceCheck.getUpdateTime();
                Long originProcInsUpdateTime = originProcessInstance.getUpdateTime();

                if (!originProcInsUpdateTime.equals(procInsUpdateTime)) {
                    // 流程实例更新时间不一致
                    processInstanceCheck.setUpdateTime(originProcInsUpdateTime);
                    diffTimes.add(processInstanceCheck);
                    continue;
                }

                // *** task diff start ***
                ProcessInstanceCheckRepresentation.ProcessInstanceCheck diffProcessInstanceCheck =
                        new ProcessInstanceCheckRepresentation.ProcessInstanceCheck();
                diffProcessInstanceCheck.setInstanceId(processInstanceCheck.getInstanceId());
                diffProcessInstanceCheck.setUpdateTime(processInstanceCheck.getUpdateTime());
                List<ProcessInstanceCheckRepresentation.TaskCheck> diffTasCheckList = new ArrayList<>();
                diffProcessInstanceCheck.setTasks(diffTasCheckList);

                for (ProcessInstanceCheckRepresentation.TaskCheck taskCheck : processInstanceCheck.getTasks()) {
                    Map<String, ExtHistoricTaskInstanceEntity> taskKeyMap = originProcessInstance.getHistoricTasksWithVariables()
                            .stream()
                            .collect(Collectors.toMap(ExtHistoricTaskInstanceEntity::getTaskKey, taskEntity -> taskEntity));

                    if (taskKeyMap.containsKey(taskCheck.getTaskId())) {

                        ExtHistoricTaskInstanceEntity taskEntity = taskKeyMap.get(taskCheck.getTaskId());

                        Long taskUpdateTime = taskCheck.getUpdateTime();
                        Long originTaskUpdateTime = taskEntity.getUpdateTime();

                        if (!originTaskUpdateTime.equals(taskUpdateTime)) {
                            // 任务更新时间不一致
                            taskCheck.setUpdateTime(originTaskUpdateTime);
                            diffTasCheckList.add(taskCheck);
                        }

                        continue;
                    }

                    // 任务不存在
                    diffTasCheckList.add(taskCheck);
                }
                // *** task diff end ***

                if (!diffTasCheckList.isEmpty()) {
                    // 流程实例中存在任务不一致
                    diffTimes.add(diffProcessInstanceCheck);
                }

                continue;
            }

            // 流程实例不存在
            diffTimes.add(processInstanceCheck);
        }
        // *** processInstance diff end ***

        ProcessInstanceCheckRepresentation checkResult = new ProcessInstanceCheckRepresentation();
        checkResult.setDiffTimes(diffTimes);
        return checkResult;
    }

    @Override
    public MiTaskMetaDo getByCode(String code, String tenantId) {
        return miTaskMetaRepository.findByModelCodeAndAppCode(code, tenantId);
    }
}
