package com.mi.oa.infra.mibpm.domain.mitask.converter;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import org.apache.commons.lang3.StringUtils;
import org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/29
 * @Description
 */
@Mapper(componentModel = "spring", uses = {AccountRemoteService.class}, builder = @Builder(disableBuilder = true))
public interface MiTaskDoFactoryConverter {
    @Mappings({
            @Mapping(source = "processInstanceId", target = "procInstId"),
            @Mapping(source = "processDefinitionId", target = "procDefId"),
            @Mapping(source = "processDefinitionId", target = "modelCode", qualifiedByName = "mapModelCode"),
            @Mapping(source = "id", target = "taskId"),
            @Mapping(source = "taskDefinitionKey", target = "taskDefKey"),
            @Mapping(source = "name", target = "taskName"),
            @Mapping(source = "createTime", target = "startTime"),
            @Mapping(source = "createTime", target = "createTime"),
            @Mapping(source = "dueDate", target = "dueDate", qualifiedByName = "toZonedDateTime")
    })
    MiTaskDo entityToDo(TaskEntityImpl taskEntity);

    @Mappings({
            @Mapping(source = "processInstanceId", target = "procInstId"),
            @Mapping(source = "processDefinitionId", target = "procDefId"),
            @Mapping(source = "processDefinitionId", target = "modelCode", qualifiedByName = "mapModelCode"),
            @Mapping(source = "id", target = "taskId"),
            @Mapping(source = "taskDefinitionKey", target = "taskDefKey"),
            @Mapping(source = "name", target = "taskName"),
            @Mapping(source = "createTime", target = "startTime"),
            @Mapping(source = "createTime", target = "createTime"),
            @Mapping(source = "dueDate", target = "dueDate", qualifiedByName = "toZonedDateTime")
    })
    MiTaskDo entityToDo(HistoricTaskInstanceEntity taskEntity);

    @Mappings({
            @Mapping(source = "processInstanceId", target = "procInstId"),
            @Mapping(source = "processDefinitionId", target = "procDefId"),
            @Mapping(source = "processDefinitionId", target = "modelCode", qualifiedByName = "mapModelCode"),
            @Mapping(source = "taskDefinitionKey", target = "taskDefKey"),
            @Mapping(source = "createTime", target = "startTime"),
            @Mapping(source = "createTime", target = "createTime"),
    })
    MiTaskDo orgDoToDo(TaskDo taskDo);

    @Named("mapModelCode")
    default String toModelCode(String processDefinitionId) {
        return processDefinitionId == null ? null : processDefinitionId.split(":")[0];
    }

    default String bpmUserToString(BpmUser bpmUser) {
        return bpmUser == null ? "" : bpmUser.getUserName();
    }

    default BpmUser stringToBpmUser(String bpmUserString, AccountRemoteService accountRemoteService) {
        return StringUtils.isBlank(bpmUserString) ? null : accountRemoteService.getUser(bpmUserString);
    }

    @Named("toZonedDateTime")
    default ZonedDateTime toZonedDateTime(Date date) {
        if (date == null || date.getTime() <= 0) {
            return null;
        }
        return ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }
}
