package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.domain.procinst.errorcode.ProcInstDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ProcessInstanceRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelRemoteService;
import com.mi.oa.infra.mibpm.sdk.dto.ModelDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/2/9 14:33
 */
@Component
public class ProcessInstanceAbilityImpl implements ProcessInstanceAbility {

    @Autowired
    private ProcessInstanceRepository processInstanceRepository;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private ModelRemoteService modelRemoteService;
    @Autowired
    private ModelMetaRepository modelMetaRepository;

    @Override
    public void start(ProcessInstanceDo processInstanceDo) {
        processInstanceRepository.start(processInstanceDo);
    }

    @Override
    public void terminate(ProcessInstanceDo processInstanceDo) {
        processInstanceRepository.terminate(processInstanceDo);
    }

    @Override
    public void recall(ProcessInstanceDo processInstanceDo) {
        processInstanceRepository.recall(processInstanceDo);
    }

    @Override
    public ProcessInstanceDo queryProcessInstanceByBusinessKey(String businessKey) {
        if (StringUtils.isBlank(businessKey)) {
            throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_BUSINESS_ID_IS_EMPTY);
        }
        ProcessInstanceDo processInstanceDo = processInstanceRepository.queryProcessInstanceByBusinessKey(businessKey);
        if (Objects.nonNull(processInstanceDo)) {
            return processInstanceDo;
        }
        throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_NOT_EXISTS, businessKey);
    }

    @Override
    public ProcessInstanceDo queryProcessInstance(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_PROC_DEF_ID_IS_EMPTY);
        }
        ProcessInstanceDo processInstanceDo = processInstanceRepository.queryProcessInstance(processInstanceId);
        if (Objects.nonNull(processInstanceDo)) {
            return processInstanceDo;
        }
        throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_NOT_EXISTS, processInstanceId);
    }

    @Override
    public ProcessInstanceDo queryHistoricProcessInstanceByBusinessKey(String businessKey) {
        if (StringUtils.isBlank(businessKey)) {
            throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_BUSINESS_ID_IS_EMPTY);
        }
        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInstByBusinessKey(businessKey);
        if (Objects.nonNull(processInstanceDo)) {
            return processInstanceDo;
        }
        throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_NOT_EXISTS, businessKey);
    }

    @Override
    public void loadProcessVariables(ProcessInstanceDo processInstanceDo) {
        processInstanceRepository.loadProcessVariables(processInstanceDo);
    }

    @Override
    public void setProcessVariables(ProcessInstanceDo processInstanceDo) {
        processInstanceRepository.setProcessVariables(processInstanceDo);
    }

    @Override
    public void setProcessVariable(ProcessInstanceDo processInstanceDo, String variableName, Object value) {
        processInstanceRepository.setProcessVariable(processInstanceDo, variableName, value);
    }

    @Override
    public void loadProcessStartUser(ProcessInstanceDo processInstanceDo) {
        if (Objects.nonNull(processInstanceDo)) {
            BpmUser user = accountRemoteService.getUser(processInstanceDo.getStartUserId());
            processInstanceDo.setStartUser(user);
        }
    }

    @Override
    public void loadProcessWrapper(ProcessInstanceDo processInstanceDo) {
        processInstanceRepository.loadProcessWrapper(processInstanceDo);
    }

    @Override
    public void checkProcessStatus(ProcessInstanceDo processInstanceDo) {
        ModelMeta modelMeta = modelMetaRepository.queryByModelCode(processInstanceDo.getModelCode());
        if (modelMeta.getModelEnableStatus() == 1) {
            return;
        }
        //throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_MODEL_STATUS_DISABLE, processInstanceDo.getModelCode());
    }
}
