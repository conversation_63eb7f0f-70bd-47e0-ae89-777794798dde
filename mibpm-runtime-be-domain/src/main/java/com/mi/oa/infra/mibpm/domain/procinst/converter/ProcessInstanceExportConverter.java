package com.mi.oa.infra.mibpm.domain.procinst.converter;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.entity.ProcessInstanceExportDo;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/11 3:57 PM
 **/
@Mapper(componentModel = "spring")
public interface ProcessInstanceExportConverter {

    ProcessInstanceExportDo convertDo(ProcessInstanceDo processInstanceDo);

    default String mapStatus(ProcessInstanceStatus status) {
        if (null == status) {
            return "";
        }
        return status.getDescCn();
    }
}
