package com.mi.oa.infra.mibpm.domain.message.ability;

import com.larksuite.appframework.sdk.core.protocol.common.I18nText;
import com.mi.oa.infra.mibpm.infra.remote.sdk.LarkAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 飞书客户端能力
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/28 17:19
 */
@Slf4j
@Service
public class LarkAppInstanceAbilityImpl implements LarkAppInstanceAbility{

    @Autowired
    private LarkAppRemoteService larkAppRemoteService;

    @Override
    public String createLarkGroup(I18nText groupName, List<String> userIds, String startUserId) {

        return larkAppRemoteService.createLarkGroup(groupName, userIds, startUserId);
    }

    @Override
    public void addUsersToChat(List<String> userIds, String chatId) {

    }
}
