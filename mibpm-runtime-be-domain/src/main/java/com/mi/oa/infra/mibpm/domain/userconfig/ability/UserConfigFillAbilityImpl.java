package com.mi.oa.infra.mibpm.domain.userconfig.ability;

import com.mi.oa.infra.mibpm.common.constant.DelegationConstants;
import com.mi.oa.infra.mibpm.domain.userconfig.model.AutoNextApprovalConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.MessagePushRuleConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserConfigDo;
import com.mi.oa.infra.mibpm.infra.userconfig.repository.UserConfigRepository;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:20
 */
@Service
public class UserConfigFillAbilityImpl implements UserConfigFillAbility {

    @Autowired
    private UserConfigRepository userConfigRepository;


    @Override
    public void fillUserInfo(UserConfigDo userConfigDo) {
        if(Objects.isNull(userConfigDo.getUserName())){
            userConfigDo.setUserName(IdentityUtil.currentUserName());
        }
    }

    @Override
    public void fillDelegationMetaInfo(UserConfigDo userConfigDo) {
        DelegationConfig delegationConfig = userConfigDo.getDelegationConfig();

        delegationConfig.setUpdateUser(IdentityUtil.currentUserName());
        delegationConfig.setUpdateTime(ZonedDateTime.now());
        delegationConfig.setStatus(DelegationConstants.VALID);
    }

    @Override
    public void fillAutoNextApprovalConfig(UserConfigDo userConfigDo) {
        //加载自动审批配置
        AutoNextApprovalConfig autoNextApprovalConfig =
                userConfigRepository.queryAutoNextApproval(userConfigDo.getUserName());
        // 填充自动审批配置
        userConfigDo.setAutoNextApprovalConfig(autoNextApprovalConfig);
    }

    @Override
    public void fillMessagePushRuleConfig(UserConfigDo userConfigDo) {
        //加载消息推送配置
        MessagePushRuleConfig messagePushRuleConfig =
                userConfigRepository.queryMessagePushRuleByUserId(userConfigDo.getUserName());
        //填充消息推送配置
        userConfigDo.setMessagePushRuleConfig(messagePushRuleConfig);
    }
}
