package com.mi.oa.infra.mibpm.infra.procinst.repository;

import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.model.ProcessTemplateContent;

/**
 * 主要用于查询1.0迁移流程的表单数据
 *
 * <AUTHOR>
 * @date 2025/2/29 14:41
 */
public interface HtmlFormContentRepository {

    /**
     * 获取模版配置
     *
     * @param clientEnum
     * @param businessKey
     * @param modelCode
     * @return
     */
    ProcessTemplateContent getContent(ClientEnum clientEnum, String businessKey, String modelCode);
}
