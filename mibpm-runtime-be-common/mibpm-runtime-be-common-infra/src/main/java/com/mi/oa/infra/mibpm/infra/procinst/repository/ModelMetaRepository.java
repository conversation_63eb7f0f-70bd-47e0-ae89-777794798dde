package com.mi.oa.infra.mibpm.infra.procinst.repository;

import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.common.model.ProcessItem;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

public interface ModelMetaRepository {

    List<ModelMeta> queryByCategoryCode(List<String> categoryCode);

    List<String> queryModelCodeByCategoryCode(List<String> categoryCode);

    List<ModelMeta> queryEnabledProcessesWithoutOwners();

    PageModel<ModelMeta> queryModelMeta(List<String> modelCodes, String processName, String modelCode, String categoryCode, Integer pageNum, Integer pageSize);


    List<ModelMeta> listModelMeta(String processName, String modelCode, String categoryCode);

    List<ModelMeta> listModelMeta(List<String> modelCodes);

    ModelMeta queryByModelCode(String modelCode);

    List<ModelMeta> queryProcessesByOwner(String userName);

    List<ModelMeta> queryProcessesByBusinessOwner(String userName);

    List<ModelMeta> queryProcessesByChecker(String userName);

    void save(ModelMeta modelMeta);

    ProcessItem queryProcessItemByModelCode(String modelCode);
}
