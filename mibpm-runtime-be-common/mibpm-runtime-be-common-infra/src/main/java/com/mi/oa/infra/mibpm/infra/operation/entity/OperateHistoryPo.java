package com.mi.oa.infra.mibpm.infra.operation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/17 16:07
 **/
@Data
@TableName("mi_bpm_operation_history")
public class OperateHistoryPo extends BasePO<OperateHistoryPo> {
    private String taskId;
    private String taskDefKey;
    private String taskName;
    private String processInstId;
    private String operation;
    private String assignee;
    private String comment;
    private String commentEn;
    private String autoOperationType;
    private String targetUser;
    private String targetTaskId;
    @TableField("target_task_def")
    private String targetTaskDefKey;
    private String targetTaskName;
    @TableField(exist = false)
    private boolean isDeleted;
}
