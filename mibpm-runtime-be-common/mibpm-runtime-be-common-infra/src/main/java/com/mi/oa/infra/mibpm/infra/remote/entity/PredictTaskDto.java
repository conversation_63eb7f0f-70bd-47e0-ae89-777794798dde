package com.mi.oa.infra.mibpm.infra.remote.entity;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 15:15
 **/
@Data
public class PredictTaskDto {

    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务key
     */
    private String taskDefKey;
    /**
     * 审批人
     */
    private BpmUser assignee;
    /**
     * 报错信息
     */
    private String description;
    /**
     * 流程定义id
     */
    private String processDefinitionId;

}
