package com.mi.oa.infra.mibpm.infra.procinst.repository;

import com.mi.oa.infra.mibpm.application.proinst.dto.req.FormStoragReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceCreateReq;

import java.util.Map;

/**
 * Repository interface for caching form content
 */
public interface FormContentCacheRepository {

    /**
     * 缓存表单内容
     */
    boolean cacheFormContent(FormStoragReq req, long expirationTimeInSeconds);

    /**
     * 获取表单缓存内容
     */
    Map<String, Object> getCachedFormContent(String modelCode, String userId);

    /**
     * 删除表单缓存内容
     * @param modelCode Model code
     * @param userId User ID
     * @return true if removal is successful, false otherwise
     */
    boolean removeCachedFormContent(String modelCode, String userId);
}
