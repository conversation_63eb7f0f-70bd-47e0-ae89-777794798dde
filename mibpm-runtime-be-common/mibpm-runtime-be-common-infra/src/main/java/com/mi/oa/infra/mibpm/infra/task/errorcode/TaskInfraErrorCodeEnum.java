package com.mi.oa.infra.mibpm.infra.task.errorcode;

import com.mi.oa.infra.oaucf.core.exception.InfraErrorCode;

/**
 * by roger
 */
public enum TaskInfraErrorCodeEnum implements InfraErrorCode {

    INFRA_UNKNOWN_ERROR(1, "基础设施层未知错误"),
    INFRA_UTILS_ERROR(2, "基础设施层工具错误"),
    PARAM_TASK_ID_IS_EMPTY(3, "任务ID缺失"),
    PARAM_PROC_INST_ID_IS_EMPTY(4, "流程实例ID缺失"),
    PARAM_PROC_DEF_ID_IS_EMPTY(5, "流程定义ID缺失"),
    PARAM_TASK_ASSIGNEE_IS_EMPTY(6, "任务处理人缺失"),
    PARAM_TASK_DEF_ID_IS_EMPTY(7, "任务定义ID缺失"),
    PARAM_TASK_DEF_KEY_IS_EMPTY(8, "任务定义Key缺失"),
    PROCESS_START_EVENT_NOT_EXISTS(9, "流程 %s 开始节点不存在"),
    TASK_NOT_EXISTS(10, "任务 %s 不存在"),
    COME_BACK_TARGET_ACTIVITY_ID_IS_EMPTY(11, "返回的目标节点ID缺失"),
    COME_BACK_TARGET_ACTIVITY_TYPE_NOT_FOUND(12, "返回的目标节点ID未找到合适类型"),
    PROC_INST_NOT_EXISTS(13, "流程实例 %s 不存在"),
    SERVICE_TASK_ERROR(14, "服务节点调用远程接口失败,请联系流程管理员"),
    RECEIVE_TASK_NOT_EXISTS(15, "接收任务不存在"),
    MODEL_NOT_EXISTS(16, "模型 %s 不存在"),
    CATEGORY_NOT_EXISTS(17, "分类 %s 不存在"),
    MITASK_CREATE_ERROR(18, "MiTask对接BPM3.0创建失败"),
    MITASK_COMPLETED_UPTATE_ERROR(19, "MiTask对接BPM3.0任务完成更新失败"),
    MITASK_PROC_COMPLETED_UPTATE_ERROR(20, "MiTask对接BPM3.0流程完成更新失败"),
    MITASK_ACTIVITY_COMPLETED_UPDATE_ERROR(21, "MiTask对接BPM3.0节点完成更新失败"),
    MITASK_ENTITY_UPDATE_ERROR(22, "MiTask对接BPM3.0实体更新失败"),
    MITASK_FIRST_START_UPDATE_ERROR(23, "MiTask对接BPM3.0发起节点标识设置失败");
    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    TaskInfraErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

}
