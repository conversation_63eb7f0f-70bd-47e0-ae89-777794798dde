package com.mi.oa.infra.mibpm.infra.remote.sdk;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.organization.rep.OrgVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/10 11:05 AM
 **/
public interface OrgRemoteService {

    List<BpmUser> listUserByOrg(String orgCode);

    BpmUser getOrgOwner(String orgCode);

    OrgVO getOrgDetail(String orgCode);

    List<OrgVO> listOrg(String orgCode);

    List<BpmUser> listUserByJob(String jobCode, String orgCode, String orgTreeCode);

}
