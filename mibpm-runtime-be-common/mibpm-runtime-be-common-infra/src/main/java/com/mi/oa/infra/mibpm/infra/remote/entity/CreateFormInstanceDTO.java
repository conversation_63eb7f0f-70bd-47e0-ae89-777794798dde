package com.mi.oa.infra.mibpm.infra.remote.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 创建表单实例
 *
 * <AUTHOR>
 * @date 2022/6/1 15:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateFormInstanceDTO {

    private String formDefinitionId;
    private String businessKey;
    private String processInstanceId;
    private String taskId;
    private String taskKey;
    private Map<String, Object> data;
    private boolean byPermission;
}
