package com.mi.oa.infra.mibpm.infra.procinst.repository;

import com.mi.oa.infra.mibpm.application.message.dto.req.DeletePermissionReq;
import com.mi.oa.infra.mibpm.common.enums.DeptFilter;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionDo;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionRecordsDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DeptPermissionDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserPermissionDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;
/**
 * <AUTHOR>
 * @Date 2024/7/16 17:08
 */
public interface PermissionOperationRepository {
    /**
     * 分页查询用户获得的权限
     */
    PageModel<PermissionDo> page(String userOrpid, long pageNum, long pageSize, String deptId);

    /**
     * 分页查询用户权限操作记录
     */
    PageModel<PermissionRecordsDo> exportRecords(String userOrpid, long pageNum, long pageSize, String deptId);

    /**
     * 添加直属下级的权限
     */
    void addPermissionAssignee(UserPermissionDo userPermissionDo);

    /**
     * 添加部门的权限
     */
    void addPermissionDept(DeptPermissionDo deptPermissionDo);

    /**
     * 删除权限
     */
    void deletePermission(DeletePermissionReq deletePermissionReq);

    /**
     * 权限增加变更记录
     */
    void addApplyRecord(String userOrpid, String permissionId, String businessKey, String taskId, PermissionIdType permissionIdType, String createUser);

    /**
     * 权限删除变更记录
     */
    void addRemoveRecord(DeletePermissionReq deletePermissionReq);

    /**
     * 查询用户获得的部门和直属下级的权限
     */
    PermissionDo queryPermission(String userOrpid, String permissionId);

    /**
     * 查询部门权限是否已存在
     */
    Boolean isDeptPermission(String userOrpid, String deptId);

    /**
     * 查询直属下级权限是否存在
     */
    Boolean isAssigneePermission(String userOrpid, String permissionUserId);

    /**
     * 初始化用户的部门权限(admin)
     */
    void addUserInitDeptId(String userOrpid, String deptId, DeptFilter deptFilter);

    /**
     * 查询部门权限(admin)
     */
    List<DeptPermissionDo> queryDeptPermission(String userOrpid);

    /**
     * 查询直属下级权限(admin)
     */
    List<UserPermissionDo> queryAssigneePermission(String userOrpid);

    /**
     * 删除部门权限(admin)
     */
    void removeDeptId(String userOrpid, String deptId);

    /**
     * 删除直属下级权限(admin)
     */
    void removeAssigneeId(String userOrpid, String permissionUserId);

    void removeDashboard(String userOrpid, String permissionUserId);

    /**
     * 添加部门权限(admin)
     */
    void addDeptId(String userOrpid, String deptId);

    /**
     * 添加直属下级权限(admin)
     */
    void addAssigneeId(String userOrpid, String permissionUserId);

    void insertIfNotExist(String userOrpid, String permissionUserId);
}
