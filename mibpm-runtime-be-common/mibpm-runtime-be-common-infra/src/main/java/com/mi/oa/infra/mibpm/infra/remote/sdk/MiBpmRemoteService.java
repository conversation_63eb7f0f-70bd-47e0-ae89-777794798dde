package com.mi.oa.infra.mibpm.infra.remote.sdk;

import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.infra.remote.entity.CompleteTaskDTO;
import com.mi.oa.infra.mibpm.infra.remote.entity.MibpmTaskDetailResp;
import com.mi.oa.infra.mibpm.infra.remote.entity.OldLarkTodoMessageReq;
import com.mi.oa.infra.mibpm.infra.remote.entity.TaskCategory;
import com.mi.oa.infra.mibpm.infra.remote.entity.TaskListResp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/4/18 3:41 PM
 **/
public interface MiBpmRemoteService {

    /**
     * 2.0发送待办消息卡片
     * @param oldLarkTodoMessageReq
     */
    void sendOldLarkTodoMessage(OldLarkTodoMessageReq oldLarkTodoMessageReq);

    /**
     * 获取老系统中的流程详情
     *
     * @param currentUserId
     * @param taskId
     * @param procInstId
     * @param modelCode
     * @return
     */
    MibpmTaskDetailResp getBpmTaskDetail(String currentUserId, String taskId, String procInstId, String modelCode);

    Map oldTaskDetail(Map map);

    /**
     * 动态表单获取数据源
     *
     * @param appCode
     * @param path
     * @param params
     * @return
     */
    Map getBpmFormDataSource(String appCode, String path, Map<String, Object> params);

    /**
     * 老系统完成任务
     *
     * @param dto
     * @return
     */
    Map<String, Object> completeTask(CompleteTaskDTO dto);

    /**
     * hr流程分类接口
     *
     * @param userName
     * @param taskType
     * @param language
     * @return
     */
    List<TaskCategory> hrCategoryList(String userName, Integer taskType, String language);

    /**
     * hr流程待办任务
     *
     * @param userName
     * @param title
     * @param bpmType
     * @param currentPage
     * @param pageSize
     * @param language
     * @return
     */
    TaskListResp waitTaskList(String userName, String title, String bpmType, Long currentPage, Long pageSize, String language);

    /**
     * hr流程已办任务
     *
     * @param userName
     * @param title
     * @param bpmType
     * @param currentPage
     * @param pageSize
     * @param language
     * @return
     */
    TaskListResp historyTaskList(String userName, String title, String bpmType, Long currentPage, Long pageSize, String language);


    /**
     * 老流程已发起任务
     *
     * @param userName
     * @param title
     * @param bpmType
     * @param currentPage
     * @param pageSize
     * @param language
     * @return
     */
    TaskListResp myTaskList(String userName, String title, String bpmType, Long currentPage, Long pageSize, String language, ClientEnum clientEnum);

    /**
     * 催办透传
     *
     * @param map
     * @return
     */
    Map oldUrge(Map map);

    /**
     * 讨论透传
     *
     * @param map
     * @return
     */
    Map oldDiscuss(Map map);

    /**
     * 下载文件透传
     *
     * @param map
     * @return
     */
    Object getFdsFile(Map map);

    /**
     * 通用透传
     *
     * @param map
     * @param path
     * @return
     */
    Object passRequest(String path, Map map);
}
