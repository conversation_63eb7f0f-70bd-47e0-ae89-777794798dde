package com.mi.oa.infra.mibpm.infra.remote.sdk;

import com.alibaba.cola.dto.SingleResponse;
import com.mi.oa.infra.mibpm.infra.remote.entity.MddFormDefDTO;
import com.mi.ucf.mdd.dto.ProcessFormDataQry;
import com.mi.ucf.mdd.dto.data.FormDataDTO;
import feign.Headers;
import feign.Param;
import feign.QueryMap;
import feign.RequestLine;

/**
 * <AUTHOR>
 * @date 2022/5/19 17:42
 */
public interface MddProcessFormService {

    @RequestLine("GET /api/v1/process/forms/definitions/search?processDefinitionId={processDefinitionId}&taskDefinitionId={taskDefinitionId}&clientType={clientType}")
    @Headers("Content-Type: application/json")
    SingleResponse<MddFormDefDTO> getProcessFromDef(@Param("processDefinitionId") String processDefinitionId,
                                                    @Param("taskDefinitionId") String taskDefinitionId,
                                                    @Param("clientType") String clientType);

    @RequestLine("GET /api/v1/process/forms/formData/search?processInstanceId={processInstanceId}&taskDefinitionId={taskDefinitionId}&clientType={clientType}")
    @Headers("Content-Type: application/json")
    SingleResponse<FormDataDTO> getProcessFormData(@Param("processInstanceId") String processInstanceId,
                                                   @Param("taskDefinitionId") String taskDefinitionId,
                                                   @Param("clientType") String clientType);
}
