package com.mi.oa.infra.mibpm.infra.apicall.repository;

import com.mi.oa.infra.mibpm.application.apicall.dto.req.ApiCallHistoryQuery;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiCallHistoryDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

/**
 * @author: qiuzhipeng
 * @Date: 2022/2/9 18:45
 *
 * 服务调用日志记录
 */
public interface ApiCallHistoryRepository {

    /**
     * 保存调用记录
     *
     * @param apiCallHistoryDo
     */
     void saveApiCallHistory(ApiCallHistoryDo apiCallHistoryDo);

    /**
     * 加载调用记录
     *
     * @param apiCallHistoryId
     * @return
     */
     ApiCallHistoryDo loadApiCallHistory(Integer apiCallHistoryId);

    /**
     * 条件查询调用记录列表
     *
     * @param apiCallHistoryQuery
     * @return hi list
     */
    PageModel<ApiCallHistoryDo> queryApiCallHistoryListPage(ApiCallHistoryQuery apiCallHistoryQuery);

    /**
     * 获取调用详情
     *
     * @param id
     * @return
     */
    ApiCallHistoryDo queryApiCallHistoryById(Integer id);

    /**
     * 删除最久的1百万记录
     */
    void deleteFarthestOneMillionHistory();

    /**
     * 删除调用记录
     *
     * @param maxId 最大ID
     */
    void deleteHistoryLessThanId(long maxId);

    /**
     * 通过apiId, instId查找调用记录
     *
     * @param apiId, instId
     * @return
     */
    ApiCallHistoryDo queryByApiIdAndSequenceId(String apiId, String sequenceId);
}
