package com.mi.oa.infra.mibpm.infra.remote.sdk;

import com.mi.oa.infra.mibpm.common.model.BpmUser;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/14 3:14 PM
 **/
public interface AccountRemoteService {

    List<BpmUser> listUserReportLine(String userIdentifier);

    /**
     * 查询单个用户信息
     *
     * @param userIdentifier 长度> 20 当作uid
     * @return
     */
    BpmUser getUser(String userIdentifier);

    /**
     * 批量查询用户信息
     *
     * @param userIdentifier 长度> 20 当作uid
     * @return
     */
    List<BpmUser> listUsers(Collection<String> userIdentifier);

    /**
     * 加载用户职级信息
     *
     * @param bpmUser
     */
    void loadUserRankInfo(BpmUser bpmUser);
}
