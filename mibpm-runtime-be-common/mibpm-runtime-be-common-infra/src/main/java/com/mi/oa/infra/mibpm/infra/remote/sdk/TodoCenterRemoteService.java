package com.mi.oa.infra.mibpm.infra.remote.sdk;

import com.mi.oa.infra.mibpm.infra.remote.entity.CreateTodoCenterTaskReq;

/**
 * 工作台待办中心远程服务
 *
 * @author: qiuzhipeng
 * @Date: 2023/3/23 14:44
 */
public interface TodoCenterRemoteService {

    /**
     * 创建待办中心任务
     *
     * @param createTodoCenterTaskReq 任务信息
     */
    void createTask(CreateTodoCenterTaskReq createTodoCenterTaskReq);

    /**
     * 结束待办中心任务
     *
     * @param taskId 任务ID
     */
    void complete(String taskId);

    /**
     * 移除待办中心任务
     *
     * @param taskId 任务ID
     * @param comment 移除原因
     */
    void removeTask(String taskId, String comment);

    /**
     * 更新待办中心任务
     *
     * @param taskId 任务ID
     * @param executor 执行人
     */
    void transfer(String taskId, String executor);
}
