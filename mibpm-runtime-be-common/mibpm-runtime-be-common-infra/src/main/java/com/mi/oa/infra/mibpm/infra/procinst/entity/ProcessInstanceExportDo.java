package com.mi.oa.infra.mibpm.infra.procinst.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/11 2:54 PM
 **/
@Data
public class ProcessInstanceExportDo {

    @ExcelProperty("流程ID")
    protected String processInstanceId;
    @ExcelProperty("业务ID")
    protected String businessKey;
    @ExcelProperty("流程标题")
    protected String processInstanceName;
    @ExcelProperty("审批状态")
    protected String processInstanceStatus;
    @ExcelProperty("提交时间")
    protected Date startTime;
    @ExcelProperty("完成时间")
    protected Date endTime;
    @ExcelProperty("提交人ID")
    protected String startUserId;
    @ExcelProperty("提交人姓名")
    protected String startUserName;
    @ExcelProperty("发起人部门")
    protected String startUserDept;
    @ExcelProperty("审批记录")
    protected String approvalHistory;
}
