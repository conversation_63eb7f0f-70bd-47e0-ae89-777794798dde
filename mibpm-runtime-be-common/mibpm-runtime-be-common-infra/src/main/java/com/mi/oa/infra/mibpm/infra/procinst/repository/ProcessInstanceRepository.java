package com.mi.oa.infra.mibpm.infra.procinst.repository;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;

import java.util.List;
import java.util.Map;

/**
 * 流程实例仓库接口
 *
 * <AUTHOR>
 * @date 2022/2/9 14:41
 */
public interface ProcessInstanceRepository {

    /**
     * 启动流程实例
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void start(ProcessInstanceDo processInstanceDo);

    /**
     * 终止流程实例
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void terminate(ProcessInstanceDo processInstanceDo);

    /**
     * 撤回流程实例
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void recall(ProcessInstanceDo processInstanceDo);

    /**
     * 根据业务唯一编码获取流程实例
     *
     * @param businessKey 业务唯一编码
     * @return 流程实例领域对象
     */
    ProcessInstanceDo queryProcessInstanceByBusinessKey(String businessKey);

    /**
     * 查询流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例领域对象
     */
    ProcessInstanceDo queryProcessInstance(String processInstanceId);

    /**
     * 查询流程实例
     *
     * @param processInstanceIds 流程实例ID
     * @return 流程实例领域对象
     */
    List<ProcessInstanceDo> queryProcessInstanceList(List<String> processInstanceIds);

    /**
     * 指定流程实例加载变量
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void loadProcessVariables(ProcessInstanceDo processInstanceDo);

    /**
     * 构建流程变量，包括显示设置的变量及其衍生参数和系统内置参数
     *
     * @param processInstanceDo 流程实例领域对象
     * @param delegateVariables 流程变量
     * @param updateSummary     是否需要更新摘要信息（form 为空则不更新）
     * @return 构建好的流程变量
     */
    Map<String, Object> buildProcessVariables(ProcessInstanceDo processInstanceDo, Map<String, Object> delegateVariables, boolean updateSummary);

    /**
     * 构建流程实例标题
     *
     * @param processInstanceDo 流程实例领域对象
     * @param formData 表单数据
     */
    void buildProcessInstanceName(ProcessInstanceDo processInstanceDo, Map<String, Object> formData);

    /**
     * 将流程变量装配到引擎中
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void setProcessVariables(ProcessInstanceDo processInstanceDo);


    /**
     * 设置流程变量
     *
     * @param processInstanceDo 流程实例领域对象
     * @param variableName 流程变量名
     * @param value 变量值
     */
    void setProcessVariable(ProcessInstanceDo processInstanceDo, String variableName, Object value);

    /**
     * 生成实例流程图片
     *
     * @param processInstanceId 流程实例ID
     * @return 流程图片
     */
    byte[] genProcessDiagram(String processInstanceId);

    /**
     * 加载流程包装对象
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void loadProcessWrapper(ProcessInstanceDo processInstanceDo);
}
