package com.mi.oa.infra.mibpm.infra.procinst.repository;

import com.mi.oa.infra.mibpm.common.model.TaskAssignee;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;

import java.util.List;

/**
 * 任务处理人仓库接口，用于处理用户任务节点处理人来源于业务系统传入的情况
 *
 * <AUTHOR>
 * @date 2022/3/9 18:27
 */
public interface TaskAssigneeRepository {

    /**
     * 存储业务系统传入的各用户任务节点的处理人
     *
     * @param processInstanceDo 流程实例领域对象
     * @param taskAssignees     任务处理人
     */
    void saveTaskAssignee(ProcessInstanceDo processInstanceDo, List<TaskAssignee> taskAssignees);
}
