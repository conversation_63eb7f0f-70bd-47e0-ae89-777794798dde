package com.mi.oa.infra.mibpm.application.task.service;

import com.mi.oa.infra.mibpm.application.task.dto.resp.ApprovalTaskResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailOperationHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskLinkResp;

import java.util.List;

/**
 * Open-API 审批服务
 *
 * <AUTHOR>
 * @date 2022/5/6 19:29
 */
public interface OpenApprovalService {

    /**
     * 获取审批记录
     *
     * @param businessKey 流程实例业务层编码
     * @param needPredict 是否需要流程预测节点
     * @return 审批记录
     */
    List<ApprovalTaskResp> listApprovalTasks(String businessKey, boolean needPredict);

    /**
     * 获取当前审批任务
     *
     * @param businessKey 流程实例业务层编码
     * @return 审批任务
     */
    List<ApprovalTaskResp> listCurrentTasks(String businessKey);

    /**
     * 获取审批任务详情链接
     *
     * @param businessKey 流程实例业务层编码
     * @param assignee 任务处理人
     * @return 审批任务详情链接
     */
    String getDetailLink(String businessKey, String assignee);

    TaskLinkResp getDetailLinkAllClient(String businessKey, String assignee);

    /**
     * 获取审批记录
     *
     * @param businessKey 流程实例业务层编码
     * @return 审批记录
     */
    List<TaskDetailOperationHistoryResp.TaskOperateHistoryResp> listOperationHistory(String businessKey);
}
