package com.mi.oa.infra.mibpm.application.task.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/7/7 15:40
 */
@Data
@ApiModel(value = "申请任务查看权限请求", description = "申请任务查看权限请求")
public class TaskViewAuthApplyReq {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;


    @ApiModelProperty(value = "留言", required = true)
    @NotBlank(message = "留言")
    private String message;
}
