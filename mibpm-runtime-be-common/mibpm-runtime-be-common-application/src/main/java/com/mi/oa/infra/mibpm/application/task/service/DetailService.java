package com.mi.oa.infra.mibpm.application.task.service;

import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailCcResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailOperationHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.VoteConfigResp;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/15 10:58
 **/
public interface DetailService {

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailResp detailBase(String taskId, ClientEnum client);

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailResp detailForm(String taskId, ClientEnum client);

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailCcResp detailCc(String taskId, ClientEnum client);

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailResp detailConfig(String taskId, ClientEnum client);

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailHistoryResp detailHistory(String taskId, ClientEnum client);

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailOperationHistoryResp detailOperationHistory(String taskId, ClientEnum client);

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailHistoryResp detailPredict(String taskId, ClientEnum client);

    VoteConfigResp voteConfig(String procInstId, String taskDefKey);
}
