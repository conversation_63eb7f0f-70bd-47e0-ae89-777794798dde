package com.mi.oa.infra.mibpm.application.task.service;

import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskInstanceResp;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskQueryPageReq;
import com.mi.oa.infra.mibpm.common.model.CascadeCategory;
import com.mi.oa.infra.oaucf.core.dto.PageVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/4/6 5:46 PM
 **/
public interface TodoCenterService {

    /**
     * 获取待办任务分类列表
     * <p>
     *
     *
     * @param userId
     * @param userName
     * @return 待办任务分类列表
     */
    List<CascadeCategory.CategoryLevel> listTodoTaskCategories(String userId, String userName);

    /**
     * 获取已办任务分类列表
     * <p>
     *
     *
     * @param userId
     * @param userName
     * @return 已办任务分类列表
     */
    List<CascadeCategory.CategoryLevel> listReviewedTaskCategories(String userId, String userName);

    /**
     * 获取待办任务数量
     *
     * @param req 待办任务查询参数
     * @return 待办任务数量
     */
    long waitTaskCount(TaskQueryPageReq req);

    /**
     * 获取待办任务列表
     *
     * @param req      任务分页查询参数
     * @param pageSize 每页数量
     * @param pageNum  页数
     * @return 待办任务列表
     */
    PageVO<TaskInstanceResp> waitTaskList(TaskQueryPageReq req, long pageNum, long pageSize);

    /**
     * 获取历史任务列表
     *
     * @param req      任务分页查询参数
     * @param pageSize 每页数量
     * @param pageNum  页数
     * @return 历史任务列表
     */
    PageVO<TaskInstanceResp> historyTaskList(TaskQueryPageReq req, long pageNum, long pageSize);

}
