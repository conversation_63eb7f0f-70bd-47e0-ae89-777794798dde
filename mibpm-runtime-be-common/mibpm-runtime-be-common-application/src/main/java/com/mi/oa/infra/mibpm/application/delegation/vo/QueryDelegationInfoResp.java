package com.mi.oa.infra.mibpm.application.delegation.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Department;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27
 * @Description
 */
@Data
@Builder
public class QueryDelegationInfoResp {
    /**
     * id
     */
    private Long id;
    /**
     * 委托人
     */
    private BpmUser delegator;
    /**
     * 被委托人
     */
    private BpmUser approver;
    /**
     * 更新记录人id（一般为管理员）
     */
    private BpmUser updateUser;
    /**
     * 记录创建人
     */
    private BpmUser createUser;
    /**
     * 委托开始时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;
    /**
     * 委托结束时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;
    /**
     * 委托类型，0代表全部流程委托，1代表部分流程委托
     */
    private Integer type;
    /**
     * 委托状态，-1委托失效 0委托审批中  1委托生效 2委托未开始 3委托审批未通过
     */
    private Integer status;
    /**
     * 委托理由
     */
    private String delegationReason;
    /**
     * 委托流程基本信息
     */
    private ModelMeta modelMeta;
    /**
     * 全部流程排除部分委托的流程信息
     */
    private List<ModelMeta> exclusionModelMetas;
    /**
     * 表示流程的范围或适用性，0:不限 1:属于 2:不属于
     */
    private Integer delegationScope;
    /**
     * 表示流程发起人的部门
     */
    private List<Department> initiatingDeptCodes;
    /**
     * 委托流程实例化id
     */
    private String delegationProcessInstanceId;
    /**
     * 委托需求提出人
     */
    private BpmUser delegationRequirementProposer;
    /**
     * 更新时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    /**
     * 委托的任务id
     */
    private String taskId;
}
