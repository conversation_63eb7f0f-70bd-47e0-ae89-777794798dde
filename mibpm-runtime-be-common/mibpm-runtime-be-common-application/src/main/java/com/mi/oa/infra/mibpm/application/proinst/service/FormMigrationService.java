package com.mi.oa.infra.mibpm.application.proinst.service;

import java.util.Map;

/**
 * 迁移服务
 *
 * <AUTHOR>
 * @date 2022/5/19 11:08
 */
public interface FormMigrationService {

    /**
     * 迁移指定流程下的表单定义到2.0表单服务
     *
     * @param processDefinitionKey 流程定义KEY
     */
    Map<String, Boolean> migrateFormDefinition(String processDefinitionKey);

    /**
     * 迁移指定流程下的表单数据到2.0表单服务
     *
     * @param processDefinitionKey 流程定义KEY
     */
    void migrateFormData(String processDefinitionKey);
}
