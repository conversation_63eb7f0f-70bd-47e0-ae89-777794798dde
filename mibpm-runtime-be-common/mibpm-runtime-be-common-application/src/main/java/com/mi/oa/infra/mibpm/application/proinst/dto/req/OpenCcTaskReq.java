package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7 14:41
 */
@Data
@ApiModel(value = "Open API审批任务抄送请求", description = "Open API审批任务抄送请求")
public class OpenCcTaskReq {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    @ApiModelProperty(value = "被抄送人id", required = true)
    private List<String> assignee;
    @ApiModelProperty(value = "意见", required = false)
    private String comment;
    @ApiModelProperty(value = "操作用户", required = true)
    @NotBlank(message = "操作用户不能为空")
    private String operator;
}
