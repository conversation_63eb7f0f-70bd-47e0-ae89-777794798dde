package com.mi.oa.infra.mibpm.application.proinst.dto.reps;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/22 10:23
 */
@Data
@ApiModel(value = "Open API 任务基本信息响应", description = "Open API 任务基本信息响应")
public class OpenSimpleTaskResp {

    @ApiModelProperty("任务ID")
    protected String taskId;
    @ApiModelProperty("任务名称")
    protected String taskName;
    @ApiModelProperty("任务定义KEY")
    protected String taskDefinitionKey;
    @ApiModelProperty("优先级")
    protected Integer priority;
    @ApiModelProperty("任务处理人")
    protected BpmUser assignee;
    @ApiModelProperty("任务候选人")
    protected List<BpmUser> candidates;
    @ApiModelProperty("流程实例ID")
    protected String processInstanceId;
    @ApiModelProperty("任务创建时间")
    protected ZonedDateTime createTime;
    @ApiModelProperty("任务创建时间")
    protected ZonedDateTime endTime;
    @ApiModelProperty("任务过期时间")
    protected ZonedDateTime dueDate;
    @ApiModelProperty("流程发起时间")
    private ZonedDateTime processStartTime;
    @ApiModelProperty("审批操作列表")
    private UserTaskOperation operation;
}
