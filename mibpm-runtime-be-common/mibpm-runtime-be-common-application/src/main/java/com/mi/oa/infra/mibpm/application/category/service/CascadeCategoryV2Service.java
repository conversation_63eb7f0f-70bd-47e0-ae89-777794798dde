package com.mi.oa.infra.mibpm.application.category.service;

import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.model.Category;

/**
 * <AUTHOR>
 * @date 2024/5/08 11:06 AM
 **/
public interface CascadeCategoryV2Service {

    /**
     * 待办任务分类
     *
     * @param userId
     * @return
     */
    Category todoTaskCategory(String userId, ClientEnum client);

    /**
     * 已办任务分类
     *
     * @param userId
     * @return
     */
    Category reviewedTaskCategory(String userId, ClientEnum client);

    /**
     * 抄送任务分类
     *
     * @param userId
     * @param userName
     * @return
     */
    Category ccTaskCategory(String userId, String userName, ClientEnum client);

    /**
     * 已发起流程分类
     *
     * @param userId
     * @param userName
     * @param client
     * @return
     */
    Category submitProcInstCategory(String userId, String userName, ClientEnum client);

}
