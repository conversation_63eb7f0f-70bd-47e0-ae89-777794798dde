package com.mi.oa.infra.mibpm.application.message.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/28 16:38
 */
@Data
@ApiModel(value = "沟通请求", description = "沟通请求")
public class DiscussReq {

    @ApiModelProperty(value = "流程ID", required = false)
    private String processInstanceId;

    @ApiModelProperty(value = "任务ID", required = false)
    private String taskId;

    @ApiModelProperty(value = "沟通对象", required = false)
    private List<String> targetUsers;

    @ApiModelProperty(value = "操作用户", required = false)
    private String operator;
}
