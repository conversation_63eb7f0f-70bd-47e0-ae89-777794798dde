package com.mi.oa.infra.mibpm.application.proinst.dto.reps;

import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/16 16:56
 * @Version 1.0
 */
@Data
public class RequirePermissionDto {
    /**
     *权限Id
     */
    private String permissionId;

    /**
     * 权限名
     */
    private String permissionName;

    /**
     * 权限类型
     */
    private PermissionIdType permissionIdType;

}
