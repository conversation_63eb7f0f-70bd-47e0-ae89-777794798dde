package com.mi.oa.infra.mibpm.application.task.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/7 11:26
 **/
@Data
public class TaskDetailHistoryResp {
    @ApiModelProperty("流程实例id")
    private String processInstanceId;
    private ProcessInstanceStatus processInstanceStatus;
    private String modelCode;
    @ApiModelProperty("任务节点列表")
    private List<FlowNodeResp> nodeList;

    @Data
    public static class FlowNodeResp {
        @ApiModelProperty("任务定义key")
        private String nodeId;
        @ApiModelProperty("任务名称")
        private String name;
        @ApiModelProperty("审批方式")
        private UserTaskSignType signType;
        @ApiModelProperty("任务列表")
        private List<TaskInstanceResp> taskList;
        @ApiModelProperty("是否预测任务")
        @JsonProperty("isPredict")
        private boolean isPredict;
    }
}
