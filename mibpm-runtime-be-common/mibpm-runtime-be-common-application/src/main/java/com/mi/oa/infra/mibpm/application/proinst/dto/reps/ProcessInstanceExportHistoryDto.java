package com.mi.oa.infra.mibpm.application.proinst.dto.reps;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/7 7:16 PM
 **/
@Data
public class ProcessInstanceExportHistoryDto {

    /**
     * 流程类型
     **/
    private String modelCode;
    /**
     * 流程类型名称
     **/
    private String modelName;
    /**
     * 数据类型 0-审批 1-表单
     **/
    private Integer type;
    /**
     * 导出时间
     **/
    private ZonedDateTime exportTime;
    /**
     * 状态0-进行中 1-完成 2-失败
     **/
    private Integer exportStatus;
    /**
     * 文件链接
     **/
    private String url;
    /**
     * 导出人
     **/
    private BpmUser exportUser;
}
