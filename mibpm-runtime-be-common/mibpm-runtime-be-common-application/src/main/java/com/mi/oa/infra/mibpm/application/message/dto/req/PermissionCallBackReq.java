package com.mi.oa.infra.mibpm.application.message.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/24 11:09
 */
@Data
public class PermissionCallBackReq {

    @JsonProperty("user_da9864fd57c9")
    private String userOrpid;
    @JsonProperty("select_4afc763d32cc")
    private PermissionIdType type;
    @JsonProperty("user_79d07e0be6ee")
    private String userPermissionId;
    @JsonProperty("dept_bb0e19de5d63")
    private List<String> deptPermissionId;
    @JsonProperty("textarea_7ad88501a0a7")
    private String reason;
}


