package com.mi.oa.infra.mibpm.application.proinst.dto.reps;

import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.Data;
import java.time.ZonedDateTime;

/**
 * @<PERSON> lix<PERSON>han
 * @Date 2024/7/16 16:56
 * @Version 1.0
 */
@Data
public class PermissionRecordsDto {
    /**
     *用户名Id
     */
    private String userOrpid;

    /**
     * 操作人详细信息
     */
    private BpmUser createUser;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 清单Id
     */
    private String businessKey;

    /**
     * 变动时间
     */
    private ZonedDateTime createTime;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限Id
     */
    private String permissionId;

}
