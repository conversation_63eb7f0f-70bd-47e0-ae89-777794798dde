package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/23 10:41
 */
@Data
public class PermissionCallBackBaseReq<T> {
    private String businessKey;
    private String taskId;
    private String taskName;
    private String modelCode;
    private String status;
    private BpmUser startUser;
    private T formData;
}

