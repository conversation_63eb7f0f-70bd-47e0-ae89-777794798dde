package com.mi.oa.infra.mibpm.application.proinst.dto.reps;

import com.mi.oa.infra.mibpm.common.model.BpmOrg;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/5/22 10:43
 **/
@Data
public class ModelMetaDto {
    private String name;
    private String enName;
    private String categoryCode;
    private CategoryResp category;
    private String appCode;
    private String modelCode;
    private List<BpmUser> owners;
    private BpmOrg ownerDept;
    protected List<BpmUser> checker;
    protected Integer fromOld;
    protected String description;
    private List<BpmUser> businessOwner;
    private String prodAppCode;
    protected String grade;
    protected String scope;

}
