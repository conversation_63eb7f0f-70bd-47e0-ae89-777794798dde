package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import com.mi.flowable.idm.api.FormData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "暂存请求", description = "暂存表单")
public class FormStoragReq {
    @ApiModelProperty(value = "用户名称", required = false)
    protected String userName;

    @NotBlank(message = "模型编码不能为空")
    @ApiModelProperty(value = "模型编码", required = true)
    protected String modelCode;

    @ApiModelProperty(value = "表单数据", required = false)
    protected Map<String, Object> formData;

}
