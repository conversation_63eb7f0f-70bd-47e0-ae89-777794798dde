package com.mi.oa.infra.mibpm.application.mitask.dto;

import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.common.model.ExternalContent;
import lombok.Data;

import java.util.List;

/**
 * @Author: zoutongxu1
 * @DateTime: 2024/10/23 19:57
 * @Description: 第三方业务系统 消息卡片类
 */
@Data
public class ExtCardBot {

    private String username;

    private String uuid;

    private String processKey;

    private String instanceId;

    private String taskId;

    /**
     * 消息卡片title
     */
    private String title;

    /**
     * 评论区
     */
    private String comment;

    /**
     * 通用模版
     */
    private ExternalContent content;

    /**
     * 自定义模版
     */
    private String customizeContent;

    /**
     * 操作按钮区
     */
    private List<Actions> actions;

    private String messageId;

    /**
     * 租户appID
     */
    private String appId;

    /**
     * bpmType
     */
    private String bpmType;

    /**
     * 当前审批状态
     */
    private String status;

    /**
     * 事件类型
     */
    private String eventType;
}
