package com.mi.oa.infra.mibpm.application.userconfig.service;

import com.mi.oa.infra.mibpm.application.message.dto.req.ApplyPermissionReq;
import com.mi.oa.infra.mibpm.application.message.dto.req.DeletePermissionReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.PermissionRecordsDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RequirePermissionDto;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.oaucf.core.dto.PageVO;

/**
 * <AUTHOR>
 * @Date 2024/7/15 16:46
 */

public interface UserPermissionService {
    /**
     * 搜索用户的部门权限（分页）
     */
    PageVO<RequirePermissionDto> queryPermissionPage(long pageNum, long pageSize, String deptId);

    /**
     * 查询用户权限操作清单（分页）
     */
    PageVO<PermissionRecordsDto> queryPermissionRecordsPage(long pageNum, long pageSize, String deptId);

    /**
     * 申请权限
     */
    void applyPermission(ApplyPermissionReq applyPermissionReq);

    /**
     * 删除权限
     */
    void removePermission(DeletePermissionReq deletePermissionReq);

    /**
     * 查询权限是否存在
     */
    Boolean queryUserPermission(String userOrpid, String permissionId);
}
