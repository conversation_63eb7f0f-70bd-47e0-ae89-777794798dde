package com.mi.oa.infra.mibpm.application.task.service;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.HistoricProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcessInstanceExportHistoryDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RecentUseModelResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceQueryReqDto;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceExportReq;
import com.mi.oa.infra.oaucf.core.dto.PageVO;

import java.util.List;

/**
 * 历史流程实例应用服务
 *
 * <AUTHOR>
 * @date 2022/5/6 11:08
 */
public interface HistoryProcessInstanceService {

    /**
     * 按条件查询发起过的流程实例（分页）
     *
     * @param processInstanceQueryReq 按条件查询流程实例请求
     * @return 流程实例分页响应
     */
    PageVO<HistoricProcInstResp> queryProcessInstanceListPage(ProcessInstanceQueryReqDto processInstanceQueryReq, long pageNum, long pageSize);

    /**
     * 流程实例导出
     *
     * @param req 导出请求
     */
    void exportProcessInstance(ProcessInstanceExportReq req);

    /**
     * 流程实例导出
     * 包含表单数据
     *
     * @param req 导出请求
     */
    void exportProcessInstanceAndForm(ProcessInstanceExportReq req);

    /**
     * 分页查询导出记录
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageVO<ProcessInstanceExportHistoryDto> queryExportHistory(long pageNum, long pageSize);

    /**
     * 查询最近发起的流程列表
     *
     * @param maxNumber 最多查询数量
     *
     * @return 最近使用的模型列表
     */
    List<RecentUseModelResp> queryRecentUseModelList(int maxNumber);
}
