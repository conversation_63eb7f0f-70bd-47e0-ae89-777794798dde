package com.mi.oa.infra.mibpm.application.userconfig.service;

import com.mi.oa.infra.mibpm.application.message.dto.req.ApplyPermissionReq;
import com.mi.oa.infra.mibpm.application.message.dto.req.DeletePermissionReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.AssigneePermissionResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.DeptPermissionResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.PermissionRecordsDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RequirePermissionDto;
import com.mi.oa.infra.oaucf.core.dto.PageVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/15 16:46
 */

public interface UserDashboardPermissionService {

    /**
     * 搜索用户的部门权限（分页）
     */
    PageVO<RequirePermissionDto> queryPermissionPage(long pageNum, long pageSize, String deptId);

    /**
     * 查询用户权限操作清单（分页）
     */
    PageVO<PermissionRecordsDto> queryPermissionRecordsPage(long pageNum, long pageSize, String deptId);

    /**
     * 申请权限
     */
    void applyPermission(ApplyPermissionReq applyPermissionReq);

    /**
     * 删除权限
     */
    void removePermission(DeletePermissionReq deletePermissionReq);

    /**
     * 查询权限是否存在
     */
    Boolean isUserPermission(String userOrpid, String permissionId);

    /**
     * 初始默认权限(admin)
     */
    void initUserPermission();

    /**
     * 查询部门权限(admin)-deptId
     */
    List<DeptPermissionResp> queryDeptId(String userOrpid);

    /**
     * 查询指数下级权限(admin)-assigneeId
     */
    List<AssigneePermissionResp> queryAssigneeId(String userOrpid);

    /**
     * 删除部门权限(admin)
     */
    void removeDeptId(String userOrpid, String deptId);

    /**
     * 删除直属下级权限(admin)
     */
    void removeAssigneeId(String userOrpid, String permissionUserId);

    void removeDashboard(String userOrpid, String permissionUserId);

    /**
     * 添加部门权限(admin)
     */
    void addDeptId(String userOrpid, String deptId);

    /**
     * 添加直属下级权限(admin)
     */
    void addAssigneeId(String userOrpid, String permissionUserId);

    void initUserPermission(String userId);
}
