package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 表单内容作废请求
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "表单内容作废请求", description = "作废暂存的表单内容")
public class ProcessFormAbortReq {

    @NotBlank(message = "模型编码不能为空")
    @ApiModelProperty(value = "模型编码", required = true)
    private String modelCode;

    @ApiModelProperty(value = "用户名称", required = false)
    private String userName;


}
