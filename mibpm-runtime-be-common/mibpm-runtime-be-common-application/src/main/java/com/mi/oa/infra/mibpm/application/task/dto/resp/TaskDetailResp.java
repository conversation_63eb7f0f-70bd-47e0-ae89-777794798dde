package com.mi.oa.infra.mibpm.application.task.dto.resp;

import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.ApprovalStrategy;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/22 16:14
 */
@Data
@ApiModel(value = "审批任务详情")
public class TaskDetailResp {

    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("任务名称")
    private String taskName;
    @ApiModelProperty("任务定义ID")
    private String taskDefinitionId;
    @ApiModelProperty("任务定义KEY")
    private String taskDefinitionKey;
    @ApiModelProperty("优先级")
    private Integer priority;
    @ApiModelProperty("任务处理人")
    private BpmUser assignee;
    @ApiModelProperty("任务候选人")
    protected List<BpmUser> candidates;
    @ApiModelProperty("流程定义ID")
    private String processDefinitionId;
    @ApiModelProperty("流程key")
    private String modelCode;
    private String modelName;
    private String modelEnName;
    @ApiModelProperty("分类编码")
    private String categoryCode;
    @ApiModelProperty("流程实例ID")
    private String processInstanceId;
    @ApiModelProperty("业务唯一编码")
    protected String businessKey;
    @ApiModelProperty("流程发起人")
    private BpmUser processInstanceStartUser;
    @ApiModelProperty("流程实例名称")
    private String processInstanceName;
    @ApiModelProperty("流程状态")
    protected ProcessInstanceStatus processInstanceStatus;
    @ApiModelProperty("范围标识符")
    private String scopeId;
    @ApiModelProperty("任务创建时间")
    private ZonedDateTime createTime;
    @ApiModelProperty("任务结束时间")
    private ZonedDateTime endTime;
    @ApiModelProperty("任务过期时间")
    private ZonedDateTime dueDate;
    @ApiModelProperty("表单")
    private Form form;
    @ApiModelProperty("审批任务列表")
    private List<Task> taskList;
    @ApiModelProperty("任务节点配置")
    private TaskConfig taskConfig;
    @ApiModelProperty("新老流程标记 0新流程 1老流程非自由表单 2老流程自由表单")
    private Integer oldType;
    protected Boolean disableAppOperation;
    @ApiModelProperty("收藏状态")
    protected String pinStatus;
    @ApiModelProperty("重新提交流程链接")
    protected Link submitLink;
    protected UserTaskSignType signType;
    protected Boolean isVetoUser;

    @Data
    @ApiModel(value = "表单")
    public static class Form {
        @ApiModelProperty("表单模型")
        private Map<String, Object> schema;
        @ApiModelProperty("表单数据")
        private Map<String, Object> data;
        @ApiModelProperty("跳转链接")
        private Link link;
        @ApiModelProperty("移动端xml模板")
        private String appContent;
        @ApiModelProperty("pc端xml")
        private String pcContent;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Link {
        private String pcLink;
        private String appLink;
    }

    @Data
    @ApiModel(value = "审批任务")
    public static class Task {
        @ApiModelProperty("任务ID")
        protected String taskId;
        @ApiModelProperty("任务名称")
        protected String taskName;
        @ApiModelProperty("任务定义ID")
        protected String taskDefinitionId;
        @ApiModelProperty("任务定义KEY")
        protected String taskDefinitionKey;
        @ApiModelProperty("优先级")
        protected Integer priority;
        @ApiModelProperty("任务处理人")
        protected BpmUser assignee;
        @ApiModelProperty("任务候选人")
        protected List<BpmUser> candidates;
        @ApiModelProperty("范围标识符")
        protected String scopeId;
        @ApiModelProperty("任务创建时间")
        protected ZonedDateTime createTime;
        @ApiModelProperty("任务创建人")
        protected BpmUser createUser;
        @ApiModelProperty("任务结束时间")
        protected ZonedDateTime endTime;
        @ApiModelProperty("任务过期时间")
        protected ZonedDateTime dueDate;
        @ApiModelProperty("任务操作")
        private UserTaskOperation operation;
        @ApiModelProperty("评论")
        private String comment;
        @ApiModelProperty("处理任务的客户端")
        private ClientEnum client;
        @ApiModelProperty("审批方式")
        private UserTaskSignType signType;
        @ApiModelProperty("是否预测任务")
        private boolean isPredict;
        @ApiModelProperty("活动节点类型")
        private ActivityTypeEnum activityType;
        private Boolean reviewed;
    }

    @Data
    @ApiModel(value = "任务节点配置")
    public static class TaskConfig {
        @ApiModelProperty("审批操作列表")
        private List<UserTaskOperation> operationList;
        @ApiModelProperty("参数列表")
        private Map<String, Object> variables;
        private List<ApprovalStrategy> strategyList;
        private List<UserTaskOperation> requiredComments;
    }
}
