package com.mi.oa.infra.mibpm.application.apicall.dto.reps;

import java.util.List;

/**
 * 表单获取数据源响应
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/14 19:00
 */
public class ContentReps {

    private List<Option> options;

    public ContentReps() {
    }

    public ContentReps(List<Option> options) {
        this.options = options;
    }

    public List<Option> getOptions() {
        return options;
    }

    public void setOptions(List<Option> options) {
        this.options = options;
    }

    public static class Option {
        private String label;
        private String value;
        private Boolean defaultChecked;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Boolean getDefaultChecked() {
            return defaultChecked;
        }

        public void setDefaultChecked(Boolean defaultChecked) {
            this.defaultChecked = defaultChecked;
        }
    }
}
