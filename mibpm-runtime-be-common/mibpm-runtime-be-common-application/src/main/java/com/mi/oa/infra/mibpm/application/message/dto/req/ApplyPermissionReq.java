package com.mi.oa.infra.mibpm.application.message.dto.req;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/19 9:35
 */
@Data
public class ApplyPermissionReq {

    @SerializedName("user_da9864fd57c9")
    private String userOrpid;
    @SerializedName("select_4afc763d32cc")
    private PermissionIdType type;
    @SerializedName("user_79d07e0be6ee")
    private String userPermissionId;
    @SerializedName("dept_bb0e19de5d63")
    private List<String> deptPermissionId;
    @SerializedName("textarea_7ad88501a0a7")
    private String reason;
}
