package com.mi.oa.infra.mibpm.application.message.dto.req;

import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/08/01 09:39
 */
@Data
public class DeletePermissionReq {
    /**
     *用户名Id
     */
    private String userOrpid;

    /**
     * 权限Id
     */
    private String permissionId;

    /**
     * 申请权限类型：DEPT_TYPE-部门权限，USER_TYPE-人员权限
     */
    private PermissionIdType permissionIdType;
}
