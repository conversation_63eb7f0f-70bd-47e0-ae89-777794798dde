package com.mi.oa.infra.mibpm.application.mitask.dto.req;

import lombok.Data;

import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2024/3/8 17:17
 */
@Data
public class MiTaskMigrateReq {

    /**
     * 开始时间
     */
    private long startTime;
    /**
     * 结束时间
     */
    private long endTime;
    /**
     * 流程ID集合
     */
    private List<String> instanceIds;
    /**
     * 根据失败列表重试
     */
    private boolean retryByFailList;
}
