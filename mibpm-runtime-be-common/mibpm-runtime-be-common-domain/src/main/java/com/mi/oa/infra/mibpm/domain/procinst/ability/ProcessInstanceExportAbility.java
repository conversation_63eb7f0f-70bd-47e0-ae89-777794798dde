package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.common.model.ProcessInstanceExportReq;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceExportHistoryDo;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/7 8:17 PM
 **/
public interface ProcessInstanceExportAbility {

    /**
     * 流程实例导出
     *
     * @param req 导出请求
     * @return 导出文件的url
     */
    ProcessInstanceExportHistoryDo exportProcessInstance(ProcessInstanceExportReq req);

    /**
     * 流程表单数据导出
     *
     * @param req 导出请求
     * @return 导出文件的url
     */
    ProcessInstanceExportHistoryDo exportProcessInstanceAndForm(ProcessInstanceExportReq req);
}
