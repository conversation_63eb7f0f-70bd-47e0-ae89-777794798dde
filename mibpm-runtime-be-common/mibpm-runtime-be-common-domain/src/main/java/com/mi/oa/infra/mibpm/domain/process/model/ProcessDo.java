package com.mi.oa.infra.mibpm.domain.process.model;

import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 流程基本信息
 *
 * @auther wangchen21
 * @data 2021-03-24 17:44:00
 */
@Data
public class ProcessDo extends DomainObject<ProcessDo> {

    /**
     * 应用编码
     */
    private String appCode;
    /**
     * 分类编码
     */
    private String categoryCode;
    /**
     * 模型名称
     */
    private String name;
    /**
     * 英文名
     */
    private String enName;
    /**
     * 模型编码
     */
    private String modelCode;
    /**
     * 模型启用状态
     */
    private ProcessEnableStatus enable;
    /**
     * 负责人
     */
    private List<String> owners;
    /**
     * 可使用范围配置
     */
    private UsableConfig usableConfig;
    /**
     * 可发起配置
     */
    private VisibleConfig visibleConfig;
    /**
     * 模型描述
     */
    private String description;
    /**
     * 模型英文描述
     */
    private String descriptionEn;
    /**
     * 备注
     */
    private String comment;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private ZonedDateTime createTime;
    /**
     * 更新时间
     */
    private ZonedDateTime updateTime;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 是否来源老系统 0否 1jsp 2自由表单
     */
    private Integer fromOld;
    /**
     * 迁移来的第一个部署版本号，当前版本>此参数 即为新流程
     */
    private Integer migrationProcDefVersion;

    private List<String> businessOwner;
    private String prodAppCode;
    protected String grade;
    protected String scope;
    protected Boolean releaseToProd;
    private String ownerDept;

    @Override
    protected boolean sameIdentityAs(ProcessDo object) {
        return this.equals(object);
    }

}
