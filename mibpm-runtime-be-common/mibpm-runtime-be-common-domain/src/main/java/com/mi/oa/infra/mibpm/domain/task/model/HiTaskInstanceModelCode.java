package com.mi.oa.infra.mibpm.domain.task.model;

import lombok.*;

import java.time.ZonedDateTime;

/**
 * 审批历史记录表
 *
 * <AUTHOR>
 * @date 2022/04/27 11:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class HiTaskInstanceModelCode{

    /**
     * 审批人
     */
    protected String assignee;

    /**
     * 模型编码
     */
    protected String modelCode;

    /**
     * 更新时间
     */
    protected ZonedDateTime updateTime;
}
