package com.mi.oa.infra.mibpm.domain.procinst.model;

import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @Date 2024/7/16 17:19
 */
@Data
public class PermissionRecordsDo {
    /**
     *用户名
     */
    private String userOrpid;

    /**
     * 操作人详细信息
     */
    private BpmUser createUser;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 数据权限Id
     */
    private String permissionId;

    /**
     * 清单Id
     */
    private String businessKey;

    /**
     * 变动时间
     */
    private ZonedDateTime createTime;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限类型
     */
    private PermissionIdType permissionIdType;
}
