package com.mi.oa.infra.mibpm.domain.mitask.service;

import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/10
 * @Description MiTask领域对象增删改
 */
public interface MiTaskDomainService {
    /**
     * @param miTaskDo 要创建的MiTaskDo对象
     * @return void
     * <AUTHOR>
     * @date 2024/10/11
     * @description 创建MiTaskDo（infra层透传）
     **/
    void createMiTask(MiTaskDo miTaskDo);

    /**
     * @param miTaskDos 要创建的MiTaskDo对象
     * @return void
     * <AUTHOR>
     * @date 2024/10/11
     * @description 创建MiTaskDo（infra层透传）
     **/
    void createMiTaskBatch(List<MiTaskDo> miTaskDos);

    /**
     * @param miTaskDo 要更新的MiTaskDo对象
     * @return void
     * <AUTHOR>
     * @date 2024/10/11
     * @description 更新MiTaskDo（infra层透传）通过procInstId和taskId更新
     **/
    void updateMiTask(MiTaskDo miTaskDo);

    /**
     * @param miTaskDo 包含更新信息的MiTaskDo对象
     * @return void
     * <AUTHOR>
     * @date 2024/10/11
     * @description 更新MiTaskProcessInstPo（infra层透传）通过procInstId和taskId更新
     **/
    void updateMiTaskInstPo(MiTaskDo miTaskDo);

    /**
     * @param miTaskDoList 包含更新信息的MiTaskDo对象列表
     * @return void
     * <AUTHOR>
     * @date 2024/10/11
     * @description 批量更新MiTaskProcessInstPo（infra层透传）通过procInstId和taskId更新
     **/
    void updateMiTaskInstPoBatch(List<MiTaskDo> miTaskDoList);

    /**
     * @param miTaskDo 要删除的MiTaskDo对象
     * @return void
     * <AUTHOR>
     * @date 2024/10/11
     * @description 删除MiTaskDo（infra层透传）通过procInstId和taskId删除
     **/
    void deleteCandidateTask(MiTaskDo miTaskDo);

}
