package com.mi.oa.infra.mibpm.domain.userconfig.ability;

import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserConfigDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * 用户个性化配置能力
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:16
 */
public interface UserConfigAbility {

    /**
     * 新建委托
     *
     * @param userConfigDo
     */
    void addDelegation(UserConfigDo userConfigDo);

    /**
     * 取消委托
     *
     * @param userConfigDo
     */
    void updateDelegation(UserConfigDo userConfigDo);

    /**
     * 更新已经失效的委托状态
     *
     * @param delegationConfigs
     */
    void updateInvalidDelegation(List<DelegationConfig> delegationConfigs);

    /**
     * 查询委托列表
     *
     * @param query
     * @param page
     * @param pageSize
     * @return
     */
    PageModel<DelegationConfig> queryDelegationDo(DelegationConfig query, int page, int pageSize);

    /**
     * 查询用户的某个流程的委托人
     *
     * @param userName
     * @param modelCode
     * @param initiatorOrgId
     * @return userName
     */
    String queryDelegationUser(String userName, String modelCode, String initiatorOrgId);

    /**
     * 取消所有委托到其他人的委托关系
     *
     * @param userName 用户名
     */
    void cancelAllDelegationConfig(String userName);

    /**
     * 转移委托关系到新的被委托人
     *
     * @param userName
     * @param newUserName 新的被委托人
     */
    void transferDelegationByUser(String userName, String newUserName);

    /**
     * 更新当前登录人连续审批设置
     *
     * @param userConfigDo
     */
    void updateAutoNextApproval(UserConfigDo userConfigDo);

    /**
     * 设置消息发送规则
     *
     * @param userConfigDo
     */
    void addMessagePushRule(UserConfigDo userConfigDo);

    /**
     * 更新消息发送规则
     *
     * @param userConfigDo
     */
    void updateMessagePushRule(UserConfigDo userConfigDo);

    /**
     * 新增查看权限
     *
     * @param processViewAuthConfig 流程可见配置
     * @return
     */
    void saveProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig);

    /**
     * 检查是否含有查看权限
     *
     * @param processViewAuthConfig 流程可见配置
     * @return
     */
    boolean hasProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig);

    /**
     * 获取用户默认签名
     *
     * @param userId
     * @return
     */
    String getUserDefaultSignature(String userId);
}
