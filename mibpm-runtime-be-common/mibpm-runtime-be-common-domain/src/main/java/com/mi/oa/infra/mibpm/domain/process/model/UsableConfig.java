package com.mi.oa.infra.mibpm.domain.process.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流程可使用范围配置
 *
 * <AUTHOR>
 * @date 2021/12/16 11:38 AM
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UsableConfig {

    private Boolean webEnabled;
    private Boolean businessEnabled;
    private Boolean appEnabled;
    private String appUrl;
    private String webUrl;
}
