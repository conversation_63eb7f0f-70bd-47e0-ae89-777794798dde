package com.mi.oa.infra.mibpm.domain.userconfig.service;

import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserConfigDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * 用户配置
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:17
 */
public interface UserConfigDomainService {

    /**
     * 新建委托
     * @param userConfigDo
     */
    void addDelegation(UserConfigDo userConfigDo);

    /**
     * 查询委托列表
     * @param delegationConfigQuery
     * @param page
     * @param pageSize
     * @return
     */
    PageModel<DelegationConfig> queryDelegationPageList(DelegationConfig delegationConfigQuery, int page, int pageSize);

    /**
     * 查询用户的某个流程的委托人
     *
     * @param userName
     * @param modelCode
     * @param initiatorOrgId
     * @return userName 若不存在 则返回null
     */
    String queryDelegationUser(String userName, String modelCode, String initiatorOrgId);


    /**
     * 取消所有委托
     *
     * @param userName 用户名
     */
    void cancelAllDelegationConfig(String userName);

    /**
     * 转移委托关系到新的被委托人
     * @param userName
     * @param newUserName 新的被委托人
     */
    void transferDelegationByUser(String userName, String newUserName);

    /**
     * 获取连续审批设置
     *
     * @param userId 用户ID
     * @return 用户配置
     */
    UserConfigDo queryAutoNextApproval(String userId);

    /**
     * 设置消息发送规则
     * @param userConfigDo
     */
    void addMessagePushRule(UserConfigDo userConfigDo);

    /**
     * 获取消息发送规则
     * @param userId
     * @return 消息推送规则
     */
    UserConfigDo queryMessagePushRule(String userId);

    /**
     * 更新用户设置
     *
     * @param userConfigDo
     */
    void updateUserConfig(UserConfigDo userConfigDo);

    /**
     * 新增查看权限
     *
     * @param processViewAuthConfig 流程可见配置
     * @return
     */
    void saveProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig);

    /**
     * 检查是否含有查看权限
     *
     * @param processViewAuthConfig 流程可见配置
     * @return
     */
    boolean hasProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig);

    /**
     * 查询查看权限
     *
     * @param processViewAuthConfig 流程可见配置
     * @return
     */
    List<ProcessViewAuthConfig> queryProcessViewAuth(ProcessViewAuthConfig processViewAuthConfig);

    /**
     * 获取用户默认签名
     *
     * @param userId
     * @return
     */
    String getUserDefaultSignature(String userId);
}
