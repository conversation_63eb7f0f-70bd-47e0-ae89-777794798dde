package com.mi.oa.infra.mibpm.domain.mitask.service;

import com.mi.flowable.external.api.ProcessInstanceCheckRepresentation;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskMetaDo;

public interface MiTaskMetaDomainService {

    /**
     * 检查并处理给定的任务元数据对象
     *
     * @param miTaskMetaDo 任务元数据对象
     */
    void checkMetaDo(MiTaskMetaDo miTaskMetaDo);

    /**
     * 检查给定的MiTaskDo对象
     *
     * @param miTaskDo 要检查的MiTaskDo对象
     */
    void checkMiTaskDo(MiTaskDo miTaskDo);

    /**
     * 检查MiTaskDo对象的差异
     *
     * @param miTaskDo 需要检查的MiTaskDo对象
     * @return
     */
    ProcessInstanceCheckRepresentation checkMiTaskDoDiff(ProcessInstanceCheckRepresentation miTaskDo);

    /**
     * 根据代码和租户ID获取MiTaskMetaDo对象
     *
     * @param code 任务代码
     * @param tenantId 租户ID
     * @return 对应的MiTaskMetaDo对象
     */
    MiTaskMetaDo getByCode(String code, String tenantId);
}
