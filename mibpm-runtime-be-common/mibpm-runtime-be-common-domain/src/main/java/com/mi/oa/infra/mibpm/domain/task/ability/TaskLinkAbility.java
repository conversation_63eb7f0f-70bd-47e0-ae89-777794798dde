package com.mi.oa.infra.mibpm.domain.task.ability;

import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.ProcessWrapper;

/**
 * <AUTHOR>
 * @date 2023/10/18 17:28
 */
public interface TaskLinkAbility {
    /**
     * 获取任务链接
     *
     * @param taskDo
     * @return 链接对象
     */
    TaskLink getTaskLink(TaskDo taskDo, ProcessWrapper processWrapper);

    /**
     * 是否支持该任务类型
     *
     * @param taskDo
     * @return
     */
    boolean support(TaskDo taskDo, ProcessWrapper processWrapper);
}
