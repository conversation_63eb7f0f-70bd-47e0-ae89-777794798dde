package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.TaskAssignee;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/9 19:13
 */
public interface ProcessInstanceFillAbility {

    /**
     * 填充流程实例发起人
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void fillProcessInstanceStartUserId(ProcessInstanceDo processInstanceDo);

    /**
     * 填充流程实例的操作人
     *
     * @param processInstanceDo 流程实例领域对象
     * @param operator          操作人
     */
    void fillProcessInstanceOperator(ProcessInstanceDo processInstanceDo, String operator);

    /**
     * 填充流程实例的操作人
     *
     * @param processInstanceDo 流程实例领域对象
     * @param operator          操作人
     */
    void fillProcessInstanceOperator(ProcessInstanceDo processInstanceDo, BpmUser operator);

    /**
     * 填充流程实例名称
     *
     * @param processInstanceDo 流程实例领域对象
     * @param formData 表单数据
     */
    void fillProcessInstanceName(ProcessInstanceDo processInstanceDo, Map<String, Object> formData);

    /**
     * 填充流程实例业务唯一编码
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void fillProcessInstanceBusinessKey(ProcessInstanceDo processInstanceDo);

    /**
     * 填充流程实例模型编码
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void fillProcessInstanceModelCode(ProcessInstanceDo processInstanceDo);

    /**
     * 填充流程实例的流程定义ID
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void fillProcessInstanceProcDefId(ProcessInstanceDo processInstanceDo);

    /**
     * 填充任务实例的各任务节点的处理人
     *
     * @param processInstanceDo 流程实例领域对象
     * @param taskAssignees     各任务节点的处理人
     */
    void fillProcessInstanceTaskAssignee(ProcessInstanceDo processInstanceDo, List<TaskAssignee> taskAssignees);

    /**
     * 填充流程实例变量
     *
     * @param processInstanceDo 流程实例领域对象
     * @param formData          表单数据
     */
    void fillProcessVariables(ProcessInstanceDo processInstanceDo, Map<String, Object> formData);
}
