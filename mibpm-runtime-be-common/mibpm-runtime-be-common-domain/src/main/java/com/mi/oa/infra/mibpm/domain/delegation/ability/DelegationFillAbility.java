package com.mi.oa.infra.mibpm.domain.delegation.ability;

import com.mi.oa.infra.mibpm.common.enums.DelegationStatusEnum;
import com.mi.oa.infra.mibpm.common.model.LeaveDelegationInfo;
import com.mi.oa.infra.mibpm.domain.delegation.model.DelegationInfoDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/23
 * @Description
 */
public interface DelegationFillAbility {
    void fillDelegationInfoDetails(DelegationInfoDo delegationInfoDo);

    void checkDelegationInfo(DelegationInfoDo delegationInfoDo);

    void applyDelegationAll(DelegationInfoDo delegationInfoDo);

    void applyDelegationPartial(DelegationInfoDo delegationInfoDo);

    void cancelDelegation(List<Long> ids);

    void updateDelegationStatusByIds(List<Long> ids, DelegationStatusEnum status);

    LarkMessageDo buildLarkMessage(LeaveDelegationInfo leaveDelegationInfo);

    void updateDelegationStatus(String processInstId, DelegationStatusEnum status, ZonedDateTime startTime, ZonedDateTime endTime);

}
