package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.application.proinst.dto.req.FormStoragReq;

import java.util.Map;


public interface FormContentCacheAbility {

    /**
     * 缓存表单内容
     * @param req
     * @return
     */
    boolean cacheFormContent(FormStoragReq req);

    /**
     * 获取表单缓存内容
     * @param modelCode
     * @param userName
     * @return
     */
    Map<String, Object> getCachedFormContent(String modelCode, String userName);

    /**
     * 删除缓存内容
     * @param modelCode
     * @param userId
     * @return
     */
    boolean removeCachedFormContent(String modelCode, String userId);
}
