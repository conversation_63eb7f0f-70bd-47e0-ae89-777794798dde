package com.mi.oa.infra.mibpm.domain.mitask.model;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ExternalAppInfoDo {

    private String id;

    private String appCode;

    private String appName;

    private String appDesc;

    private String systemUrl;

    private String createAppId;

    private String createAppKey;

    private String callbackAppId;

    private String callbackAppKey;

    private LocalDateTime createTime;

    private Integer status;

    private String tenantId;
}
