package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Builder;
import lombok.Data;

/**
 *
 * 用户任务更新事件
 *
 *
 * @author: qiuzhipeng
 * @Date: 2022/10/28 16:28
 */

@Data
@Builder
public class UserTaskUpdateEvent extends Event {

    /**
     * 任务处理人
     */
    protected String assignee;
    /**
     * 任务实例ID
     */
    protected String taskId;
    /**
     * 任务名称
     */
    protected String taskName;
    /**
     * 任务定义Key
     */
    protected String taskDefinitionKey;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;

    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
}
