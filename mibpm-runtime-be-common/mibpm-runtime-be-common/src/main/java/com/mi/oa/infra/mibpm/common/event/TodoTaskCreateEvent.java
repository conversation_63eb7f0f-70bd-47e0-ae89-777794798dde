package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/12/2 16:28
 */

@Data
@NoArgsConstructor
public class TodoTaskCreateEvent extends Event {

    /**
     * 任务处理人
     */
    protected String assignee;
    /**
     * 任务实例ID
     */
    protected String taskId;
    /**
     * 任务名称
     */
    protected String taskName;
    /**
     * 任务定义Key
     */
    protected String taskDefinitionKey;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;

    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
    /**
     * 候选人
     */
    protected List<String> candidates;
}
