package com.mi.oa.infra.mibpm.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.lang.reflect.Type;
import java.util.Set;

/**
 *  自定义gson序列化 不对特殊字符进行转义
 *
 * <AUTHOR>
 * @date 2024/9/9 17:06
 */
public class CustomGsonUtils {

    private static Gson filterNullGson = (new GsonBuilder()).disableHtmlEscaping().enableComplexMapKeySerialization().setDateFormat("yyyy-MM-dd HH:mm:ss:SSS").create();
    private static Gson nullableGson = (new GsonBuilder()).disableHtmlEscaping().enableComplexMapKeySerialization().serializeNulls().setDateFormat("yyyy-MM-dd HH:mm:ss:SSS").create();

    protected CustomGsonUtils() {
    }

    public static String toJsonWtihNullField(Object obj) {
        return nullableGson.toJson(obj);
    }

    public static String toJsonFilterNullField(Object obj) {
        return filterNullGson.toJson(obj);
    }

    public static <T> T fromJson(String json, Type type) {
        return nullableGson.fromJson(json, type);
    }

    public static <T> T convert(Object source, Class<T> clz) {
        String json = toJsonFilterNullField(source);
        return fromJson(json, clz);
    }

    public static String getString(JsonObject jsonObject, String name) {
        JsonElement element = jsonObject.get(name);
        return element != null && !element.isJsonNull() && element.isJsonPrimitive() ? element.getAsString() : "";
    }

    public static long getLongValue(JsonObject jsonObject, String name) {
        JsonElement element = jsonObject.get(name);
        return element != null && !element.isJsonNull() && element.isJsonPrimitive() ? element.getAsLong() : 0L;
    }

    public static Integer getInteger(JsonObject jsonObject, String name) {
        JsonElement element = jsonObject.get(name);
        return element != null && !element.isJsonNull() && element.isJsonPrimitive() ? element.getAsInt() : null;
    }

    public static int getIntValue(JsonObject jsonObject, String name) {
        JsonElement element = jsonObject.get(name);
        return element != null && !element.isJsonNull() && element.isJsonPrimitive() ? element.getAsInt() : 0;
    }

    public static boolean getBooleanValue(JsonObject jsonObject, String name) {
        JsonElement element = jsonObject.get(name);
        return element != null && element.isJsonPrimitive() ? element.getAsBoolean() : false;
    }

    public static JsonObject parseObject(String data) {
        JsonElement jsonElement = (new JsonParser()).parse(data);
        return jsonElement != null && jsonElement.isJsonObject() ? jsonElement.getAsJsonObject() : null;
    }

    public static JsonArray parseAsJsonArray(String data) {
        JsonElement jsonElement = (new JsonParser()).parse(data);
        return jsonElement != null && jsonElement.isJsonArray() ? jsonElement.getAsJsonArray() : null;
    }

    public static Set<String> keySet(JsonObject jsonObject) {
        return jsonObject.keySet();
    }

    public static JsonObject getJsonObject(JsonObject jsonObject, String member) {
        JsonElement jsonElement = jsonObject.get(member);
        return jsonElement != null && jsonElement.isJsonObject() ? jsonElement.getAsJsonObject() : null;
    }
}
