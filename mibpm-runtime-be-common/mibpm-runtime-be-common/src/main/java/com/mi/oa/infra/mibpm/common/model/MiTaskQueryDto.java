package com.mi.oa.infra.mibpm.common.model;

import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiTaskQueryDto {
    private String currentUserName;

    private String categoryCode;
    private String modelCode;
    private String processInstanceName;
    private String userName;

    private Long taskCreateTime;

    private Long startTimeBegin;
    private Long startTimeEnd;

    private ProcessInstanceStatus processInstanceStatus;
    private List<ProcessInstanceStatus> statusList;

    private Boolean byStartTimeAsc;
    private Boolean byTaskCreateTimeAsc;
    private Boolean byTaskDueDateAsc;
    private Boolean byTaskEndTimeAsc;

    private ClientEnum clientType;

    private Integer pageNum;
    private Integer pageSize;
}
