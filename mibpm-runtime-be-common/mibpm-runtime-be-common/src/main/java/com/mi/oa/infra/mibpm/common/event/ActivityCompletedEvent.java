package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 活动节点结束事件
 *
 * @author: qiuzhipeng
 * @Date: 2022/2/25 16:42
 */
@Data
@Builder
public class ActivityCompletedEvent extends Event {

    /**
     * 任务名称
     */
    protected String taskName;
    /**
     * 任务定义key
     */
    protected String taskDefinitionKey;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;

    /**
     * 流程定义ID
     */
    protected String processDefinitionId;

    /**
     * 活动类型
     */
    protected String activityType;

    /**
     * 表单数据
     */
    protected Map<String, Object> formData;
}
