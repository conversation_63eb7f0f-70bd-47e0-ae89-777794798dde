package com.mi.oa.infra.mibpm.common.model;

import com.mi.oa.infra.mibpm.common.enums.DelegationStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/27
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CancelInfo {
    private Long id;
    private String delegatorId;
}