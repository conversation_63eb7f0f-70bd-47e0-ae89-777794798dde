package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Builder;
import lombok.Data;

/**
 * 催办事件
 *
 * @author: qiuzhipeng
 * @Date: 2022/2/25 16:28
 */

@Data
@Builder
public class UrgeEvent extends Event {

    /**
     * 催办对象
     */
    protected String targetUser;

    /**
     * 操作人
     */
    protected String operator;

    /**
     * 任务实例ID
     */
    protected String taskId;

    /**
     * 流程实例ID
     */
    protected String processInstanceId;

    protected String taskDefKey;
}
