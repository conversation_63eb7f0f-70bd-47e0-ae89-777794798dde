package com.mi.oa.infra.mibpm.common.model;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * 查询流程实例条件
 *
 * <AUTHOR>
 * @date 2022/2/9 18:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessInstanceQueryDto {
    protected String categoryCode;
    protected String modelCode;
    protected List<String> inModelCodes;
    protected String processInstanceName;
    protected String processInstanceId;
    protected String businessKey;
    protected ProcessInstanceStatus status;
    protected List<ProcessInstanceStatus> inStatus;
    protected ZonedDateTime createTimeStart;
    protected ZonedDateTime createTimeEnd;
    protected String completeTimeStart;
    protected String completeTimeEnd;
    protected String currentUserId;
    protected List<String> startUserIds;
    protected String currentUserName;
    protected Boolean byCreateTimeAsc;
    protected boolean needVariables;
    // 是否放开时间查询
    protected boolean unlimitedTime;

    /**
     * 是否走MiTask 查询
     */
    protected boolean newQueryFlag;

    public MiTaskQueryDto convertToMiTaskQuery(long pageNum, long pageSize) {
        return MiTaskQueryDto.builder()
                .currentUserName(currentUserName)
                .categoryCode(categoryCode)
                .modelCode(modelCode)
                .processInstanceName(processInstanceName)
                .processInstanceStatus(this.status)
                .userName(currentUserName)
                .startTimeBegin(createTimeStart == null ? null : createTimeStart.toEpochSecond() * 1000)
                .startTimeEnd(createTimeEnd == null ? null : createTimeEnd.toEpochSecond() * 1000)
                .statusList(inStatus)
                .byTaskCreateTimeAsc(byCreateTimeAsc)
                .pageNum((int) pageNum)
                .pageSize((int) pageSize)
                .build();
    }
}
