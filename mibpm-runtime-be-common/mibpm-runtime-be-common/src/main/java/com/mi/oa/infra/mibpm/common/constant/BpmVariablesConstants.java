package com.mi.oa.infra.mibpm.common.constant;

/**
 * 系统内置变量
 *
 * <AUTHOR>
 * @description
 * @date 2022/3/8 5:55 PM
 **/
public class BpmVariablesConstants {

    /**
     * 流程定义名称
     */
    public static final String VARIABLE_PROC_DEF_NAME = "processDefinitionName";
    /**
     * 流程实例发起人
     */
    public static final String VARIABLE_INITIATOR = "initiator";
    /**
     * 流程发起人名称
     */
    public static final String VARIABLE_INITIATOR_USER = "initiatorUser";
    /**
     * 流程发起人uid
     */
    public static final String VARIABLE_INITIATOR_USER_ID = "initiatorUserId";
    /**
     * 流程发起人所在组织
     */
    public static final String VARIABLE_INITIATOR_ORG_ID = "initiatorOrgId";
    /**
     * 流程实例发起时间
     */
    public static final String VARIABLE_INITIATE_TIME = "initiateTime";
    /**
     * 流程实例ID
     */
    public static final String VARIABLE_PROC_INST_ID = "processInstanceId";
    /**
     * 流程实例标题
     */
    public static final String VARIABLE_PROC_INST_NAME = "title";
    /**
     * 加签处理完后返回的节点ID
     */
    public static final String VARIABLE_SIGN_RETURN_ACTIVITY_ID = "SIGN_TARGET_NODE";
    /**
     * 流程实例的状态
     */
    public static final String VARIABLE_PROC_INST_STATUS = "status";
    /**
     * 是否开启自动跳过表达式
     */
    public static final String FLOWABLE_SKIP_EXPRESSION_ENABLED = "_FLOWABLE_SKIP_EXPRESSION_ENABLED";
    /**
     * 摘要配置
     */
    public static final String VARIABLE_SUMMARY = "summary";
    /**
     * 快捷审批配置
     */
    public static final String VARIABLE_FAST_APPROVAL = "fastApproval";
    /**
     * 摘要字段最大个数
     */
    public static final int SUMMARY_MAX_COUNT = 5;
    /**
     * 任务加签原任务id
     */
    public static final String VARIABLE_SIGN_TASK = "ENGINE_SIGN_TASK";
    /**
     * 任务加签标记 true/false
     */
    public static final String VARIABLE_SIGN_FLAG = "ENGINE_SIGN_FLAG";
    /**
     * 任务加签加签类型
     */
    public static final String VARIABLE_SIGN_TYPE = "ENGINE_SIGN_TYPE";
    /**
     * 任务加签子任务id
     */
    public static final String VARIABLE_SIGN_SUB_TASK = "ENGINE_SIGN_SUB_TASK_ID";
    /**
     * 流程变量审批人
     */
    public static final String VARIABLE_ASSIGNEE = "assignee";
    /**
     * task中VariableLocal保存的原审批人信息
     */
    public static final String VARIABLE_ORIGINAL_ASSIGNEE = "originalAssignee";
    /**
     * 流程变量审批人集合
     */
    public static final String VARIABLE_SYS_MULTI_ASSIGNEES = "sys_multiinstance_assignees";
    /**
     * 一票否决特权人
     */
    public static final String VARIABLE_SYS_VETO_ASSIGNEES = "sys_veto_assignees";
    /**
     * 流程变量-活跃实例数
     */
    public static final String VARIABLE_NUMBER_ACTIVE_INSTANCES = "nrOfActiveInstances";
    /**
     * 流程变量-已完成实例数
     */
    public static final String VARIABLE_NUMBER_COMPLETED_INSTANCES = "nrOfCompletedInstances";
    /**
     * 流程变量-总实例数
     */
    public static final String VARIABLE_NUMBER_TOTAL_INSTANCES = "nrOfInstances";
    /**
     * 流程变量-循环计数
     */
    public static final String VARIABLE_LOOP_COUNTER = "loopCounter";
    public static final String VARIABLE_VOTE_OPERATION = "voteOperation";
    public static final String VARIABLE_VOTE_AGREE_COUNT = "agreeCount";
    public static final String VARIABLE_VOTE_REJECT_COUNT = "rejectCount";
    public static final String VARIABLE_VOTE_WAIVE_COUNT = "waiveCount";
    public static final String VARIABLE_VOTE_AGREE_RATE = "agreeRate";
    public static final String VARIABLE_VOTE_REJECT_RATE = "rejectRate";
    public static final String VARIABLE_VOTE_WAIVE_RATE = "waiveRate";
    public static final String VARIABLE_VOTE_IS_VETO_TASK = "isVetoTask";
    public static final String VARIABLE_VOTE_MUST_REJECTED = "voteMustRejected";
    public static final String VARIABLE_VOTE_VETO_EXECUTION_IDS = "vetoExecutionIds";
}
