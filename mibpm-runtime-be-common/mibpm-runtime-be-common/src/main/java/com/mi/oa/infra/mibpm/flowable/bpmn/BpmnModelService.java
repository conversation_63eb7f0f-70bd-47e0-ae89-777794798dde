package com.mi.oa.infra.mibpm.flowable.bpmn;

import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.ReceiveTask;
import org.flowable.bpmn.model.ServiceTask;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.bpmn.model.Task;
import org.flowable.bpmn.model.UserTask;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import java.util.List;
import java.util.Set;

/**
 * flowable bpmn model 二次封装服务
 *
 * <AUTHOR>
 * @date 2022/3/8 10:29
 */
public interface BpmnModelService {

    /**
     * 通过流程定义id获取 BpmnModel
     *
     * @param processDefId 流程定义id
     * @return BpmnModel
     */
    BpmnModel getBpmnModelByProcessDefId(String processDefId);

    /**
     * 获取主流程
     *
     * @param processDefId 流程定义id
     * @return 主流程
     */
    Process findMainProcess(String processDefId);

    /**
     * 获取流程开始节点
     *
     * @param processDefId 流程定义ID
     * @return 流程开始节点
     */
    StartEvent findStartEvent(String processDefId);

    /**
     * 获取流程结束节点
     *
     * @param processDefId 流程定义id
     * @return 流程结束节点
     */
    List<EndEvent> findEndFlowElement(String processDefId);

    /**
     * 获取用户任务节点
     *
     * @param processDefId 流程定义ID
     * @param taskDefKey   用户任务节点定义Key
     * @return 用户任务节点
     */
    UserTask findUserTask(String processDefId, String taskDefKey);

    /**
     * 获取用户任务节点
     *
     * @param processDefId 流程定义ID
     * @param taskDefKey   用户任务节点定义Key
     * @param bpmnModel    流程模型
     * @return 用户任务节点
     */
    UserTask findUserTask(String processDefId, String taskDefKey, BpmnModel bpmnModel);

    /**
     * 获取接收任务节点
     *
     * @param processDefId 流程定义ID
     * @param taskDefKey   接收任务节点定义Key
     * @return 接收任务节点
     */
    ReceiveTask findReceiveTask(String processDefId, String taskDefKey);

    /**
     * 获取服务任务节点
     *
     * @param processDefId 流程定义ID
     * @param taskDefKey   用户任务节点定义Key
     * @return 服务任务节点
     */
    ServiceTask findServiceTask(String processDefId, String taskDefKey);

    /**
     * 获取节点
     *
     * @param processDefId 流程定义ID
     * @param taskDefKey   节点定义Key
     * @return task
     */
    Task findTask(String processDefId, String taskDefKey);

    /**
     * 获取节点
     *
     * @param processDefId 流程定义ID
     * @param taskDefKey   节点定义Key
     * @param bpmnModel 流程模型
     * @return task
     */
    Task findTask(String processDefId, String taskDefKey, BpmnModel bpmnModel);

    /**
     * 获取用户任务节点定义包装类
     * @param processDefId
     * @param taskDefKey
     * @param bpmnModel, 可以为null
     * @return
     */
    UserTaskWrapper findUserTaskWrapper(String processDefId, String taskDefKey, BpmnModel bpmnModel);

    /**
     * 获取节点
     *
     * @param processDefId 流程定义ID
     * @param activityId 节点定义Key
     * @return task
     */
    FlowElement findFlowElement(String processDefId, String activityId);

    /**
     * 获取节点
     *
     * @param activityId 节点定义Key
     * @param bpmnModel 流程模型
     * @return task
     */
    FlowElement findFlowElement(String activityId, BpmnModel bpmnModel);

    Set<String> listFirstNodeDef(String processDefinitionId);

    Set<String> listFirstNodeDef(BpmnModel bpmnModel);
}
