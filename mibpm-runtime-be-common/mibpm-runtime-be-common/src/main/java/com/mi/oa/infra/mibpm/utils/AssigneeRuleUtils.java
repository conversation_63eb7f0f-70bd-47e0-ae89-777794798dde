package com.mi.oa.infra.mibpm.utils;

import com.mi.oa.infra.mibpm.flowable.extension.model.AppAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRuleType;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptManagerAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.GroupAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.InitiatorAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.LeaderAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.MatrixAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.MultiDeptManagerAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.MultiLeaderAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.MultipleUserAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.PassValueAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.PositionAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.ReportLineAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.RoleAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.SingleUserAssigneeRule;
import com.mi.oa.infra.oaucf.utils.GsonUtils;

/**
 * @FileName AssigneeRuleUtils
 * @Description
 * <AUTHOR>
 * @date 2025-02-13
 **/
public class AssigneeRuleUtils {

    // 适配AssigneeRule反序列化
    public static AssigneeRule buildAssigneeRuleByContext(String type, String assigneeRuleContext) {
        if (type.equals(AssigneeRuleType.APP.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, AppAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.DEPARTMENT.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, DeptAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.DEPT_MANAGER.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, DeptManagerAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.GROUP.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, GroupAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.INITIATOR.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, InitiatorAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.LEADER.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, LeaderAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.MATRIX.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, MatrixAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.MULTIPLE_DEPT_MANAGER.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, MultiDeptManagerAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.MULTIPLE_LEADER.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, MultiLeaderAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.MULTIPLE_USER.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, MultipleUserAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.PASS_VALUE.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, PassValueAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.POSITION.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, PositionAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.REPORT_LINE.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, ReportLineAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.ROLE.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, RoleAssigneeRule.class);
        } else if (type.equals(AssigneeRuleType.SINGLE_USER.getCode())) {
            return GsonUtils.fromJson(assigneeRuleContext, SingleUserAssigneeRule.class);
        }
        return null;
    }

}
