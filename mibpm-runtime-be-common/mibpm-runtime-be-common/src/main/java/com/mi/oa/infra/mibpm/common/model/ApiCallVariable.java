package com.mi.oa.infra.mibpm.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 *
 * 服务调用 系统内置变量
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/2 15:23
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ApiCallVariable {

    /**
     * 流程变量
     */
    protected Map<String, Object> variables;

    /**
     * 是否通过模版解析
     *
     */
    protected Boolean isParse;
    /**
     * 事件编码
     */
    protected String eventCode;
}
