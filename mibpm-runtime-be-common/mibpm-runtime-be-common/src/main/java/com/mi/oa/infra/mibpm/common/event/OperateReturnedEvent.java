package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 审批任务被退回事件
 *
 * <AUTHOR>
 * @date 2022/3/18 20:51
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OperateReturnedEvent extends Event {

    /**
     * 任务ID
     */
    protected String taskId;

    /**
     * 任务名称
     */
    protected String taskName;

    /**
     * 任务定义key
     */
    protected String taskDefinitionKey;
    /**
     * 目标节点ID
     */
    private String targetActivityId;
    /**
     * 目标节点名称
     */
    private String targetActivityName;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;
    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
    /**
     * 操作人
     */
    protected BpmUser operator;

    /**
     * 评论
     */
    protected String comment;

    /**
     * 表单数据
     */
    protected Map<String, Object> formData;
}
