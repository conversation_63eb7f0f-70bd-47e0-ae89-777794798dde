package com.mi.oa.infra.mibpm.common.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/7 16:14
 **/
public enum AutoOperationTypeEnum {
    /**
     * 审批人为空
     */
    EMPTY("empty"),
    /**
     * 审批人重复
     */
    DUPLICATE("duplicate"),
    /**
     * 审批任务超时
     */
    TIMEOUT("timeout");

    private String code;

    AutoOperationTypeEnum(String code) {
        this.code = code;
    }

    public static AutoOperationTypeEnum findByCode(String code) {
        AutoOperationTypeEnum[] values = AutoOperationTypeEnum.values();
        for (AutoOperationTypeEnum value : values) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }
}
