package com.mi.oa.infra.mibpm.common.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 部门
 *
 * <AUTHOR>
 * @date 2021/12/26 15:47
 */
@Builder
@Data
public class Department {

    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门层级
     */
    private Integer deptLevel;
    /**
     * 部门状态
     */
    private String status;
    /**
     * 部门路径
     */
    private List<Department> deptPath;
}
