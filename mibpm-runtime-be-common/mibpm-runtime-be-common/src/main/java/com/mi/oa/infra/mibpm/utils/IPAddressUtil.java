package com.mi.oa.infra.mibpm.utils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/2/8 17:58
 */
public class IPAddressUtil {
    /**
     * 获取客户端的真实IP地址
     *
     * @param request HttpServletRequest对象
     * @return 客户端的真实IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");

        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        } else {
            String proxyHeader = request.getHeader("Proxy-Client-IP");
            if (proxyHeader != null && !proxyHeader.isEmpty()) {
                return proxyHeader;
            }

            proxyHeader = request.getHeader("WL-Proxy-Client-IP");
            if (proxyHeader != null && !proxyHeader.isEmpty()) {
                return proxyHeader;
            }
            return request.getRemoteAddr();
        }
    }
}
