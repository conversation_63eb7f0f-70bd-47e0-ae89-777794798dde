package com.mi.oa.infra.mibpm.common.enums;

/**
 * <AUTHOR>
 * @date 2024/8/22
 * @Description
 */
public enum DelegationTypeEnum {
    ALL(0, "全部委托"),
    PARTIAL(1, "部分委托");

    private final int code;
    private final String description;

    DelegationTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static DelegationTypeEnum fromCode(int code) {
        for (DelegationTypeEnum type : DelegationTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}
