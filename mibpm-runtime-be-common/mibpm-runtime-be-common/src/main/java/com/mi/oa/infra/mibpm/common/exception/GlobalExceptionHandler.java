package com.mi.oa.infra.mibpm.common.exception;

import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableOptimisticLockingException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;

/**
 * GlobalExceptionHandler
 *
 * <AUTHOR>
 * @date 2021/8/4 14:10
 */
@Slf4j
@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
class GlobalExceptionHandler {

    /**
     * 其余异常错误
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(value = {Exception.class})
    public BaseResp<Object> handleException(Exception e, HttpServletResponse response) {
        log.error("request internal error: ", e);
        response.setStatus(500);
        return BaseResp.error(500, e.getMessage());
    }

    /**
     * 服务节点调用异常
     */
    @ExceptionHandler(value = {FlowableException.class})
    public BaseResp<Object> handleServiceNodeCallException(FlowableException e, HttpServletResponse response) {
        if (e.getCause() instanceof ServiceNodeCallException) {
            response.setStatus(400);
            return BaseResp.error(400, e.getMessage());
        }
        response.setStatus(500);
        return BaseResp.error(500, e.getMessage());
    }

    /**
     * 空指针异常
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(NullPointerException.class)
    public BaseResp<Object> handleNullPointerException(NullPointerException e, HttpServletResponse response) {
        String message = "空指针异常";
        if (e.getStackTrace().length > 0) {
            StackTraceElement first = e.getStackTrace()[0];
            message = String.format("%s [%s:%d]", message, first.getFileName(), first.getLineNumber());
        }
        log.error(message, e);
        response.setStatus(500);
        return BaseResp.error(500, message);
    }

    /**
     * http not supported 异常
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(value = {HttpRequestMethodNotSupportedException.class})
    public BaseResp<Object> handleNotSupportedRequestException(HttpRequestMethodNotSupportedException e, HttpServletResponse response) {
        log.warn("request not found", e);
        response.setStatus(404);
        return BaseResp.error(404, e.getMessage());
    }

    /**
     * 业务异常
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(value = {BizException.class})
    public BaseResp<Object> handleDomainException(BizException e, HttpServletResponse response) {
        log.warn("----- BizException [" + e.getCode() + "] message :" + e.getMessage());
        log.debug("request domain error:", e);
        response.setStatus(400);
        return BaseResp.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    public BaseResp<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e,
                                                                  HttpServletResponse response) {
        log.warn("请求参数错误:", e);

        ObjectError objectError = e.getBindingResult().getAllErrors()
                .stream()
                .findFirst()
                .get();

        response.setStatus(400);
        return BaseResp.error(400, objectError.getDefaultMessage());
    }

    @ExceptionHandler(value = {MissingServletRequestParameterException.class})
    public BaseResp<Object> handleMissingServletRequestParameterException(MissingServletRequestParameterException e,
                                                                          HttpServletResponse response) {
        log.warn("请求参数错误:", e);
        response.setStatus(400);
        return BaseResp.error(400, e.getMessage());
    }

    @ExceptionHandler(value = {FlowableOptimisticLockingException.class})
    public BaseResp<Object> handleFlowableOptimisticLockingException(FlowableOptimisticLockingException e,
                                                                     HttpServletResponse response) {
        log.error("FlowableOptimisticLockingException:", e);

        response.setStatus(400);
        return BaseResp.error(400, "The data has been modified, please try again");
    }

    @ExceptionHandler(value = {IllegalArgumentException.class})
    public BaseResp<Object> handleIllegalArgumentException(IllegalArgumentException e,
                                                           HttpServletResponse response) {
        log.warn("非法参数错误:", e);

        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return BaseResp.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }
}
