package com.mi.oa.infra.mibpm.common.model;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import lombok.Data;

import java.util.List;

/**
 * @Author: zoutongxu1
 * @DateTime: 2024/10/23 20:57
 * @Description:
 **/
@Data
public class CardBot {

    private String templateId;

    private String username;

    private String deptId;

    private String uuid;

    private String processKey;

    private String instanceId;

    private String taskId;

    private String bpmType;

    private String tenantId;

    /**
     * title color
     */
    private TemplateColor templateColor;

    /**
     * 消息卡片title
     */
    private String title;
    private String enTitle;

    /**
     * 评论区
     */
    private String comment;

    /**
     * 通用模版
     */
    private ExternalContent content;

    /**
     * 自定义模版
     */
    private String customizeContent;

    /**
     * 操作按钮区
     */
    private List<Actions> actions;

    private String messageId;

    /**
     * 卡片来源 bpm or ext
     */
    private String source;

    /**
     * 租户appID
     */
    private String appId;

    /**
     * 三方业务系统数据
     */
    private String extProcessKey;

    private String extInstanceId;

    private String extTaskId;

    private InstanceInfo instanceInfo;

    /**
     * 当前审批状态
     */
    private String status;

    /**
     * 具体业务的提示信息
     */
    private String tips;
    private String enTips;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 是否以webView方式打开详情
     */
    protected Boolean webView;

    @Data
    public static class InstanceInfo {

        private String processName;

        private String startTime;

        private String taskName;
    }

}
