package com.mi.oa.infra.mibpm.common.model;

import lombok.*;

import java.util.Date;

/**
 * 任务日志
 *
 * <AUTHOR>
 * @date 2022/2/9 15:58
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TaskLog {

    /**
     * 日志ID
     */
    protected String logId;
    /**
     * 事件类型
     */
    protected String type;
    /**
     * 任务ID
     */
    protected String taskId;
    /**
     * 事件发生时间
     */
    protected Date timeStamp;
    /**
     * 触发任务事件的用户标识
     */
    protected String userId;
    /**
     * 负载信息
     */
    protected String data;
    /**
     * 执行ID
     */
    protected String executionId;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;
    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
    /**
     * 任务范畴
     */
    protected String scopeType;
}
