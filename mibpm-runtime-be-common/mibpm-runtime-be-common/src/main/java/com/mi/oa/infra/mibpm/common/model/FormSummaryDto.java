package com.mi.oa.infra.mibpm.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/20 4:13 PM
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FormSummaryDto implements Serializable {

    private static final long serialVersionUID  = 8744866340877415770L;

    private String label;
    private String labelEn;
    private String name;
    private String type;
    private Object data;
}
