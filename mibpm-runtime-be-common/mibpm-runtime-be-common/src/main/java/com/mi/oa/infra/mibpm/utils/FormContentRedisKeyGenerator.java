package com.mi.oa.infra.mibpm.utils;

import org.springframework.stereotype.Component;

/**
 * Redis的key生成器，用于生成表单内容的Redis key
 * <AUTHOR>
 * @date 2023/7/10
 */
@Component
public class FormContentRedisKeyGenerator {
    
    private static final String FORM_CONTENT_PREFIX = "mibpm-form-content:";

    /**
     * 缓存表单的key生成
     * @param modelCode
     * @param userId
     * @return
     */
    public String generateFormContentKey(String modelCode, String userId) {
        return FORM_CONTENT_PREFIX + modelCode + ":" + userId;
    }

    /**
     * 缓存表单的key生成，带业务键
     * @param modelCode
     * @param userId
     * @param businessKey
     * @return
     */
    public String generateFormContentKey(String modelCode, String userId, String businessKey) {
        if (businessKey == null || businessKey.isEmpty()) {
            return generateFormContentKey(modelCode, userId);
        }
        return FORM_CONTENT_PREFIX + modelCode + ":" + userId + ":" + businessKey;
    }
}
