package com.mi.oa.infra.mibpm.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色
 *
 * <AUTHOR>
 * @date 2022/4/11 16:21
 */
@Getter
@AllArgsConstructor
public enum AuthorizeRoleEnum {

    /**
     * 超级管理员
     */
    SUPER_ADMIN("super_admin", "超级管理员"),
    /**
     * 全局稽查员
     */
    CHECKER("checker", "全局稽查员"),
    /**
     * 实施管理员
     */
    OPERATIONS_ADMIN("operations_admin", "实施管理员");

    private final String code;
    private final String desc;
}
