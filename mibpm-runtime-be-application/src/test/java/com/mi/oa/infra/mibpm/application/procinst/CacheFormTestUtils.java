package com.mi.oa.infra.mibpm.application.procinst;

import com.mi.oa.infra.mibpm.application.proinst.dto.req.FormStoragReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessFormAbortReq;
import com.mi.oa.infra.mibpm.common.model.BpmUser;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缓存表单测试工具类
 * 提供测试用的静态工具方法
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public class CacheFormTestUtils {

    // ==================== 测试数据创建方法 ====================

    /**
     * 创建标准的测试表单数据
     * @return 测试表单数据
     */
    public static Map<String, Object> createStandardFormData() {
        Map<String, Object> formData = new HashMap<>();
        formData.put("applicantName", "张三");
        formData.put("department", "技术部");
        formData.put("amount", 5000.00);
        formData.put("reason", "项目开发费用");
        formData.put("urgent", true);
        return formData;
    }

    /**
     * 创建复杂的测试表单数据
     * @return 复杂的测试表单数据
     */
    public static Map<String, Object> createComplexFormData() {
        Map<String, Object> formData = new HashMap<>();
        
        // 基础字段
        formData.put("title", "复杂表单测试");
        formData.put("priority", 1);
        formData.put("enabled", true);
        
        // 数组字段
        formData.put("tags", new String[]{"测试", "表单", "缓存"});
        
        // 嵌套对象
        Map<String, Object> applicant = new HashMap<>();
        applicant.put("name", "李四");
        applicant.put("email", "<EMAIL>");
        applicant.put("phone", "13800138000");
        formData.put("applicant", applicant);
        
        // 列表数据
        Map<String, Object> item1 = new HashMap<>();
        item1.put("name", "项目A");
        item1.put("cost", 1000.0);
        
        Map<String, Object> item2 = new HashMap<>();
        item2.put("name", "项目B");
        item2.put("cost", 2000.0);
        
        formData.put("items", new Object[]{item1, item2});
        
        return formData;
    }

    /**
     * 创建空的表单数据
     * @return 空的表单数据
     */
    public static Map<String, Object> createEmptyFormData() {
        return new HashMap<>();
    }

    /**
     * 创建包含特殊字符的表单数据
     * @return 包含特殊字符的表单数据
     */
    public static Map<String, Object> createSpecialCharFormData() {
        Map<String, Object> formData = new HashMap<>();
        formData.put("special_chars", "测试@#$%^&*()_+{}|:<>?[]\\;'\",./<>?");
        formData.put("unicode_chars", "🚀🎉💻📱🌟");
        formData.put("chinese_chars", "中文测试数据");
        formData.put("english_chars", "English Test Data");
        formData.put("numbers", "1234567890");
        return formData;
    }

    // ==================== 请求对象创建方法 ====================

    /**
     * 创建标准的 FormStoragReq 对象
     * @param modelCode 模型编码
     * @param userName 用户名
     * @return FormStoragReq 对象
     */
    public static FormStoragReq createStandardFormStoragReq(String modelCode, String userName) {
        return FormStoragReq.builder()
                .modelCode(modelCode)
                .userName(userName)
                .formData(createStandardFormData())
                .build();
    }

    /**
     * 创建复杂的 FormStoragReq 对象
     * @param modelCode 模型编码
     * @param userName 用户名
     * @return FormStoragReq 对象
     */
    public static FormStoragReq createComplexFormStoragReq(String modelCode, String userName) {
        return FormStoragReq.builder()
                .modelCode(modelCode)
                .userName(userName)
                .formData(createComplexFormData())
                .build();
    }

    /**
     * 创建没有用户名的 FormStoragReq 对象
     * @param modelCode 模型编码
     * @return FormStoragReq 对象
     */
    public static FormStoragReq createFormStoragReqWithoutUser(String modelCode) {
        return FormStoragReq.builder()
                .modelCode(modelCode)
                .formData(createStandardFormData())
                .build();
    }

    /**
     * 创建标准的 ProcessFormAbortReq 对象
     * @param modelCode 模型编码
     * @param userName 用户名
     * @param businessKey 业务键
     * @return ProcessFormAbortReq 对象
     */
    public static ProcessFormAbortReq createStandardAbortReq(String modelCode, String userName, String businessKey) {
        return ProcessFormAbortReq.builder()
                .modelCode(modelCode)
                .userName(userName)
                .build();
    }

    /**
     * 创建没有用户名的 ProcessFormAbortReq 对象
     * @param modelCode 模型编码
     * @param businessKey 业务键
     * @return ProcessFormAbortReq 对象
     */
    public static ProcessFormAbortReq createAbortReqWithoutUser(String modelCode, String businessKey) {
        return ProcessFormAbortReq.builder()
                .modelCode(modelCode)
                .build();
    }

    // ==================== 用户对象创建方法 ====================

    /**
     * 创建标准的 BpmUser 对象
     * @param userName 用户名
     * @param displayName 显示名称
     * @param uid 用户ID
     * @return BpmUser 对象
     */
    public static BpmUser createStandardBpmUser(String userName, String displayName, String uid) {
        BpmUser user = new BpmUser();
        user.setUserName(userName);
        user.setDisplayName(displayName);
        user.setUid(uid);
        return user;
    }

    /**
     * 创建当前用户对象
     * @return 当前用户对象
     */
    public static BpmUser createCurrentUser() {
        return createStandardBpmUser("currentUser", "当前用户", "123456");
    }

    /**
     * 创建测试用户对象
     * @return 测试用户对象
     */
    public static BpmUser createTestUser() {
        return createStandardBpmUser("testUser", "测试用户", "654321");
    }

    // ==================== 验证工具方法 ====================

    /**
     * 验证 FormStoragReq 对象的基本属性
     * @param req 要验证的请求对象
     * @param expectedModelCode 期望的模型编码
     * @param expectedUserName 期望的用户名
     */
    public static void assertFormStoragReq(FormStoragReq req, String expectedModelCode, String expectedUserName) {
        assertNotNull(req, "FormStoragReq 不应为空");
        assertEquals(expectedModelCode, req.getModelCode(), "模型编码不匹配");
        assertEquals(expectedUserName, req.getUserName(), "用户名不匹配");
        assertNotNull(req.getFormData(), "表单数据不应为空");
    }

    /**
     * 验证 ProcessFormAbortReq 对象的基本属性
     * @param req 要验证的请求对象
     * @param expectedModelCode 期望的模型编码
     * @param expectedUserName 期望的用户名
     * @param expectedBusinessKey 期望的业务键
     */
    public static void assertAbortReq(ProcessFormAbortReq req, String expectedModelCode,
                                     String expectedUserName, String expectedBusinessKey) {
        assertNotNull(req, "ProcessFormAbortReq 不应为空");
        assertEquals(expectedModelCode, req.getModelCode(), "模型编码不匹配");
        assertEquals(expectedUserName, req.getUserName(), "用户名不匹配");
    }

    /**
     * 验证 BpmUser 对象的基本属性
     * @param user 要验证的用户对象
     * @param expectedUserName 期望的用户名
     * @param expectedDisplayName 期望的显示名称
     * @param expectedUid 期望的用户ID
     */
    public static void assertBpmUser(BpmUser user, String expectedUserName, 
                                    String expectedDisplayName, String expectedUid) {
        assertNotNull(user, "BpmUser 不应为空");
        assertEquals(expectedUserName, user.getUserName(), "用户名不匹配");
        assertEquals(expectedDisplayName, user.getDisplayName(), "显示名称不匹配");
        assertEquals(expectedUid, user.getUid(), "用户ID不匹配");
    }

    /**
     * 验证表单数据不为空且包含指定的键
     * @param formData 表单数据
     * @param expectedKeys 期望的键
     */
    public static void assertFormDataContainsKeys(Map<String, Object> formData, String... expectedKeys) {
        assertNotNull(formData, "表单数据不应为空");
        for (String key : expectedKeys) {
            assertTrue(formData.containsKey(key), "表单数据应该包含键: " + key);
        }
    }

    /**
     * 验证表单数据的值
     * @param formData 表单数据
     * @param key 键
     * @param expectedValue 期望的值
     */
    public static void assertFormDataValue(Map<String, Object> formData, String key, Object expectedValue) {
        assertNotNull(formData, "表单数据不应为空");
        assertTrue(formData.containsKey(key), "表单数据应该包含键: " + key);
        assertEquals(expectedValue, formData.get(key), "键 " + key + " 的值不匹配");
    }

    // ==================== 常量定义 ====================

    public static final String DEFAULT_MODEL_CODE = "test_model_001";
    public static final String DEFAULT_USER_NAME = "testUser";
    public static final String DEFAULT_BUSINESS_KEY = "test_business_key";
    public static final String CURRENT_USER_NAME = "currentUser";
    public static final String CURRENT_USER_ID = "123456";
}
