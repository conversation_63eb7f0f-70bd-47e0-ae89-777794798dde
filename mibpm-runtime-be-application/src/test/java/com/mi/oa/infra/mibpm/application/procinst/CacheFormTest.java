package com.mi.oa.infra.mibpm.application.procinst;

import com.mi.oa.infra.mibpm.application.procinst.impl.ProcessInstanceServiceImpl;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.FormStoragReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessFormAbortReq;
import com.mi.oa.infra.mibpm.application.proinst.service.ProcessInstanceService;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.procinst.ability.impl.FormContentCacheAbilityImpl;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainServiceImpl;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.impl.FormContentCacheRepositoryImpl;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.sun.tools.javac.util.Abort;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 表单缓存测试类
 * 测试表单内容的缓存、获取和作废功能
 *
 * <AUTHOR>
 * @date 2025/6/4
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({IdentityUtil.class})
public class CacheFormTest {

    @Mock
    private ProcessInstanceDomainService processInstanceDomainService;

    @Mock
    private AccountRemoteService accountRemoteService;

    @InjectMocks
    private ProcessInstanceServiceImpl processInstanceService;

    @InjectMocks
    private ProcessInstanceDomainServiceImpl processInstanceDomainServiceImpl;

    @InjectMocks
    private FormContentCacheAbilityImpl formContentCacheAbility;
    @InjectMocks
    private FormContentCacheRepositoryImpl formContentCacheRepository;

    private FormStoragReq validStagingReq;
    private BpmUser mockUser;
    private Map<String, Object> mockFormData;

    @Before
    public void setUp() {
        // 准备模拟用户
        mockUser = new BpmUser();
        mockUser.setUid("test_uid_123");
        mockUser.setUserName("testUserName");
        mockUser.setDisplayName("测试用户显示名");
        mockUser.setPersonId("P123456");
        mockUser.setHrStatus("A"); // 在职
        mockUser.setType("employee");

        // 准备模拟表单数据
        mockFormData = new HashMap<>();
        mockFormData.put("applicantName", "张三");
        mockFormData.put("department", "技术部");
        mockFormData.put("amount", 5000.50);
        mockFormData.put("reason", "项目开发费用");
        mockFormData.put("urgent", true);

        // 准备有效的暂存请求
        validStagingReq = FormStoragReq.builder()
                .modelCode("test_model_001")
                .userName("testUser")
                .formData(mockFormData)
                .build();
    }

    // ==================== stagingFormContent 方法测试 ====================

    @Test
    public void testStagingFormContent_Success() {
        // Given: 准备测试数据
        FormStoragReq req = FormStoragReq.builder()
                .modelCode("test_model_staging_001")
                .userName("testUser")
                .formData(mockFormData)
                .build();

        // Mock 领域服务返回成功
        when(processInstanceDomainService.cacheFormContent(eq(req)))
                .thenReturn(true);

        // When: 执行暂存操作
        Boolean result = processInstanceService.stagingFormContent(req);

        // Then: 验证结
        assertNotNull(result);
        assertTrue(result);
        // 验证领域服务被正确调用
        verify(processInstanceDomainService, times(1))
                .cacheFormContent(req);
        log.info("✅ stagingFormContent成功场景测试通过");
    }

    @Test
    @DisplayName("测试stagingFormContent方法 - 失败场景")
    public void testStagingFormContent_Failure() {
        // Given: 准备测试数据
        FormStoragReq req = FormStoragReq.builder()
                .modelCode("test_model_staging_002")
                .userName("testUser")
                .formData(mockFormData)
                .build();

        // Mock 领域服务返回失败
        when(processInstanceDomainService.cacheFormContent(eq(req)))
                .thenReturn(false);

        // When: 执行暂存操作
        Boolean result = processInstanceService.stagingFormContent(req);

        // Then: 验证结果
        assertNotNull(result);
        assertFalse(result);

        // 验证领域服务被正确调用
        verify(processInstanceDomainService, times(1))
                .cacheFormContent(req);

        log.info("✅ stagingFormContent失败场景测试通过");
    }

    @Test
    public void testStagingFormContent_PrepareStagingIdentifierMethod() {
        // Given: 准备测试数据，不提供用户名以触发prepareStagingIdentifier方法
        FormStoragReq req = FormStoragReq.builder()
                .modelCode("test_model_staging_003")
                .formData(mockFormData)
                .build();

        // Mock 当前用户获取 - 使用 PowerMock
        PowerMockito.mockStatic(IdentityUtil.class);
        PowerMockito.when(IdentityUtil.currentUserName()).thenReturn("currentUserId");
        when(accountRemoteService.getUser("currentUserId")).thenReturn(mockUser);
        when(processInstanceDomainService.cacheFormContent(any(FormStoragReq.class)))
                .thenReturn(true);

        // When: 执行暂存操作
        Boolean result = processInstanceService.stagingFormContent(req);

        // Then: 验证结果和内部方法调用
        assertTrue("暂存操作应该成功", result);

        // 验证prepareStagingIdentifier方法的内部逻辑被执行
        // 1. 验证IdentityUtil.currentUserName()被调用
        PowerMockito.verifyStatic(IdentityUtil.class, times(1));
        IdentityUtil.currentUserName();

        // 2. 验证accountRemoteService.getUser()被调用
        verify(accountRemoteService, times(1)).getUser("currentUserId");
        // 3. 验证用户名被正确设置到请求对象中
        assertEquals("用户名应该被正确设置", "testUserName", req.getUserName());
        // 4. 验证领域服务被调用
        verify(processInstanceDomainService, times(1))
                .cacheFormContent(req);
        log.info("✅ prepareStagingIdentifier内部方法调用测试通过");
    }

    @Test
    public void testStagingFormContent_UserNotFound() {
        // Given: 准备测试数据，不提供用户名且当前用户不存在
        FormStoragReq req = FormStoragReq.builder()
                .modelCode("test_model_staging_004")
                .formData(mockFormData)
                .build();

        // Mock 当前用户获取失败 - 使用 PowerMock
        PowerMockito.mockStatic(IdentityUtil.class);
        PowerMockito.when(IdentityUtil.currentUserName()).thenReturn("nonExistentUser");
        when(accountRemoteService.getUser("nonExistentUser")).thenReturn(null);

        // When & Then: 执行测试并验证异常
        try {
            processInstanceService.stagingFormContent(req);
            fail("应该抛出 DomainException");
        } catch (DomainException e) {
            // 验证异常被正确抛出
            assertNotNull("异常不应为空", e);
        }
        // 验证当前用户获取被调用
        PowerMockito.verifyStatic(IdentityUtil.class, times(1));
        IdentityUtil.currentUserName();
        verify(accountRemoteService, times(1)).getUser("nonExistentUser");
        // 验证领域服务没有被调用
        verify(processInstanceDomainService, never())
                .cacheFormContent(any(FormStoragReq.class));
        log.info("✅ 用户不存在异常测试通过");
    }

    @Test
    public void testStagingFormContent_FormStoragReqProperties() {
        // Given: 准备测试数据，验证FormStoragReq的所有属性
        Map<String, Object> complexFormData = new HashMap<>();
        complexFormData.put("userName", "张三");
        complexFormData.put("department", "技术部");
        complexFormData.put("amount", 5000.50);
        complexFormData.put("urgent", true);
        complexFormData.put("items", new String[]{"item1", "item2"});

        FormStoragReq req = FormStoragReq.builder()
                .modelCode("test_model_staging_005")
                .userName("testUser")
                .formData(complexFormData)
                .build();

        // 验证FormStoragReq的属性
        assertNotNull("模型编码不应为空", req.getModelCode());
        assertNotNull("用户名不应为空", req.getUserName());
        assertNotNull("表单数据不应为空", req.getFormData());
        assertEquals("模型编码应该匹配", "test_model_staging_005", req.getModelCode());
        assertEquals("用户名应该匹配", "testUser", req.getUserName());
        assertEquals("表单数据大小应该匹配", 5, req.getFormData().size());
        assertTrue("应该包含userName字段", req.getFormData().containsKey("userName"));
        assertTrue("应该包含amount字段", req.getFormData().containsKey("amount"));

        // Mock 领域服务
        when(processInstanceDomainService.cacheFormContent(eq(req)))
                .thenReturn(true);

        // When: 执行暂存操作
        Boolean result = processInstanceService.stagingFormContent(req);

        // Then: 验证结果
        assertTrue("暂存操作应该成功", result);

        // 验证传递给领域服务的请求对象属性保持不变
        verify(processInstanceDomainService, times(1))
                .cacheFormContent(argThat(argument ->
                    "test_model_staging_005".equals(argument.getModelCode()) &&
                    "testUser".equals(argument.getUserName()) &&
                    argument.getFormData().size() == 5 &&
                    argument.getFormData().containsKey("userName") &&
                    argument.getFormData().containsKey("amount")
                ));

        log.info("✅ FormStoragReq属性验证测试通过");
    }

    @Test
    public void testStagingFormContent_BpmUserProperties() {
        // Given: 准备测试数据，不提供用户名以触发BpmUser的使用
        FormStoragReq req = FormStoragReq.builder()
                .modelCode("test_model_staging_006")
                .formData(mockFormData)
                .build();

        // 创建完整的BpmUser对象，验证所有属性
        BpmUser testUser = new BpmUser();
        testUser.setUid("test_uid_123");
        testUser.setUserName("testUserName");
        testUser.setDisplayName("测试用户显示名");
        testUser.setPersonId("P123456");
        testUser.setHrStatus("A"); // 在职
        testUser.setType("employee");
        // Mock 当前用户获取 - 使用 PowerMock
        PowerMockito.mockStatic(IdentityUtil.class);
        PowerMockito.when(IdentityUtil.currentUserName()).thenReturn("currentUserId");
        when(accountRemoteService.getUser("currentUserId")).thenReturn(testUser);
        when(processInstanceDomainService.cacheFormContent(any(FormStoragReq.class)))
                .thenReturn(true);

        // When: 执行暂存操作
        Boolean result = processInstanceService.stagingFormContent(req);
        // Then: 验证结果和BpmUser属性的使用
        assertTrue("暂存操作应该成功", result);

        // 验证BpmUser的userName属性被正确使用
        assertEquals("BpmUser的userName属性应该被正确设置到请求中", "testUserName", req.getUserName());

        // 验证accountRemoteService.getUser()被调用并返回了正确的BpmUser
        verify(accountRemoteService, times(1)).getUser("currentUserId");

        // 验证领域服务被调用，且请求中的userName被正确设置
        verify(processInstanceDomainService, times(1))
                .cacheFormContent(argThat(argument ->
                    "testUserName".equals(argument.getUserName())
                ));

        log.info("✅ BpmUser属性验证测试通过");
    }

    @Test
    public void testGetFormContent_BpmUserProperties(){
        // Given: 准备测试数据
        FormStoragReq req = FormStoragReq.builder()
                .modelCode("test_model_staging_001")
                .formData(mockFormData)
                .build();
        String modelCode=req.getModelCode();
        String modelName=req.getUserName();

        PowerMockito.mockStatic(IdentityUtil.class);
        PowerMockito.when(IdentityUtil.currentUserName()).thenReturn("currentUserId");
        when(accountRemoteService.getUser("currentUserId")).thenReturn(mockUser);
        when(processInstanceDomainService.getCachedFormContent(modelCode,"testUserName")).thenReturn(mockFormData);

        processInstanceService.getStagingFormContent(modelCode,modelName);
        assertNotNull("表单不应为空",mockFormData );


    }
    @Test
    public void abortFormContent(){
        ProcessFormAbortReq req = ProcessFormAbortReq.builder()
                .modelCode("test_model_staging_001")
                .build();

        PowerMockito.mockStatic(IdentityUtil.class);
        PowerMockito.when(IdentityUtil.currentUserName()).thenReturn("currentUserId");
        when(accountRemoteService.getUser("currentUserId")).thenReturn(mockUser);
        when(processInstanceDomainService.abortCachedFormContent(req.getModelCode(),"testUserName")).thenReturn(true);
        when(processInstanceService.abortCachedFormContent(req)).thenReturn(true);

    }
    //接下来所有的test都是测试domainservice和ability中的方法
    @Test
    public void testCacheDomainCache(){

//        // Given: 准备测试数据，不提供用户名以触发BpmUser的使用
//        FormStoragReq req = FormStoragReq.builder()
//                .modelCode("test_model_staging_006")
//                .formData(mockFormData)
//                .build();
//
//        when(formContentCacheAbility.cacheFormContent(req)).thenReturn(true);
//
//        when(processInstanceDomainServiceImpl.cacheFormContent(req)).thenReturn(true);










    }




}


