package com.mi.oa.infra.mibpm.application;

import com.mi.oa.infra.mibpm.application.delegation.dto.ApplyDelegationDTO;
import com.mi.oa.infra.mibpm.application.delegation.impl.DelegationServiceImpl;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcessInstanceResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceCreateReq;
import com.mi.oa.infra.mibpm.application.proinst.service.ProcessInstanceService;
import com.mi.oa.infra.mibpm.common.enums.DelegationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import org.apache.commons.lang3.StringUtils;
import static org.assertj.core.api.Assertions.assertThat;
import org.flowable.idm.api.User;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.time.ZonedDateTime;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/3/8 11:16
 */
@SpringBootTest(classes = {ServiceTestApplication.class})
public class ProcessInstanceServiceTest {

    @Autowired
    private ProcessInstanceService processInstanceService;

    private static final String MODEL_CODE = "bpmn_691295827278290944";
    private static final String PROC_DEF_ID = "";
    private static final String BUSINESS_KEY = "t220220308006";
    private static final String NAME = "流程发起测试";
    private static final String START_USER_ID = "wangchen21";
    @Resource
    private DelegationServiceImpl delegationServiceImpl;

    @Test
    public void startProcessInstance() {
        ProcessInstanceCreateReq processInstanceCreateReq = getProcessInstanceCreateReq();
        ProcessInstanceResp processInstanceReps = processInstanceService.startProcessInstance(processInstanceCreateReq);
        assertThat(processInstanceReps).isNotNull();
        //assertThat(processInstanceReps.getCategoryCode()).isNotBlank();
        if (StringUtils.isNoneBlank(MODEL_CODE)) {
            assertThat(processInstanceReps.getModelCode()).isEqualTo(MODEL_CODE);
        } else {
            assertThat(processInstanceReps.getModelCode()).isNotBlank();
        }
        if (StringUtils.isNotBlank(PROC_DEF_ID)) {
            assertThat(processInstanceReps.getProcessDefinitionId()).isEqualTo(PROC_DEF_ID);
        } else {
            assertThat(processInstanceReps.getProcessDefinitionId()).isNotBlank();
        }
        //assertThat(processInstanceReps.getProcessInstanceName()).isNotBlank();
        assertThat(processInstanceReps.getProcessDefinitionVersion()).isNotNull();
        if (StringUtils.isNotBlank(BUSINESS_KEY)) {
            assertThat(processInstanceReps.getBusinessKey()).isEqualTo(BUSINESS_KEY);
        } else {
            assertThat(processInstanceReps.getBusinessKey()).isNotBlank();
        }
        assertThat(processInstanceReps.getProcessInstanceId()).isNotBlank();
        if (StringUtils.isNoneBlank(NAME)) {
            assertThat(processInstanceReps.getProcessInstanceName()).isEqualTo(NAME);
        } else {
            assertThat(processInstanceReps.getProcessInstanceName()).isNotBlank();
        }
        assertThat(processInstanceReps.getProcessInstanceStatus()).isEqualTo(ProcessInstanceStatus.RUNNING);
        assertThat(processInstanceReps.getStartTime()).isNotNull();
        if (StringUtils.isNotBlank(START_USER_ID)) {
            assertThat(processInstanceReps.getStartUserId()).isEqualTo(START_USER_ID);
        } else {
            assertThat(processInstanceReps.getStartUserId()).isNotBlank();
        }

    }

    @Test
    public void buildProcInstName() {
        ExpressionParser parser = new SpelExpressionParser();
        EvaluationContext context = new StandardEvaluationContext();
        User person = new UserEntityImpl();
        person.setDisplayName("wangchen21");
        context.setVariable("name", "wangchen21");
        context.setVariable("gender", "man");
        Object value =
                parser.parseExpression("他的名字为#{#name}-#{#gender}", new TemplateParserContext()).getValue(context);
        System.out.println(value);
    }

    private ProcessInstanceCreateReq getProcessInstanceCreateReq() {
        return ProcessInstanceCreateReq.builder().modelCode(MODEL_CODE).businessKey(BUSINESS_KEY).name(NAME)
                .startUserId(START_USER_ID).build();
    }

    @Test
    public void testApplyDelegation() {
        ApplyDelegationDTO applyDelegationDTO = ApplyDelegationDTO.builder()
                .delegatorId("zoutongxu1")
                .approverAllId("baopan")
                .delegationReason("离职委托")
                .startTime(ZonedDateTime.now().toLocalDateTime())
                .endTime(ZonedDateTime.now().plusYears(3L).toLocalDateTime())
                .delegationType(DelegationTypeEnum.ALL)
                .delegationSourceEnum(com.mi.oa.infra.mibpm.common.enums.DelegationSourceEnum.RESIGNATION_DELEGATION)
                .build();

        delegationServiceImpl.applyDelegation(applyDelegationDTO, true);

    }

}
