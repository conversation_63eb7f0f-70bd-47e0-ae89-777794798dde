package com.mi.oa.infra.mibpm.application.common;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/3/5 14:42
 */
@Slf4j
@Component
public class NewQuery {

    @NacosValue(value = "${mibpm.mitask.query:false}", autoRefreshed = true)
    private Boolean newQueryFlag;
    @NacosValue(value = "${mibpm.mitask.extMiTaskDetail:-1}", autoRefreshed = true)
    private Integer canaryRate;
    @NacosValue(value = "${mibpm.mitask.whitelist:userId}", autoRefreshed = true)
    private String whitelist;

    public boolean get() {
        boolean newQuery = newQueryFlag;
        if (newQuery) {
            log.info("query by miTask, user={}", IdentityUtil.currentUserName());
        }
        return newQuery;
    }

    public boolean newMiTaskDetail(String taskId) {
        boolean newMiTaskDetail = false;
        if (Arrays.asList(whitelist.split(","))
                .contains(IdentityUtil.currentUserName())) {
            newMiTaskDetail = true;
        } else {
            newMiTaskDetail = Math.abs(taskId.hashCode()) % 100 <= canaryRate;
        }

        if (newMiTaskDetail) {
            log.info("newMiTaskDetail by miTask, user={}, taskId={}", IdentityUtil.currentUserName(), taskId);
        }
        return newMiTaskDetail;
    }
}
