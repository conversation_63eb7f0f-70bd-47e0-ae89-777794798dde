package com.mi.oa.infra.mibpm.application.message.event;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.application.message.service.MessageService;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.constant.LarkMessageConstants;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.FormSummaryDto;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.eventbus.Event;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.oaucf.idm.api.rep.DeptInfoDto;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 消息回调监听器
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/30 21:10
 */
@Slf4j
public abstract class AbstractMessageListener<T extends Event> implements EventSubscriber<T> {

    @Autowired
    private MessageService messageService;
    @Autowired
    private HistoricTaskRepository historicTaskRepository;
    @Autowired
    private HistoryService historyService;
    @Autowired
    protected AccountRemoteService accountRemoteService;
    @Autowired
    private ModelMetaRepository modelMetaRepository;

    /**
     * 同步待办角标
     *
     * @param userId
     */
    public void syncAppBadge(String userId) {
        messageService.syncAppBadge(userId);
    }

    /**
     * 同步待办角标
     *
     * @param instanceId
     * @param taskDefKey
     */
    public void syncAppBadge(String instanceId, String taskDefKey) {
        messageService.syncAppBadge(instanceId, taskDefKey);
    }

    /**
     * 查找最匹配发起人的taskId
     *
     * @param instance
     * @return
     */
    public String queryStartUserMostConformTask(ProcessInstanceDo instance) {
        TaskDo taskDo = historicTaskRepository
                .queryMostConformTask(instance, instance.getStartUserId());
        return taskDo.getTaskId();
    }

    public Map<String, String> parseSummary(String taskId) {
        List<HistoricVariableInstance> list = historyService.createHistoricVariableInstanceQuery().taskId(taskId)
                .list();
        Optional<HistoricVariableInstance> any = list.stream()
                .filter(i -> StringUtils.equals(BpmVariablesConstants.VARIABLE_SUMMARY, i.getVariableName())).findAny();
        Map<String, String> summaryMap = new LinkedHashMap<>(8);
        if (any.isPresent()) {
            Object value = any.get().getValue();
            addSummary(summaryMap, value);
        }
        return summaryMap;
    }

    public void parseSummaryAndAction(String taskId, LarkMessageDo messageDo) {
        List<HistoricVariableInstance> list = historyService.createHistoricVariableInstanceQuery().taskId(taskId)
                .list();
        Optional<HistoricVariableInstance> any = list.stream()
                .filter(i -> StringUtils.equals(BpmVariablesConstants.VARIABLE_SUMMARY, i.getVariableName())).findAny();
        Map<String, String> summaryMap = new LinkedHashMap<>();
        Content content = messageDo.getContent();
        if (any.isPresent()) {
            Object value = any.get().getValue();
            addSummary(summaryMap, value);
            content.setSummaries(Lists.newArrayList(summaryMap));
        }
        Optional<HistoricVariableInstance> fastApproval = list.stream()
                .filter(i -> StringUtils.equals(BpmVariablesConstants.VARIABLE_FAST_APPROVAL, i.getVariableName()))
                .findAny();
        if (fastApproval.isPresent() && Boolean.parseBoolean(String.valueOf(fastApproval.get().getValue()))) {
            List<Actions> actions = messageDo.getActions();
            // 填充审批按钮信息
            Actions agree = new Actions();
            agree.setActionKey(LarkMessageConstants.APPROVED);
            actions.add(agree);
            Actions reject = new Actions();
            reject.setActionKey(LarkMessageConstants.REJECTED);
            actions.add(reject);
        }
    }

    private void addSummary(Map<String, String> summaryMap, Object value) {
        if (value instanceof List) {
            List<FormSummaryDto> summaryDtoList = (List<FormSummaryDto>) value;
            summaryDtoList.forEach(i -> {
                String enLabel = StringUtils.isNotBlank(i.getLabelEn()) ? i.getLabelEn() : "";
                summaryMap.put(i.getLabel() + "," + enLabel, String.valueOf(i.getData()));
            });
        }
    }

    /**
     * 构建流程信息
     *
     * @param taskId
     * @param builder
     * @param processInstanceDo
     */
    public void buildInstanceInfo(String taskId, LarkMessageDoBuilder builder, ProcessInstanceDo processInstanceDo,
                                  boolean queryStartUserMostConformTask) {

        if (StringUtils.isBlank(taskId)) {
            return;
        }

        HistoricTaskInstance taskInstance = historyService.createHistoricTaskInstanceQuery()
                .taskId(taskId)
                .locale(Locale.ENGLISH.getLanguage())
                .singleResult();

        builder.taskName(taskInstance.getName());
        try {
            Field taskName = taskInstance.getClass().getDeclaredField("name");
            taskName.setAccessible(true);

            builder.taskName(taskName.get(taskInstance).toString());
        } catch (Exception e) {
            log.error("获取国际化字段配置失败", e);
        }
        // 构建流程信息

        builder.processNameEn(processInstanceDo.getProcessInstanceName());
        builder.processName(processInstanceDo.getProcessInstanceName());
        builder.instanceId(processInstanceDo.getProcessInstanceId());
        builder.startTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(processInstanceDo.getStartTime()));
        builder.taskId(queryStartUserMostConformTask ? this.queryStartUserMostConformTask(processInstanceDo) : taskId);
        builder.taskNameEn(taskInstance.getName());
        builder.taskDefKey(taskInstance.getTaskDefinitionKey());
        builder.modelCode(processInstanceDo.getModelCode());
        builder.categoryCode(processInstanceDo.getCategoryCode());
    }

    public List<String> buildDeptInfo(BpmUser user) {
        List<String> deptNames = new ArrayList<>();
        if (!user.functionAccount()) {
            List<DeptInfoDto> deptInfo = GsonUtils.fromJson(user.getOrg().getFullOrgDesc(),
                    new TypeToken<List<DeptInfoDto>>() {
                    }.getType());
            if (Objects.nonNull(deptInfo)) {
                // 获取部门的前两级
                deptNames = deptInfo.stream().filter(t -> !t.getLevel().equals("0"))
                        .limit(2).map(DeptInfoDto::getDeptName).collect(Collectors.toList());
            }
        }
        return deptNames;
    }


    /**
     * 构建流程标题
     *
     * @param builder
     * @param startUser
     */
    public void buildTitle(LarkMessageDoBuilder builder, BpmUser startUser, ProcessInstanceDo instance) {
        ModelMeta modelMeta = modelMetaRepository.queryByModelCode(instance.getModelCode());
        // 构建title
        String title = String.format("[%s]发起[%s]，请及时处理", startUser.getDisplayName(), reBuildTitle(modelMeta.getName(), 23));
        String enTitle = String.format("[%s] initiate [%s], please check in time.",
                startUser.getUserName(), reBuildTitle(
                        StringUtils.isEmpty(modelMeta.getEnName()) ? modelMeta.getName() : modelMeta.getEnName(), 41));
        builder.title(new I18n(title, enTitle, enTitle));
    }

    public String reBuildTitle(String title, int maxLen) {
        if (title.length() <= maxLen) {
            return title;
        }
        return title.substring(0, maxLen - 1).concat("...");
    }
}
