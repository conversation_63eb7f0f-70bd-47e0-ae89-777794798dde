package com.mi.oa.infra.mibpm.application.task.converter;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.resp.CcTaskResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskInstanceResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.UserTaskDefinitionResp;
import com.mi.oa.infra.mibpm.application.task.impl.detail.BpmDetailServiceImpl;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.FormSummaryDto;
import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.sdk.dto.ModelDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/2/24 4:21 PM
 **/
@Mapper(componentModel = "spring")
public interface TaskInstConverter {

    @Mapping(source = "requiredComments", target = "requiredComments")
    UserTaskDefinitionResp wrapperToDto(UserTaskWrapper wrapper);

    @Mapping(source = "predict", target = "isPredict")
    @Mapping(source = "taskAttribute.operation", target = "operation")
    @Mapping(source = "taskAttribute.comment", target = "comment")
    @Mapping(source = "taskAttribute.commentEn", target = "commentEn")
    @Mapping(source = "taskAttribute.autoOperationType", target = "autoOperationType")
    TaskInstanceResp doToDto(TaskDo taskDo);

    List<TaskInstanceResp> doToDto(List<TaskDo> taskDo);

    List<CcTaskResp> doToCcDto(List<TaskDo> taskDo);

    default List<TaskInstanceResp> doToDto(List<TaskDo> taskDos, List<ProcessInstanceDo> processInstanceDos,
                                           List<BpmUser> users, List<ModelDto> modelDtoList,
                                           List<TaskDo> currentTasks, Map<String, TaskLink> taskLinkMap) {
        List<TaskInstanceResp> result = new CopyOnWriteArrayList<>();
        Map<String, ProcessInstanceDo> procInstMap = processInstanceDos.stream()
                .collect(Collectors.toMap(ProcessInstanceDo::getProcessInstanceId, i -> i, (k1, k2) -> k1));
        Map<String, ModelDto> modelMap = modelDtoList.stream()
                .collect(Collectors.toMap(ModelDto::getModelCode, i -> i, (k1, k2) -> k1));
        Map<String, List<TaskDo>> currentTaskMap = currentTasks.stream()
                .collect(Collectors.groupingBy(TaskDo::getProcessInstanceId));
        Map<String, BpmUser> userMap = new HashMap<>();
        for (BpmUser user : users) {
            userMap.putIfAbsent(user.getUid(), user);
            userMap.putIfAbsent(user.getUserName(), user);
        }

        Map<String, Integer> sort = new HashMap<>();
        for (int i = 0; i < taskDos.size(); i++) {
            sort.put(taskDos.get(i).getTaskId(), i);
        }

        taskDos.parallelStream().forEach(task -> {
            TaskLink taskLink = taskLinkMap.get(task.getTaskId());
            String processInstanceId = task.getProcessInstanceId();
            TaskInstanceResp taskInstanceReps = doToDto(task);
            Map<String, Object> localVariables = task.getTaskLocalVariables();
            if (null != localVariables) {
                List<FormSummaryDto> summary = (List<FormSummaryDto>) localVariables.getOrDefault(
                        BpmVariablesConstants.VARIABLE_SUMMARY, new ArrayList<>());
                taskInstanceReps.setSummary(summary);
                if (null != localVariables.get(BpmVariablesConstants.VARIABLE_FAST_APPROVAL)
                        && (Boolean) localVariables.get(BpmVariablesConstants.VARIABLE_FAST_APPROVAL)) {
                    taskInstanceReps.setOperationList(
                            Lists.newArrayList(UserTaskOperation.AGREE, UserTaskOperation.REJECT));
                }
            }
            ProcessInstanceDo processInstanceDo = procInstMap.get(processInstanceId);
            ModelDto modelMeta = null;
            if (null != processInstanceDo) {
                taskInstanceReps.setProcessInstanceName(processInstanceDo.getProcessInstanceName());
                BpmUser bpmUser = userMap.get(processInstanceDo.getStartUserId());
                taskInstanceReps.setProcessInstanceStartUser(bpmUser);
                taskInstanceReps.setProcessStartTime(processInstanceDo.getStartTime());
                taskInstanceReps.setModelCode(processInstanceDo.getModelCode());
                taskInstanceReps.setProcessInstanceStatus(processInstanceDo.getProcessInstanceStatus());
                taskInstanceReps.setBusinessKey(processInstanceDo.getBusinessKey());
                modelMeta = modelMap.get(processInstanceDo.getModelCode());
                if (null != modelMeta) {
                    taskInstanceReps.setCategoryCode(modelMeta.getCategoryCode());
                    taskInstanceReps.setOldType(BpmDetailServiceImpl.calculateOldType(processInstanceDo, modelMeta));
                }
                List<TaskDo> currentTask = currentTaskMap.get(processInstanceId);
                if (null != currentTask) {
                    for (TaskDo taskDo : currentTask) {
                        BpmUser user = userMap.get(taskDo.getAssignee().getUid());
                        if (null != user) {
                            taskDo.setAssignee(user);
                        }
                    }
                    taskInstanceReps.setCurrentTasks(this.doToDto(currentTask));
                }
            }
            if (!taskLink.getFormType().isBpmFormType()) {
                if (!taskLink.getFormType().isExtHtmlFormType()) {
                    taskInstanceReps.setPcLink(taskLink.getPcLink());
                    taskInstanceReps.setMobileLink(taskLink.getMobileLink());
                }
                taskInstanceReps.setExtTask(1);
            }
            taskInstanceReps.setFormType(taskLink.getFormType().getCode());
            result.add(taskInstanceReps);
        });
        return result.stream()
                .sorted(Comparator.comparingInt(a -> sort.getOrDefault(a.getTaskId(), 0)))
                .collect(Collectors.toList());
    }

    TaskDo dtoToDo(TaskInstanceResp taskInstanceReps);

    TaskDo dtoToDo(CompleteTaskReq completeTaskReps);

    default TaskDetailHistoryResp listToDto(List<List<TaskDo>> taskList) {
        TaskDetailHistoryResp resp = new TaskDetailHistoryResp();
        List<TaskDetailHistoryResp.FlowNodeResp> taskNodes = new ArrayList<>();
        for (List<TaskDo> historicTask : taskList) {
            TaskDo task = historicTask.get(0);
            if (StringUtils.isNotBlank(task.getProcessInstanceId())) {
                resp.setProcessInstanceId(task.getProcessInstanceId());
            }
            TaskDetailHistoryResp.FlowNodeResp nodeResp = new TaskDetailHistoryResp.FlowNodeResp();
            nodeResp.setNodeId(task.getTaskDefinitionKey());
            nodeResp.setName(task.getTaskName());
            nodeResp.setTaskList(doToDto(historicTask));
            if (null != task.getUserTaskWrapper()) {
                nodeResp.setSignType(task.getUserTaskWrapper().getSignType());
            }
            nodeResp.setPredict(task.isPredict());
            taskNodes.add(nodeResp);
        }
        resp.setNodeList(taskNodes);
        return resp;
    }

    default ZonedDateTime map(Long value) {
        return ZonedDateTime.ofInstant(Instant.ofEpochSecond(value / 1000), ZoneId.systemDefault());
    }
}
