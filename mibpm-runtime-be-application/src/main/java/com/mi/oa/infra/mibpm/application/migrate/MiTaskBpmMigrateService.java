package com.mi.oa.infra.mibpm.application.migrate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.mi.flowable.external.api.ExtProcessItem;
import com.mi.flowable.external.api.ExternalService;
import com.mi.oa.infra.mibpm.application.mitask.dto.req.MiTaskMigrateReq;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.enums.SourceEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.impl.MiTaskMigrateRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.CategoryMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.CategoryPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.HiProcInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.HiTaskInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.IdentityLinkPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessInstPo;
import com.mi.oa.infra.mibpm.utils.RedisUtil;
import com.mi.oa.infra.mibpm.utils.ZoneDateTimeUtil;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仅用于 miTask数据迁移 （BPM）
 *
 * <AUTHOR>
 * @date 2024/10/22 10:50
 */
@Slf4j
@Repository
public class MiTaskBpmMigrateService {
    private static final int USER_NAME_MAX_LENGTH = 20;
    @Autowired
    private MiTaskMigrateRepository miTaskMigrateRepository;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private ModelMetaRepository modelMetaRepository;
    @Autowired
    private ExternalService externalService;
    @Autowired
    private CategoryMapper categoryMapper;

    private static final Map<String, Integer> MODEL_META_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Integer> TASK_MAP = new ConcurrentHashMap<>();

    private static final Executor EXECUTOR = MigrateTaskThreadPool.getInstance();

    public List<String> dispatch(MiTaskMigrateReq miTaskMigrateReq) {
        // retry
        if (miTaskMigrateReq.isRetryByFailList() || CollectionUtils.isNotEmpty(miTaskMigrateReq.getInstanceIds())) {
            return retry(miTaskMigrateReq);
        }

        // 调度器
        long startTime = miTaskMigrateReq.getStartTime();
        long endTime = miTaskMigrateReq.getEndTime();
        MigrateLog migrateLog = null;
        redisUtil.delete(Constants.START_MIGRATE);
        redisUtil.delete(Constants.OFFSET);
        redisUtil.delete(Constants.FAIL);
        if (redisUtil.get(Constants.START_MIGRATE) != null) {
            // 继续迁移
            migrateLog = GsonUtils.fromJson(redisUtil.get(Constants.START_MIGRATE), MigrateLog.class);
        } else {
            migrateLog = new MigrateLog(startTime, endTime, 40);

            Page<HiProcInstPo> hiProcInstPoPage = miTaskMigrateRepository.queryHiProcInstList(1, 10,
                    new Date(startTime), new Date(endTime));
            migrateLog.setAllTotal(hiProcInstPoPage.getTotal());
            redisUtil.set(Constants.START_MIGRATE, JacksonUtils.bean2Json(migrateLog));
        }
        for (MigrateLog.Part part : migrateLog.getParts()) {
            EXECUTOR.execute(() -> {
                try {
                    migrate(part);
                } catch (Exception e) {
                    log.error("MiTaskException: 数据迁移异常", e);
                } finally {
                    part.setPending(false);
                }
            });
        }
        return Collections.emptyList();
    }

    public void migrate(MigrateLog.Part part) {
        part.setPending(true);

        long totalPage = 0L;
        int curPage = part.getOffset() > 0 ? (int) part.getOffset() % 10 : 1;
        do {
            log.info("当前迁移任务ID={},页码进度 {}/{}", part.getId(), curPage, totalPage);
            Page<HiProcInstPo> hiProcInstPoPage = miTaskMigrateRepository.queryHiProcInstList(curPage, 10,
                    new Date(part.getStartTime()), new Date(part.getEndTime()));
            totalPage = hiProcInstPoPage.getPages();
            part.setTotalSize(hiProcInstPoPage.getTotal());

            List<HiProcInstPo> hiProcInstPos = hiProcInstPoPage.getRecords();
            List<String> instanceIds = hiProcInstPos.stream().map(HiProcInstPo::getId)
                    .collect(Collectors.toList());

            List<HiTaskInstPo> hiTaskInstPos = miTaskMigrateRepository.queryHiTasks(instanceIds);
            Map<String, List<HiTaskInstPo>> taskMap = hiTaskInstPos.stream()
                    .collect(Collectors.groupingBy(HiTaskInstPo::getProcInstId,
                            Collectors.mapping(Function.identity(), Collectors.toList())));

            for (HiProcInstPo hiProcInstPo : hiProcInstPos) {
                try {
                    doMigrate(hiProcInstPo, taskMap);
                } catch (Exception e) {
                    log.error("MiTaskException: 流程迁移失败，instId = {}", hiProcInstPo.getProcInstId(), e);
                    redisUtil.lRightPush(Constants.FAIL, hiProcInstPo.getProcInstId());
                }
            }
            part.incrOffset(hiProcInstPos.size(), Constants.OFFSET);
        } while (++curPage <= totalPage);
    }

    /**
     * 重试
     *
     * @param miTaskMigrateReq
     */
    public List<String> retry(MiTaskMigrateReq miTaskMigrateReq) {

        List<String> ids = miTaskMigrateReq.getInstanceIds();
        if (CollectionUtils.isEmpty(miTaskMigrateReq.getInstanceIds())) {
            ids = redisUtil.lRange(Constants.FAIL, 0, 10000);
        }
        List<HiProcInstPo> hiProcInstPos = miTaskMigrateRepository.queryHiProcInstListByIds(ids);
        List<String> instanceIds = hiProcInstPos.stream().map(HiProcInstPo::getId)
                .collect(Collectors.toList());
        List<HiTaskInstPo> hiTaskInstPos = miTaskMigrateRepository.queryHiTasks(instanceIds);
        Map<String, List<HiTaskInstPo>> taskMap = hiTaskInstPos.stream()
                .collect(Collectors.groupingBy(HiTaskInstPo::getProcInstId,
                        Collectors.mapping(Function.identity(), Collectors.toList())));
        List<String> fail = new ArrayList<>();
        for (HiProcInstPo hiProcInstPo : hiProcInstPos) {
            try {
                doMigrate(hiProcInstPo, taskMap);
            } catch (Exception e) {
                fail.add(hiProcInstPo.getProcInstId());
                log.error("MiTaskException: 流程迁移失败，instId = {}", hiProcInstPo.getProcInstId(), e);
            }
        }
        return fail;
    }

    private boolean doMigrate(HiProcInstPo hiProcInstPo, Map<String, List<HiTaskInstPo>> taskMap) {
        // 查询任务相关信息
        List<HiTaskInstPo> hiTaskInstList = taskMap.get(hiProcInstPo.getId());
        if (CollectionUtils.isEmpty(hiTaskInstList)) {
            return true;
        }
        // 插入任务
        String startTaskId = hiTaskInstList.get(0).getId();
        Date startTaskTime = hiTaskInstList.get(0).getStartTime();

        HiTaskInstPo latestTask = hiTaskInstList.get(0);
        Date latestTaskTime = hiTaskInstList.get(0).getEndTime();

        List<MiTaskProcessInstPo> miTaskProcessInstPos = new ArrayList<>();
        Map<String, MiTaskProcessInstPo> map = new HashMap<>();
        for (HiTaskInstPo hiTaskInstPo : hiTaskInstList) {
            if (hiTaskInstPo.getStartTime().compareTo(startTaskTime) < 0) {
                startTaskTime = hiTaskInstPo.getStartTime();
                startTaskId = hiTaskInstPo.getId();
            }
            if (latestTaskTime != null && (hiTaskInstPo.getEndTime() == null
                    || hiTaskInstPo.getEndTime().compareTo(latestTaskTime) > 0)) {
                latestTaskTime = hiTaskInstPo.getEndTime();
                latestTask = hiTaskInstPo;
            }
            MiTaskProcessInstPo miTask = toMiTask(hiTaskInstPo, hiProcInstPo);
            if (StringUtils.isBlank(miTask.getAssignee()) && hiTaskInstPo.getEndTime() == null) {
                List<IdentityLinkPo> identityLinkPos
                        = miTaskMigrateRepository.queryIdentityLinkByTaskId(miTask.getTaskId());
                for (IdentityLinkPo identityLinkPo : identityLinkPos) {
                    MiTaskProcessInstPo identityTask = toMiTask(hiTaskInstPo, hiProcInstPo);
                    identityTask.setParentTaskId(hiTaskInstPo.getId());
                    identityTask.setTaskId(String.format("%s:%s", hiTaskInstPo.getId(), UUID.randomUUID()));
                    identityTask.setAssignee(findUser(identityLinkPo.getUserId()));
                    identityTask.setSignType(UserTaskSignType.COMPETITION.name());
                    miTaskProcessInstPos.add(identityTask);
                }
            }
            map.put(miTask.getTaskId(), miTask);
            miTaskProcessInstPos.add(miTask);
        }
        // process to miTask
        miTaskProcessInstPos.add(toMiTask(hiProcInstPo));
        map.get(startTaskId).setIsStartTask(true);
        resetProcessInstanceStatus(hiProcInstPo, latestTask, miTaskProcessInstPos);
        try {
            miTaskMigrateRepository.saveBatchMiTask(miTaskProcessInstPos, null);
        } catch (Exception e) {
            miTaskMigrateRepository.deleteByInstanceId(hiProcInstPo.getProcInstId());
            miTaskMigrateRepository.saveBatchMiTask(miTaskProcessInstPos, null);
        }
        return false;
    }

    private void resetProcessInstanceStatus(HiProcInstPo hiProcInstPo, HiTaskInstPo latestTask, List<MiTaskProcessInstPo> miTaskProcessInstPos) {
        ProcessInstanceStatus status = ProcessInstanceStatus.getStatus(findDesc(latestTask.getDescription()).getRight(),
                hiProcInstPo.getEndTime() == null ? null : ZoneDateTimeUtil.getZonedDateTime(hiProcInstPo.getEndTime()));
        String processInstanceStatus = status == null ? ProcessInstanceStatus.RUNNING.getCode() : status.getCode();
        if (status == null) {
            if (hiProcInstPo.getEndTime() != null) {
                processInstanceStatus = ProcessInstanceStatus.COMPLETED.getCode();
            }
        }
        for (MiTaskProcessInstPo miTaskProcessInstPo : miTaskProcessInstPos) {
            miTaskProcessInstPo.setProcessInstanceStatus(processInstanceStatus);
        }
    }

    private MiTaskProcessInstPo toMiTask(HiTaskInstPo hiTaskInstPo, HiProcInstPo hiProcInstPo) {

        MiTaskProcessInstPo taskProcessInstPo = new MiTaskProcessInstPo();
        taskProcessInstPo.setIsStartTask(false);
        Date lastUpdatedTime = hiTaskInstPo.getLastUpdatedTime();
        if (lastUpdatedTime == null) {
            taskProcessInstPo.setUpdateTime(hiTaskInstPo.getEndTime() == null
                    ? hiTaskInstPo.getStartTime().getTime() : hiTaskInstPo.getEndTime().getTime());
        } else {
            taskProcessInstPo.setUpdateTime(lastUpdatedTime.getTime());
        }
        taskProcessInstPo.setProcInstStarter(findUser(hiProcInstPo.getStartUserId()));
        taskProcessInstPo.setBusinessKey(hiProcInstPo.getBusinessKey() == null ? hiProcInstPo.getId() : hiProcInstPo.getBusinessKey());
        taskProcessInstPo.setIsFastApproval(false);
        Pair<String, String> desc = findDesc(hiTaskInstPo.getDescription());
        taskProcessInstPo.setClient(desc.getLeft());
        taskProcessInstPo.setProcInstId(hiTaskInstPo.getProcInstId());
        taskProcessInstPo.setProcInstName(hiProcInstPo.getName());
        taskProcessInstPo.setProcDefId(hiTaskInstPo.getProcDefId());
        taskProcessInstPo.setModelCode(hiProcInstPo.getProcDefId().split(":")[0]);
        taskProcessInstPo.setTaskDefKey(hiTaskInstPo.getTaskDefKey());
        taskProcessInstPo.setClaimTime(hiTaskInstPo.getClaimTime() == null ? null : hiTaskInstPo.getClaimTime().getTime());
        taskProcessInstPo.setStartTime(hiTaskInstPo.getStartTime() == null ? null : hiTaskInstPo.getStartTime().getTime());
        taskProcessInstPo.setCreateTime(hiTaskInstPo.getStartTime().getTime());
        taskProcessInstPo.setDueDate(hiTaskInstPo.getDueDate() == null ? null : Math.max(hiTaskInstPo.getDueDate().getTime(), 0));
        taskProcessInstPo.setDuration((hiTaskInstPo.getDuration() == null || hiTaskInstPo.getDuration() < 0)
                ? 0 : hiTaskInstPo.getDuration());
        taskProcessInstPo.setSource(findSource(taskProcessInstPo.getModelCode()));
        taskProcessInstPo.setTaskId(hiTaskInstPo.getId());
        taskProcessInstPo.setExecutionId(hiTaskInstPo.getExecutionId());
        taskProcessInstPo.setTaskName(hiTaskInstPo.getName());
        taskProcessInstPo.setParentTaskId(hiTaskInstPo.getParentTaskId());
        taskProcessInstPo.setOwner(findUser(hiTaskInstPo.getOwner()));
        taskProcessInstPo.setOperation(desc.getRight());
        taskProcessInstPo.setUpdateUser("robot");
        taskProcessInstPo.setAssignee(findUser(hiTaskInstPo.getAssignee()));
        if (hiTaskInstPo.getEndTime() != null) {
            taskProcessInstPo.setEndTime(hiTaskInstPo.getEndTime().getTime());
        }

        if (hiTaskInstPo.getDueDate() != null) {
            taskProcessInstPo.setDueDate(hiTaskInstPo.getDueDate().getTime());
        }
        // 暂时不考虑
        taskProcessInstPo.setSignType(null);
        if (taskProcessInstPo.getDueDate() != null && taskProcessInstPo.getDueDate() < 0) {
            taskProcessInstPo.setDueDate(null);
        }

        return taskProcessInstPo;
    }

    private MiTaskProcessInstPo toMiTask(HiProcInstPo hiProcInstPo) {

        MiTaskProcessInstPo taskProcessInstPo = new MiTaskProcessInstPo();
        taskProcessInstPo.setIsStartTask(false);
        Date lastUpdatedTime = hiProcInstPo.getEndTime();
        if (lastUpdatedTime == null) {
            taskProcessInstPo.setUpdateTime(hiProcInstPo.getStartTime().getTime());
        } else {
            taskProcessInstPo.setUpdateTime(lastUpdatedTime.getTime());
        }
        taskProcessInstPo.setProcInstStarter(findUser(hiProcInstPo.getStartUserId()));
        taskProcessInstPo.setBusinessKey(hiProcInstPo.getBusinessKey() == null ? hiProcInstPo.getId() : hiProcInstPo.getBusinessKey());
        taskProcessInstPo.setIsFastApproval(false);
        taskProcessInstPo.setClient("");
        taskProcessInstPo.setProcInstId(hiProcInstPo.getProcInstId());
        taskProcessInstPo.setProcInstName(hiProcInstPo.getName());
        taskProcessInstPo.setProcDefId(hiProcInstPo.getProcDefId());
        taskProcessInstPo.setModelCode(hiProcInstPo.getProcDefId().split(":")[0]);
        taskProcessInstPo.setTaskDefKey("");
        taskProcessInstPo.setClaimTime(0L);
        taskProcessInstPo.setStartTime(hiProcInstPo.getStartTime().getTime());
        taskProcessInstPo.setCreateTime(hiProcInstPo.getStartTime().getTime());
        taskProcessInstPo.setDueDate(0L);
        taskProcessInstPo.setDuration((hiProcInstPo.getDuration() == null || hiProcInstPo.getDuration() < 0)
                ? 0 : hiProcInstPo.getDuration());
        taskProcessInstPo.setSource(findSource(taskProcessInstPo.getModelCode()));
        taskProcessInstPo.setTaskId("");
        taskProcessInstPo.setExecutionId(hiProcInstPo.getId());
        taskProcessInstPo.setTaskName("");
        taskProcessInstPo.setParentTaskId("");
        taskProcessInstPo.setOwner("");
        taskProcessInstPo.setOperation("");
        taskProcessInstPo.setUpdateUser("robot");
        taskProcessInstPo.setAssignee("");
        taskProcessInstPo.setEndTime(hiProcInstPo.getEndTime() == null ? 0L : hiProcInstPo.getEndTime().getTime());

        // 暂时不考虑
        taskProcessInstPo.setSignType(null);
        if (taskProcessInstPo.getDueDate() != null && taskProcessInstPo.getDueDate() < 0) {
            taskProcessInstPo.setDueDate(null);
        }

        return taskProcessInstPo;
    }

    private String findUser(String username) {
        if (username != null && username.length() > USER_NAME_MAX_LENGTH) {
            BpmUser user = accountRemoteService.getUser(username);
            if (user != null) {
                return user.getUserName();
            }
            if (username.contains(",")) {
                // 兼容1.0数据
                return username.split(",")[0];
            }
            return username;
        }
        return username;
    }

    private String findSource(String modelCode) {
        if (!MODEL_META_MAP.containsKey(modelCode)) {
            ModelMeta modelMeta = modelMetaRepository.queryByModelCode(modelCode);
            if (modelMeta == null) {
                log.error("MiTaskException: No model metadata found, modelCode={}", modelCode);
                if (modelCode.startsWith("bpmn_")) {
                    MODEL_META_MAP.put(modelCode, 0);
                } else {
                    MODEL_META_MAP.put(modelCode, 1);
                }
            } else {
                if (modelMeta.getSource() == 1) {
                    return SourceEnum.BAI_TE_DA.name();
                }
                MODEL_META_MAP.put(modelCode, modelMeta.getFromOld());
            }
        }
        Integer fromOld = MODEL_META_MAP.get(modelCode);
        switch (fromOld) {
            case 0:
                return SourceEnum.BPM3_PROCESS.name();
            case 1:
                return SourceEnum.BPM1_PROCESS.name();
            case 2:
                return SourceEnum.BPM2_PROCESS.name();
            default:
                return "";
        }
    }

    private Pair<String, String> findDesc(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return Pair.of("WEB", "agree");
        }
        JsonObject jsonObject = JsonParser
                .parseString(desc).getAsJsonObject();
        String source = jsonObject.get("source") == null ? "WEB" : jsonObject.get("source").getAsString();
        String status = jsonObject.get("status") == null ? "agree" : jsonObject.get("status").getAsString();
        // 获取指定键的值
        return Pair.of(source, status);
    }

    public void migrateCategory() {
        List<ExtProcessItem> list = externalService.createProcessItemQuery()
                .list();
        for (ExtProcessItem extProcessItem : list) {
            if (!extProcessItem.getParentKey().equals("CATE")) {
                continue;
            }
            CategoryPo parent = new CategoryPo();
            parent.setExtKey("");
            parent.setName(extProcessItem.getProcessName());
            parent.setEnName(extProcessItem.getProcessEnName());
            parent.setSupportPc(extProcessItem.getSupportPc());
            parent.setSupportApp(extProcessItem.getSupportMobile());
            parent.setSort(0);
            parent.setDescription("ext_category");
            parent.setAdminUser(new ArrayList<>());
            parent.setChecker(new ArrayList<>());
            parent.setCode("ext_".concat(generateRandomNumberString()));
            categoryMapper.insert(parent);

            CategoryPo categoryPo = new CategoryPo();
            categoryPo.setExtKey(extProcessItem.getDefKey());
            categoryPo.setName(extProcessItem.getProcessName());
            categoryPo.setEnName(extProcessItem.getProcessEnName());
            categoryPo.setSupportPc(extProcessItem.getSupportPc());
            categoryPo.setSupportApp(extProcessItem.getSupportMobile());
            categoryPo.setSort(0);
            categoryPo.setDescription("ext_category");
            categoryPo.setAdminUser(new ArrayList<>());
            categoryPo.setChecker(new ArrayList<>());
            categoryPo.setCode(extProcessItem.getProcessKey());
            categoryPo.setParentCode(parent.getCode());
            categoryMapper.insert(categoryPo);
        }
    }

    private String generateRandomNumberString() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        sb.append(random.nextInt(9) + 1);
        for (int i = 1; i < 20; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
}
