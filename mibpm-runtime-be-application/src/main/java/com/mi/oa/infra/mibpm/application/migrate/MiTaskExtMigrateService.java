package com.mi.oa.infra.mibpm.application.migrate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.infra.mibpm.application.mitask.dto.req.MiTaskMigrateReq;
import com.mi.oa.infra.mibpm.common.enums.SourceEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.infra.repository.impl.MiTaskMigrateRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExtHiProcInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExtHiTaskInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExtHiVarInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExtRuTaskInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessExtPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessInstPo;
import com.mi.oa.infra.mibpm.utils.RedisUtil;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仅用于 miTask数据迁移 (统一待办)
 *
 * <AUTHOR>
 * @date 2024/10/22 10:50
 */
@Slf4j
@Repository
public class MiTaskExtMigrateService {
    @Autowired
    private MiTaskMigrateRepository miTaskMigrateRepository;
    @Autowired
    private RedisUtil redisUtil;

    private static final Executor EXECUTOR = MigrateTaskThreadPool.getInstance();

    public List<String> dispatch(MiTaskMigrateReq miTaskMigrateReq) {
        // retry
        if (miTaskMigrateReq.isRetryByFailList() || CollectionUtils.isNotEmpty(miTaskMigrateReq.getInstanceIds())) {
            return retry(miTaskMigrateReq);
        }

        // 调度器
        long startTime = miTaskMigrateReq.getStartTime();
        long endTime = miTaskMigrateReq.getEndTime();
        MigrateLog migrateLog = null;
        redisUtil.delete(Constants.START_MIGRATE_EXT);
        redisUtil.delete(Constants.OFFSET);
        redisUtil.delete("fail");
        if (redisUtil.get(Constants.START_MIGRATE_EXT) != null) {
            // 继续迁移
            migrateLog = GsonUtils.fromJson(redisUtil.get(Constants.START_MIGRATE), MigrateLog.class);
        } else {
            migrateLog = new MigrateLog(startTime, endTime, 40);

            Page<ExtHiProcInstPo> hiProcInstPoPage = miTaskMigrateRepository.queryExtHiProcInstList(1, 10,
                    startTime, endTime);
            migrateLog.setAllTotal(hiProcInstPoPage.getTotal());
            redisUtil.set(Constants.START_MIGRATE_EXT, JacksonUtils.bean2Json(migrateLog));
        }
        for (MigrateLog.Part part : migrateLog.getParts()) {
            EXECUTOR.execute(() -> {
                try {
                    doMigrate(part);
                } catch (Exception e) {
                    log.error("数据迁移异常", e);
                } finally {
                    part.setPending(false);
                }
            });
        }
        return Collections.emptyList();
    }

//    public void doMigrate(MigrateLog.Part part) {
//        part.setPending(true);
//
//        long totalPage = 0;
//        int curPage = part.getOffset() > 0 ? (int) part.getOffset() % 10 : 1;
//        do {
//            log.info("当前迁移任务ID={},页码进度 {}/{}", part.getId(), curPage, totalPage);
//            Page<ExtHiProcInstPo> hiProcInstPoPage = miTaskMigrateRepository.queryExtHiProcInstList(curPage, 10,
//                    part.getStartTime(), part.getEndTime());
//            totalPage = hiProcInstPoPage.getPages();
//            part.setTotalSize(hiProcInstPoPage.getTotal());
//
//            List<ExtHiProcInstPo> hiProcInstPos = hiProcInstPoPage.getRecords();
//            List<String> instanceIds = hiProcInstPos.stream().map(ExtHiProcInstPo::getId)
//                    .collect(Collectors.toList());
//
//            List<ExtHiTaskInstPo> hiTaskInstPos = miTaskMigrateRepository.queryExtHiTasks(instanceIds);
//            Map<String, List<ExtHiTaskInstPo>> taskMap = hiTaskInstPos.stream()
//                    .collect(Collectors.groupingBy(ExtHiTaskInstPo::getProcInstId,
//                            Collectors.mapping(Function.identity(), Collectors.toList())));
//
//            List<ExtHiVarInstPo> extHiVarInstPos = miTaskMigrateRepository.queryExtHiVars(instanceIds);
//            Map<String, List<ExtHiVarInstPo>> varMap = extHiVarInstPos.stream()
//                    .collect(Collectors.groupingBy(ExtHiVarInstPo::getProcInstId,
//                            Collectors.mapping(Function.identity(), Collectors.toList())));
//            for (ExtHiProcInstPo hiProcInstPo : hiProcInstPos) {
//                try {
//                    doMigrate(hiProcInstPo, taskMap, varMap);
//                } catch (Exception e) {
//                    log.error("流程迁移失败，instId = {}", hiProcInstPo.getId(), e);
//                    redisUtil.lRightPush("fail", hiProcInstPo.getId());
//                }
//            }
//            part.incrOffset(hiProcInstPos.size(), Constants.OFFSET_EXT);
//        } while (++curPage <= totalPage);
//    }
//
//    public List<String> retry(MiTaskMigrateReq miTaskMigrateReq) {
//        List<String> ids = miTaskMigrateReq.getInstanceIds();
//        if (CollectionUtils.isEmpty(miTaskMigrateReq.getInstanceIds())) {
//            ids = redisUtil.lRange(Constants.FAIL, 0, 10000);
//        }
//        List<ExtHiProcInstPo> hiProcInstPos = miTaskMigrateRepository.queryExtHiProcInstListByIds(ids);
//        List<String> instanceIds = hiProcInstPos.stream().map(ExtHiProcInstPo::getId)
//                .collect(Collectors.toList());
//
//        List<ExtHiTaskInstPo> hiTaskInstPos = miTaskMigrateRepository.queryExtHiTasks(instanceIds);
//        Map<String, List<ExtHiTaskInstPo>> taskMap = hiTaskInstPos.stream()
//                .collect(Collectors.groupingBy(ExtHiTaskInstPo::getProcInstId,
//                        Collectors.mapping(Function.identity(), Collectors.toList())));
//
//        List<ExtHiVarInstPo> extHiVarInstPos = miTaskMigrateRepository.queryExtHiVars(instanceIds);
//        Map<String, List<ExtHiVarInstPo>> varMap = extHiVarInstPos.stream()
//                .collect(Collectors.groupingBy(ExtHiVarInstPo::getProcInstId,
//                        Collectors.mapping(Function.identity(), Collectors.toList())));
//        List<String> failIds = new ArrayList<>();
//        for (ExtHiProcInstPo hiProcInstPo : hiProcInstPos) {
//            try {
//                doMigrate(hiProcInstPo, taskMap, varMap);
//            } catch (Exception e) {
//                log.error("流程迁移失败，instId = {}", hiProcInstPo.getId(), e);
//                failIds.add(hiProcInstPo.getId());
//            }
//        }
//        return failIds;
//    }
//
//    private boolean doMigrate(ExtHiProcInstPo hiProcInstPo, Map<String, List<ExtHiTaskInstPo>> taskMap, Map<String, List<ExtHiVarInstPo>> varMap) {
//        // 查询任务相关信息
//        List<ExtHiTaskInstPo> hiTaskInstList = taskMap.get(hiProcInstPo.getId());
//        if (CollectionUtils.isEmpty(hiTaskInstList)) {
//            return true;
//        }
//        Map<String, ExtHiTaskInstPo> taskInstPoMap = hiTaskInstList.stream()
//                .collect(Collectors.toMap(ExtHiTaskInstPo::getId, Function.identity(),
//                        (v1, v2) -> v1));
//        // 查询变量相关信息
//        List<ExtHiVarInstPo> hiVarInstList = varMap.getOrDefault(hiProcInstPo.getId(), new ArrayList<>());
//        List<MiTaskProcessInstPo> miTaskProcessInstPos = new ArrayList<>();
//        List<MiTaskProcessExtPo> miTaskProcessExtPos = new ArrayList<>();
//        miTaskProcessInstPos.add(extProcToMiTask(hiProcInstPo));
//
//        // 处理任务ID重复的记录
//        Map<String, ExtHiTaskInstPo> map = new HashMap<>();
//        for (ExtHiTaskInstPo hiTaskInstPo : hiTaskInstList) {
//            // 如果Map中不存在该taskId，或者当前task的updateTime更大，则更新Map中的记录
//            if (!map.containsKey(hiTaskInstPo.getTaskKey())
//                    || hiTaskInstPo.getUpdateTime() > map.get(hiTaskInstPo.getTaskKey()).getUpdateTime()) {
//                map.put(hiTaskInstPo.getTaskKey(), hiTaskInstPo);
//            }
//        }
//        hiTaskInstList = new ArrayList<>(map.values());
//
//        for (ExtHiTaskInstPo hiTaskInstPo : hiTaskInstList) {
//            MiTaskProcessInstPo miTask = extTaskToMiTask(hiTaskInstPo, hiProcInstPo, null);
//            // add link var
//            addMiTaskLinkVar(hiProcInstPo, hiTaskInstPo, miTaskProcessExtPos);
//            miTaskProcessInstPos.add(miTask);
//        }
//        for (ExtHiVarInstPo extHiVarInstPo : hiVarInstList) {
//            miTaskProcessExtPos.add(extVarToMiTaskVar(hiProcInstPo, extHiVarInstPo, taskInstPoMap));
//        }
//
//        miTaskMigrateRepository.deleteByInstanceId(hiProcInstPo.getId());
//        miTaskMigrateRepository.deleteExtByInstanceId(hiProcInstPo.getId());
//        try {
//            miTaskMigrateRepository.saveBatchMiTask(miTaskProcessInstPos, null);
//            miTaskMigrateRepository.saveBatchExtVars(miTaskProcessExtPos);
//        } catch (Exception e) {
//            miTaskMigrateRepository.deleteByInstanceId(hiProcInstPo.getBusinessKey());
//            miTaskMigrateRepository.deleteExtByInstanceId(hiProcInstPo.getBusinessKey());
//
//            miTaskMigrateRepository.saveBatchExtVars(miTaskProcessExtPos);
//            miTaskMigrateRepository.saveBatchMiTask(miTaskProcessInstPos, null);
//        }
//        return false;
//    }

    public void doMigrate(MigrateLog.Part part) {
        part.setPending(true);

        long totalPage = 0;
        int curPage = part.getOffset() > 0 ? (int) part.getOffset() % 10 : 1;
        do {
            log.info("当前校验任务ID={},页码进度 {}/{}", part.getId(), curPage, totalPage);
            Page<ExtHiProcInstPo> hiProcInstPoPage = miTaskMigrateRepository.queryExtHiProcInstList(curPage, 10,
                    part.getStartTime(), part.getEndTime());
            totalPage = hiProcInstPoPage.getPages();
            part.setTotalSize(hiProcInstPoPage.getTotal());

            List<ExtHiProcInstPo> hiProcInstPos = hiProcInstPoPage.getRecords();
            List<String> instanceIds = hiProcInstPos.stream().map(ExtHiProcInstPo::getId)
                    .collect(Collectors.toList());

            List<ExtRuTaskInstPo> ruTaskInstPos = miTaskMigrateRepository.queryExtRuTasksByInstanceId(instanceIds);
            Map<String, List<ExtRuTaskInstPo>> taskMap = ruTaskInstPos.stream()
                    .collect(Collectors.groupingBy(ExtRuTaskInstPo::getProcInstId,
                            Collectors.mapping(Function.identity(), Collectors.toList())));
            for (ExtHiProcInstPo hiProcInstPo : hiProcInstPos) {
                try {
                    doMigrate(hiProcInstPo, taskMap);
                } catch (Exception e) {
                    log.error("流程迁移失败，instId = {}", hiProcInstPo.getId(), e);
                    redisUtil.lRightPush("fail", hiProcInstPo.getId());
                }
            }
            part.incrOffset(hiProcInstPos.size(), Constants.OFFSET_EXT);
        } while (++curPage <= totalPage);
    }

    public List<String> retry(MiTaskMigrateReq miTaskMigrateReq) {
        List<String> ids = miTaskMigrateReq.getInstanceIds();
        if (CollectionUtils.isEmpty(miTaskMigrateReq.getInstanceIds())) {
            ids = redisUtil.lRange(Constants.FAIL, 0, 10000);
        }
        List<ExtHiProcInstPo> hiProcInstPos = miTaskMigrateRepository.queryExtHiProcInstListByIds(ids);
        List<String> instanceIds = hiProcInstPos.stream().map(ExtHiProcInstPo::getId)
                .collect(Collectors.toList());

        List<ExtRuTaskInstPo> ruTaskInstPos = miTaskMigrateRepository.queryExtRuTasksByInstanceId(instanceIds);
        Map<String, List<ExtRuTaskInstPo>> taskMap = ruTaskInstPos.stream()
                .collect(Collectors.groupingBy(ExtRuTaskInstPo::getProcInstId,
                        Collectors.mapping(Function.identity(), Collectors.toList())));
        for (ExtHiProcInstPo hiProcInstPo : hiProcInstPos) {
            try {
                doMigrate(hiProcInstPo, taskMap);
            } catch (Exception e) {
                log.error("流程迁移失败，instId = {}", hiProcInstPo.getId(), e);
                redisUtil.lRightPush("fail", hiProcInstPo.getId());
            }
        }
        return null;
    }

    private boolean doMigrate(ExtHiProcInstPo hiProcInstPo, Map<String, List<ExtRuTaskInstPo>> taskMap) {
        // 查询任务相关信息
        List<ExtRuTaskInstPo> hiTaskInstList = taskMap.get(hiProcInstPo.getId());
        Map<String, ExtRuTaskInstPo> taskInstPoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hiTaskInstList)) {
            taskInstPoMap = hiTaskInstList.stream()
                    .collect(Collectors.toMap(ExtRuTaskInstPo::getTaskKey, Function.identity(),
                            (v1, v2) -> v1));
        }


        List<Long> removeTaskIds = new ArrayList<>();
        List<MiTaskProcessInstPo> miTaskProcessInstPos = miTaskMigrateRepository.queryMiTaskRuTask(hiProcInstPo.getBusinessKey());
        for (MiTaskProcessInstPo miTaskProcessInstPo : miTaskProcessInstPos) {
            // 历史表中的待办任务在待办表中不存在
            if (!taskInstPoMap.containsKey(miTaskProcessInstPo.getTaskId())) {
                removeTaskIds.add(miTaskProcessInstPo.getId());
            }
        }
//        if (hiProcInstPo.getEndTime() != null && hiProcInstPo.getEndTime() != 0) {
//            // COMPLETED
//            // 更新流程的结束时间
//            miTaskMigrateRepository.updateBatchMiTaskEndTime(hiProcInstPo.getBusinessKey(), hiProcInstPo.getEndTime());
//            // 更新所有任务的状态
//            miTaskMigrateRepository.updateBatchMiTaskStatus(hiProcInstPo.getBusinessKey(), "COMPLETED");
//        } else {
//            // RUNNING
//            // 更新所有的任务状态
//            miTaskMigrateRepository.updateBatchMiTaskStatus(hiProcInstPo.getBusinessKey(), "RUNNING");
//        }
        miTaskMigrateRepository.deleteMiTaskByIds(removeTaskIds);
        return false;
    }

    private MiTaskProcessExtPo extVarToMiTaskVar(ExtHiProcInstPo hiProcInstPo, ExtHiVarInstPo hiVariablePo, Map<String, ExtHiTaskInstPo> taskInstPoMap) {
        MiTaskProcessExtPo miTaskProcessExtPo = new MiTaskProcessExtPo();
        miTaskProcessExtPo.setProcInstId(hiProcInstPo.getBusinessKey());
        ExtHiTaskInstPo extHiTaskInstPo = taskInstPoMap.get(hiVariablePo.getTaskId());
        if (extHiTaskInstPo == null || StringUtils.isBlank(hiVariablePo.getTaskId())) {
            miTaskProcessExtPo.setTaskId("");
        } else {
            miTaskProcessExtPo.setTaskId(extHiTaskInstPo.getTaskKey());
        }
        miTaskProcessExtPo.setName(hiVariablePo.getName());
        miTaskProcessExtPo.setType(hiVariablePo.getType());
        miTaskProcessExtPo.setTextValue(hiVariablePo.getTextValue());
        miTaskProcessExtPo.setDoubleValue(hiVariablePo.getDoubleValue());
        miTaskProcessExtPo.setLongValue(hiVariablePo.getLongValue());
        miTaskProcessExtPo.setCreateTime(hiVariablePo.getCreateTime());
        miTaskProcessExtPo.setUpdateTime(hiVariablePo.getUpdateTime());
        return miTaskProcessExtPo;
    }

    private void addMiTaskLinkVar(ExtHiProcInstPo hiProcInstPo, ExtHiTaskInstPo extHiTaskInstPo, List<MiTaskProcessExtPo> miTaskProcessExtPos) {
        MiTaskProcessExtPo pcLinkVar = new MiTaskProcessExtPo();

        pcLinkVar.setProcInstId(hiProcInstPo.getBusinessKey());
        pcLinkVar.setTaskId(extHiTaskInstPo.getTaskKey());
        pcLinkVar.setName("pcLink");
        pcLinkVar.setType("text");
        pcLinkVar.setTextValue(StringUtils.isBlank(extHiTaskInstPo.getPcLink()) ? hiProcInstPo.getPcLink() : extHiTaskInstPo.getPcLink());
        pcLinkVar.setDoubleValue(0.0);
        pcLinkVar.setLongValue(0L);
        pcLinkVar.setCreateTime(extHiTaskInstPo.getCreateTime());
        pcLinkVar.setUpdateTime(extHiTaskInstPo.getUpdateTime());

        MiTaskProcessExtPo mobileLinkVar = new MiTaskProcessExtPo();
        mobileLinkVar.setProcInstId(hiProcInstPo.getBusinessKey());
        mobileLinkVar.setTaskId(extHiTaskInstPo.getTaskKey());
        mobileLinkVar.setName("mobileLink");
        mobileLinkVar.setType("text");
        mobileLinkVar.setTextValue(StringUtils.isBlank(extHiTaskInstPo.getMobileLink()) ? hiProcInstPo.getMobileLink() : extHiTaskInstPo.getMobileLink());
        mobileLinkVar.setDoubleValue(0.0);
        mobileLinkVar.setLongValue(0L);
        mobileLinkVar.setCreateTime(extHiTaskInstPo.getCreateTime());
        mobileLinkVar.setUpdateTime(extHiTaskInstPo.getUpdateTime());

        miTaskProcessExtPos.add(mobileLinkVar);
        miTaskProcessExtPos.add(pcLinkVar);
    }

    private MiTaskProcessInstPo extTaskToMiTask(ExtHiTaskInstPo hiTaskInstPo, ExtHiProcInstPo hiProcInstPo, ExtHiVarInstPo hiVariablePo) {

        MiTaskProcessInstPo taskProcessInstPo = new MiTaskProcessInstPo();
        taskProcessInstPo.setIsStartTask(false);
        taskProcessInstPo.setUpdateTime(hiTaskInstPo.getUpdateTime());

        taskProcessInstPo.setProcInstStarter(hiProcInstPo.getStartUserId());
        taskProcessInstPo.setBusinessKey(hiProcInstPo.getBusinessKey());
        taskProcessInstPo.setProcessInstanceStatus((hiProcInstPo.getEndTime() == null || hiProcInstPo.getEndTime() == 0) ? "RUNNING" : "COMPLETED");
        taskProcessInstPo.setIsFastApproval(false);
        taskProcessInstPo.setClient("WEB");
        taskProcessInstPo.setProcInstId(hiProcInstPo.getBusinessKey());
        taskProcessInstPo.setProcDefId(hiTaskInstPo.getProcDefId());
        taskProcessInstPo.setModelCode(hiProcInstPo.getProcDefId().split(":")[0]);
        taskProcessInstPo.setTaskDefKey("");
        taskProcessInstPo.setClaimTime(null);
        taskProcessInstPo.setStartTime(hiTaskInstPo.getCreateTime());
        taskProcessInstPo.setCreateTime(hiTaskInstPo.getCreateTime());
        taskProcessInstPo.setDueDate(0L);
        taskProcessInstPo.setDuration((hiTaskInstPo.getDuration() == null || hiTaskInstPo.getDuration() < 0)
                ? 0 : hiTaskInstPo.getDuration());
        taskProcessInstPo.setSource(SourceEnum.UNIFIED_TODO_EXTERNAL.name());
        taskProcessInstPo.setTaskId(hiTaskInstPo.getTaskKey());
        taskProcessInstPo.setExecutionId(hiTaskInstPo.getId());
        taskProcessInstPo.setTaskName(hiTaskInstPo.getName());
        taskProcessInstPo.setProcInstName(hiProcInstPo.getName());
        taskProcessInstPo.setParentTaskId("");
        taskProcessInstPo.setOwner(hiTaskInstPo.getOwner());
        taskProcessInstPo.setOperation(statusConvert(hiTaskInstPo.getStatus()));
        taskProcessInstPo.setUpdateUser("robot");
        taskProcessInstPo.setAssignee(hiTaskInstPo.getAssignee());
        taskProcessInstPo.setEndTime(hiTaskInstPo.getEndTime());

        if (hiTaskInstPo.getDueDate() != null) {
            taskProcessInstPo.setDueDate(hiTaskInstPo.getDueDate().getTime());
        }
        return taskProcessInstPo;
    }

    private MiTaskProcessInstPo extProcToMiTask(ExtHiProcInstPo hiProcInstPo) {
        MiTaskProcessInstPo taskProcessInstPo = new MiTaskProcessInstPo();
        taskProcessInstPo.setIsStartTask(true);
        taskProcessInstPo.setUpdateTime(hiProcInstPo.getUpdateTime());

        taskProcessInstPo.setProcInstStarter(hiProcInstPo.getStartUserId());
        taskProcessInstPo.setBusinessKey(hiProcInstPo.getBusinessKey());
        taskProcessInstPo.setProcessInstanceStatus((hiProcInstPo.getEndTime() == null || hiProcInstPo.getEndTime() == 0) ? "RUNNING" : "COMPLETED");
        taskProcessInstPo.setIsFastApproval(false);
        taskProcessInstPo.setClient("WEB");
        taskProcessInstPo.setProcInstId(hiProcInstPo.getBusinessKey());
        taskProcessInstPo.setProcDefId(hiProcInstPo.getProcDefId());
        taskProcessInstPo.setModelCode(hiProcInstPo.getProcDefId().split(":")[0]);
        taskProcessInstPo.setTaskDefKey("");
        taskProcessInstPo.setClaimTime(null);
        taskProcessInstPo.setStartTime(hiProcInstPo.getCreateTime());
        taskProcessInstPo.setCreateTime(hiProcInstPo.getCreateTime());
        taskProcessInstPo.setDueDate(0L);
        taskProcessInstPo.setProcInstName(hiProcInstPo.getName());
        taskProcessInstPo.setDuration((hiProcInstPo.getDuration() == null || hiProcInstPo.getDuration() < 0)
                ? 0 : hiProcInstPo.getDuration());
        taskProcessInstPo.setSource(SourceEnum.UNIFIED_TODO_EXTERNAL.name());
        taskProcessInstPo.setTaskId("");
        taskProcessInstPo.setExecutionId(hiProcInstPo.getId());
        taskProcessInstPo.setTaskName("");
        // 统一待办无此概念
        taskProcessInstPo.setParentTaskId("");
        taskProcessInstPo.setOwner("");
        taskProcessInstPo.setOperation("");
        taskProcessInstPo.setUpdateUser("robot");
        taskProcessInstPo.setAssignee("");
        taskProcessInstPo.setEndTime(hiProcInstPo.getEndTime());
        return taskProcessInstPo;
    }

    private String statusConvert(String status) {
        UserTaskOperation userTaskOperation = null;
        switch (status) {
            case "APPROVED":
            case "DONE":
                userTaskOperation = UserTaskOperation.AGREE;
                break;
            case "PENDING":
                break;
            case "REJECTED":
                userTaskOperation = UserTaskOperation.REJECT;
                break;
            case "TRANSFERRED":
                userTaskOperation = UserTaskOperation.TRANSFER;
                break;
            default:
                userTaskOperation = UserTaskOperation.AGREE;
        }
        return userTaskOperation == null ? "" : userTaskOperation.getCode();
    }
}
