package com.mi.oa.infra.mibpm.application.apicall.dto;

import com.mi.oa.infra.mibpm.common.model.BpmOrg;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 用户对象
 *
 * <AUTHOR>
 * @date 2022/05/18 16:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCallBpmUserDto {
    /**
     * uid
     */
    protected String uid;
    /**
     * 用户名
     */
    protected String userName;
    /**
     * 显示名
     */
    protected String displayName;
    /**
     * 工号
     */
    protected String personId;
    /**
     * 组织信息
     */
    protected BpmOrg org;

    public static RemoteCallBpmUserDto convert(BpmUser bpmUser) {
        if (Objects.isNull(bpmUser)) {
            return null;
        }
        RemoteCallBpmUserDto dto = new RemoteCallBpmUserDto();
        dto.setUid(bpmUser.getUid());
        dto.setUserName(bpmUser.getUserName());
        dto.setDisplayName(bpmUser.getDisplayName());
        dto.setPersonId(bpmUser.getPersonId());
        dto.setOrg(bpmUser.getOrg());

        return dto;
    }
}
