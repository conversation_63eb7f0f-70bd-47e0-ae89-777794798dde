package com.mi.oa.infra.mibpm.application.procinst.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.DeptPermissionResp;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DeptPermissionDo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/08/21 17:03
 */

@Mapper(componentModel = "spring")
public interface DeptPermissionReqConverter {
    List<DeptPermissionResp> doToDto(List<DeptPermissionDo> deptPermissionDo);
}
