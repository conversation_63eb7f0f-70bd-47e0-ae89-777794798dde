package com.mi.oa.infra.mibpm.application.procinst.impl;

import com.mi.oa.infra.mibpm.application.message.dto.req.ApplyPermissionReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenCreateProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.service.OpenProcessInstanceService;
import com.mi.oa.infra.mibpm.application.proinst.service.PermissionProInstService;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/22 19:37
 */
@Slf4j
@Service
public class PermissionProInstServiceImpl implements PermissionProInstService {

    @Value("${bpm.model.permission-apply:bpmn_1022521484160798720}")
    private String applyPermissionModelCode;
    @Autowired
    private OpenProcessInstanceService openProcessInstanceService;

    @Override
    public void startProcInstApplyPermisson(ApplyPermissionReq req) {
        String json = GsonUtils.toJsonWtihNullField(req);
        Map<String, Object> formDataMap = GsonUtils.fromJson(json, Map.class);
        OpenCreateProcInstReq openCreateProcInstReq = OpenCreateProcInstReq.builder()
                .modelCode(applyPermissionModelCode)
                .startUserId(IdentityUtil.currentUserName())
                .formData(formDataMap)
                .build();
        log.info("发起流程实例, request = {}", GsonUtils.toJsonWtihNullField(openCreateProcInstReq));
        openProcessInstanceService.startProcessInstance(openCreateProcInstReq);
    }
}
