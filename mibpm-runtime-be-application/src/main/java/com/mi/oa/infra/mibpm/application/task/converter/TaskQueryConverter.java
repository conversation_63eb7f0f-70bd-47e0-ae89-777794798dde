package com.mi.oa.infra.mibpm.application.task.converter;

import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskMonitorQueryReqDto;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskQueryPageReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.VoteTaskReq;
import com.mi.oa.infra.mibpm.common.model.HistoricTaskQueryDto;
import com.mi.oa.infra.mibpm.domain.task.model.TaskListQueryReq;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/22 5:44 PM
 **/
@Mapper(componentModel = "spring")
public interface TaskQueryConverter {
    TaskListQueryReq dtoToDo(TaskQueryPageReq req);

    @Mappings(value = {
            @Mapping(source = "userName", target = "startUserId")
    })
    HistoricTaskQueryDto dtoToHistoric(TaskQueryPageReq req);

    HistoricTaskQueryDto dtoToHistoricTaskQuery(TaskMonitorQueryReqDto req);

    VoteTaskReq convert(CompleteTaskReq req);

    default ZonedDateTime map(String s) {
        if (StringUtils.isNumeric(s)) {
            return ZonedDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(s) / 1000), ZoneId.systemDefault());
        }
        return null;
    }
}
