package com.mi.oa.infra.mibpm.application.message.event;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.AuthorizeRoleEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.PersonResignMessageEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.HistoricTaskQueryDto;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.procinst.remote.ProcessDefinitionRemoteService;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 人员离职事件监听器
 *
 * <AUTHOR>
 * @date 2022/3/24 11:50
 */
@Slf4j
@Service
public class PersonResignEventMessageListener implements EventSubscriber<PersonResignMessageEvent> {
    @Autowired
    private HistoricTaskRepository historicTaskRepository;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private ModelsAuthorityRemote modelsAuthorityRemote;
    @Autowired
    private ProcessDefinitionRemoteService processDefinitionRemoteService;

    @Override
    public String identifier() {
        return EventIdentify.PERSON_RESIGN_MESSAGE.name();
    }

    @Override
    public void process(PersonResignMessageEvent event) {
        log.info("消息模块消费人员离职事件，event = {}", event);

        BpmUser targetAssignee = event.getHandoverUser();
        BpmUser curAssignee = event.getUserName();

        // 查询近三个月离职人处理的流程
        int page = 1;
        int pageSize = 10;

        Set<String> historyModelCodes = new HashSet<>();

        HistoricTaskQueryDto historicTaskQuery = HistoricTaskQueryDto.builder()
                .currentUserId(curAssignee.getUid())
                .currentUserName(curAssignee.getUserName())
                .taskCreateTimeStart(ZonedDateTime.now().minusMonths(3L))
                .taskCreateTimeEnd(ZonedDateTime.now())
                .build();

        PageModel<TaskDo> taskDoPageModel = historicTaskRepository.queryHistoricTaskPage(historicTaskQuery, page++, pageSize);
        filterModelCodes(historyModelCodes, taskDoPageModel);
        for (; page <= taskDoPageModel.getPageTotal(); page++) {
            PageModel<TaskDo> pageModel = historicTaskRepository.queryHistoricTaskPage(historicTaskQuery, page, pageSize);
            filterModelCodes(historyModelCodes, pageModel);
        }

        if (CollectionUtils.isEmpty(historyModelCodes)) {
            return;
        }
        // 发送提示消息
        // 由事件构建消息实例
        LarkMessageDoBuilder builder = LarkMessageDo.builder();
        builder.templateColor(TemplateColor.YELLOW);
        buildContent(builder, historyModelCodes, targetAssignee);
        buildTitle(builder, curAssignee);
        builder.eventType(EventIdentify.REMOTE_CALL_ERROR);

        LarkMessageDo larkMessageDo = builder.build();

        // 查询消息发送对象
        PageModel<String> accounts = modelsAuthorityRemote.listAccountByPermissionPage(AuthorizeRoleEnum.OPERATIONS_ADMIN, 1, 10);

        if (CollectionUtils.isEmpty(accounts.getList())) {
            return;
        }
        // 发送消息
        accounts.getList().forEach((user) -> {
            larkMessageDo.setUsername(user);
            // 检查消息实例
            larkMessageDomainService.checkLarkMessage(larkMessageDo);
            larkMessageDomainService.sendMessageCard(larkMessageDo);
        });
    }

    private void filterModelCodes(Set<String> historyModelCodes, PageModel<TaskDo> pageModel) {
        for (TaskDo taskDo : pageModel.getList()) {
            if (processDefinitionRemoteService.listFirstNodeDef(taskDo.getProcessDefinitionId())
                    .contains(taskDo.getTaskDefinitionKey())) {
                continue;
            }
            ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(taskDo.getProcessInstanceId());
            historyModelCodes.add(String.format("%s(%s)", processInstanceDo.getProcessDefinitionName(), processInstanceDo.getModelCode()));
        }
    }

    /**
     * 构建流程标题
     *
     * @param builder
     */
    private void buildTitle(LarkMessageDoBuilder builder, BpmUser curAssignee) {
        String title = String.format("人员[%s(%s)]离职通知", curAssignee.getDisplayName(), curAssignee.getUserName());
        builder.title(new I18n(title, title, title));
    }

    /**
     * 构建业务数据区
     *
     * @return
     */
    private void buildContent(LarkMessageDoBuilder builder, Set<String> modelCodes, BpmUser targetUser) {

        String message = "**离职交接人:** " + targetUser.getUserName();
        message += "\n**最近三个月审批流程:** \n";

        for (String modelCode : modelCodes) {
            message += String.format("**%s** \n", modelCode);
        }
        builder.customizeContent(new I18n(message, message, message));
    }
}
