package com.mi.oa.infra.mibpm.application.todocenter.event.workbench;

import com.mi.oa.infra.mibpm.application.message.service.MessageService;
import com.mi.oa.infra.mibpm.application.todocenter.event.AbstractTodoCenterSyncListener;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.UserTaskDeletedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Timer;
import java.util.TimerTask;

/**
 * 用户任务已删除 同步待办中心
 *
 * 当用户任务从ru_task表中移除时触发
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/17 20:50
 */
@Slf4j
@Component
public class TaskDeletedTodoCenterListener extends AbstractTodoCenterSyncListener<UserTaskDeletedEvent> {

    @Autowired
    private MessageService messageService;

    @Override
    public String identifier() {
        return EventIdentify.TASK_DELETED.name();
    }

    @Override
    public void process(UserTaskDeletedEvent event) {
        log.info("待办中心任务同步模块消费任务删除事件，event = {}", event);
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    messageService.syncAppBadge(event.getAssignee());
                    todoCenterRemoteService.complete(event.getTaskId());
                } catch (Exception e) {
                    log.error("待办中心任务删除异常", e);
                }
            }
        }, 5000);
    }
}
