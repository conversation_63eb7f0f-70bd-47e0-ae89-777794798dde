package com.mi.oa.infra.mibpm.application.message.event;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.PersonResignMessageEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 人员离职发送消息给交接人
 *
 * <AUTHOR>
 * @date 2024/9/12 11:50
 */
@Slf4j
@Service
public class HandoverResignMessageListener extends AbstractHandoverMessageListener implements EventSubscriber<PersonResignMessageEvent> {
    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private ModelMetaRepository modelMetaRepository;
    @Autowired
    private AccountRemoteService accountRemoteService;

    @Override
    public String identifier() {
        return EventIdentify.PERSON_RESIGN_MESSAGE.name();
    }

    @Override
    public void process(PersonResignMessageEvent event) {

        BpmUser targetAssignee = event.getHandoverUser();
        BpmUser curAssignee = event.getUserName();
        log.info("人员{}离职，发送消息给交接人{}，event = {}", curAssignee.getUserName(), targetAssignee.getUserName(), event);

        List<ModelMeta> ownerProcessModels = modelMetaRepository.queryProcessesByOwner(curAssignee.getUserName());
        List<ModelMeta> businessOwnerProcessModels = modelMetaRepository.queryProcessesByBusinessOwner(curAssignee.getUserName());
        List<ModelMeta> checkerProcessModels = modelMetaRepository.queryProcessesByChecker(curAssignee.getUserName());

        List<String> ownerProcessModelCodes = queryModelCodesByModelMetas(ownerProcessModels);
        List<String> businessOwnerProcessModelCodes = queryModelCodesByModelMetas(businessOwnerProcessModels);
        List<String> checkerProcessModelCodes = queryModelCodesByModelMetas(checkerProcessModels);

        // 检查是否三种角色都没有流程
        if (CollectionUtils.isEmpty(ownerProcessModels) && CollectionUtils.isEmpty(businessOwnerProcessModels) &&
                CollectionUtils.isEmpty(checkerProcessModels)) {
            log.info("离职人员{}没有作为负责人、管理员、稽查员的流程，不发送消息", curAssignee.getUserName());
            return;
        }

        LarkMessageDoBuilder builder = LarkMessageDo.builder();
        builder.templateColor(TemplateColor.BLUE);

        Set<String> allModelCodes = new HashSet<>();
        handleModelUpdates(ownerProcessModels, businessOwnerProcessModels, checkerProcessModels, curAssignee, targetAssignee, allModelCodes);

        if (!CollectionUtils.isEmpty(ownerProcessModelCodes)) {
            ownerProcessModels = modelMetaRepository.listModelMeta(ownerProcessModelCodes);
        }
        if (!CollectionUtils.isEmpty(businessOwnerProcessModelCodes)) {
            businessOwnerProcessModels = modelMetaRepository.listModelMeta(businessOwnerProcessModelCodes);
        }
        if (!CollectionUtils.isEmpty(checkerProcessModelCodes)) {
            checkerProcessModels = modelMetaRepository.listModelMeta(checkerProcessModelCodes);
        }

        buildTitle(builder, allModelCodes, curAssignee);
        buildContent(builder, ownerProcessModels, businessOwnerProcessModels, checkerProcessModels);
        builder.eventType(EventIdentify.PERSON_RESIGN_MESSAGE);
        buildActions(builder);
        LarkMessageDo larkMessageDo = builder.build();
        larkMessageDo.setUsername(targetAssignee.getUserName());

        log.info("发送提醒消息给流程负责人: {}", targetAssignee);
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        larkMessageDomainService.sendHanoverMessageCard(larkMessageDo);

    }


    /**
     * 构建流程标题
     *
     * @param builder
     */
    private void buildTitle(LarkMessageDoBuilder builder, Set<String> modelCodes, BpmUser curAssignee) {
        String title = String.format("因您是[%s(%s)]的离职交接人，以下[%s]个流程的管理权已自动交接给您", curAssignee.getDisplayName(), curAssignee.getUserName(), modelCodes.size());
        builder.title(new I18n(title, title, title));
    }

    /**
     * 构建业务数据区
     *
     * @return
     */
    private void buildContent(LarkMessageDoBuilder builder, List<ModelMeta> ownerModelCodes, List<ModelMeta> businessOwnerModelCodes, List<ModelMeta> checkerModelCodes) {
        StringBuilder content = new StringBuilder();
        // 负责人部分
        if (businessOwnerModelCodes != null && !businessOwnerModelCodes.isEmpty()) {
            content.append("以下流程，您是[负责人]，负责流程规则的设计，主导流程落地运营。如需修改您的负责人身份，可联系流程管理员 \n");
            businessOwnerModelCodes.forEach(modelCode -> {
                List<String> ownerIds = modelCode.getOwners();

                if (ownerIds != null && !ownerIds.isEmpty()) {
                    // 只取第一个管理员
                    String firstOwnerId = ownerIds.get(0);
                    BpmUser ownerUser = accountRemoteService.getUser(firstOwnerId);
                    String ownerDetails = (ownerUser != null)
                            ? String.format("%s (%s)", ownerUser.getDisplayName(), ownerUser.getUserName())
                            : "[未知用户]";

                    content.append(String.format("%s (ID: %s)  管理员：%s \n",
                            modelCode.getName(),
                            modelCode.getModelCode(),
                            ownerDetails));
                } else {
                    content.append(String.format("%s (ID: %s)  管理员：null \n",
                            modelCode.getName(),
                            modelCode.getModelCode()));
                }
            });
        }

        // 管理员部分
        if (ownerModelCodes != null && !ownerModelCodes.isEmpty()) {
            content.append("\n以下流程，您是[管理员]，负责流程规则的后台配置，流程问题的运维。如需修改您的管理员身份，可进入后台自行修改 \n");
            ownerModelCodes.forEach(modelCode -> {
                content.append(String.format("%s (ID: %s) \n",
                        modelCode.getName(),
                        modelCode.getModelCode()));
            });
        }
        // 稽查员部分
        if (checkerModelCodes != null && !checkerModelCodes.isEmpty()) {

            content.append("\n以下流程，您是[稽查员]，可查看流程数据。如需修改稽查员身份，可联系流程管理员 \n");
            checkerModelCodes.forEach(modelCode -> {
                List<String> ownerIds = modelCode.getOwners();

                if (ownerIds != null && !ownerIds.isEmpty()) {
                    // 只取第一个管理员
                    String firstOwnerId = ownerIds.get(0);
                    BpmUser ownerUser = accountRemoteService.getUser(firstOwnerId);
                    String ownerDetails = (ownerUser != null)
                            ? String.format("%s (%s)", ownerUser.getDisplayName(), ownerUser.getUserName())
                            : "[未知用户]";

                    content.append(String.format("%s (ID: %s)  管理员：%s \n",
                            modelCode.getName(),
                            modelCode.getModelCode(),
                            ownerDetails));
                } else {
                    content.append(String.format("%s (ID: %s)  管理员：null \n",
                            modelCode.getName(),
                            modelCode.getModelCode()));
                }
            });
        }

        // 设置卡片内容
        builder.customizeContent(new I18n(content.toString(), content.toString(), content.toString()));
    }
}
