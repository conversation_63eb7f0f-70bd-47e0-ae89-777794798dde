package com.mi.oa.infra.mibpm.application.userconfig.impl;

import com.mi.oa.infra.mibpm.application.message.dto.req.ApplyPermissionReq;
import com.mi.oa.infra.mibpm.application.message.dto.req.DeletePermissionReq;
import com.mi.oa.infra.mibpm.application.procinst.converter.AssigneePermissionReqConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.DeptPermissionReqConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.PermissionRecordReqConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.PermissionReqConverter;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.AssigneePermissionResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.DeptPermissionResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.PermissionRecordsDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RequirePermissionDto;
import com.mi.oa.infra.mibpm.application.proinst.service.PermissionProInstService;
import com.mi.oa.infra.mibpm.application.userconfig.errorcode.ApplyPermissionErrorCodeEnum;
import com.mi.oa.infra.mibpm.application.userconfig.errorcode.PermissionManageErrorCodeEnum;
import com.mi.oa.infra.mibpm.application.userconfig.service.UserDashboardPermissionService;
import com.mi.oa.infra.mibpm.common.enums.DeptFilter;
import com.mi.oa.infra.mibpm.common.enums.PermissionIdType;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionDo;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionRecordsDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.PermissionOperationRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.utils.ExternalThreadPool;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.ListVO;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.organization.rep.OrgVO;
import com.mi.oa.infra.organization.service.OrgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/15 16:47
 */

@Slf4j
@Service
public class UserPermissionServiceImpl implements UserDashboardPermissionService {
    @Autowired
    PermissionProInstService permissionProInstService;
    @Autowired
    ModelsAuthorityRemote modelsAuthorityRemote;
    @Autowired
    PermissionOperationRepository permissionOperationRepository;
    @Autowired
    PermissionReqConverter permissionReqConverter;
    @Autowired
    PermissionRecordReqConverter permissionRecordReqConverter;
    @Autowired
    OrgService orgService;
    @Autowired
    DeptPermissionReqConverter deptPermissionReqConverter;
    @Autowired
    AssigneePermissionReqConverter assigneePermissionReqConverter;

    public void initUserPermission() {
        String orgCode = "MI";
        BaseResp<PageVO<OrgVO>> descendant = orgService.getDescendant("MIGROUP", orgCode, 1, 1, 50);
        if (descendant != null && descendant.getData() != null) {
            List<String> orgCodes = descendant.getData().getList().stream()
                    .map(OrgVO::getOrgCode)
                    .collect(Collectors.toList());
            processOrgCodes("MIGROUP", orgCodes, DeptFilter.ONEDEPT);
            for (String code : orgCodes) {
                BaseResp<PageVO<OrgVO>> level2Descendant = orgService.getDescendant("MIGROUP", code, 1, 1, 50);
                if (level2Descendant != null && level2Descendant.getData() != null) {
                    List<OrgVO> level2List = level2Descendant.getData().getList();
                    if (level2List != null) {
                        List<String> level2OrgCodes = level2List.stream()
                                .map(OrgVO::getOrgCode)
                                .collect(Collectors.toList());
                        processOrgCodes("MIGROUP", level2OrgCodes, DeptFilter.TWODEPT);
                    } else {
                        log.info("获取 level2Descendant 列表失败：列表为空");
                    }
                } else {
                    log.info("获取 level2Descendant 失败：" + (level2Descendant != null ? level2Descendant.getMessage() : "未知错误"));
                }
            }
        } else {
            log.info("获取 descendant 失败：" + (descendant != null ? descendant.getMessage() : "未知错误"));
        }
    }

    private void processOrgCodes(String groupCode, List<String> orgCodes, DeptFilter deptFilter) {
        if (orgCodes.isEmpty()) {
            return;
        }
        String orgCodesStr = String.join(",", orgCodes);
        BaseResp<ListVO<OrgVO>> batchGetListResp = orgService.batchGetList(groupCode, orgCodesStr, 1, true, true, true);
        if (batchGetListResp != null && batchGetListResp.getData() != null) {
            List<OrgVO> orgList = batchGetListResp.getData().getList();
            for (OrgVO org : orgList) {
                if (org.getOrgOwner() != null && !org.getOrgOwner().isEmpty()) {
                    Boolean hasPermission = permissionOperationRepository.isDeptPermission(org.getOrgOwner(), org.getOrgCode());
                    if (!hasPermission) {
                        permissionOperationRepository.addUserInitDeptId(org.getOrgOwner(), org.getOrgCode(), deptFilter);
                    } else {
                        log.info(org.getOrgOwner() + "已拥有部门权限：" + org.getOrgCode());
                    }
                }
            }
        } else {
            log.info("获取批次列表失败：" + (batchGetListResp != null ? batchGetListResp.getMessage() : "未知错误"));
        }
    }

    @Override
    public PageVO<RequirePermissionDto> queryPermissionPage(long pageNum, long pageSize, String deptId) {
        String userOrpid = IdentityUtil.currentUserName();
        PageModel<PermissionDo> page = permissionOperationRepository.page(userOrpid, pageNum,
                pageSize, deptId);
        List<RequirePermissionDto> dtoList = permissionReqConverter.doToDto(
                page.getList());
        return PageVO.build(dtoList, page.getPageSize(), page.getPageNum(), page.getTotal());
    }

    @Override
    public PageVO<PermissionRecordsDto> queryPermissionRecordsPage(long pageNum, long pageSize, String deptId) {
        String userOrpid = IdentityUtil.currentUserName();
        PageModel<PermissionRecordsDo> records = permissionOperationRepository.exportRecords(userOrpid, pageNum,
                pageSize, deptId);
        List<PermissionRecordsDto> dtoList = permissionRecordReqConverter.doToDto(
                records.getList());
        return PageVO.build(dtoList, records.getPageSize(), records.getPageNum(), records.getTotal());
    }

    @Override
    public void applyPermission(ApplyPermissionReq applyPermissionReq) {
        boolean isPermissionValid;
        if (applyPermissionReq.getType() == PermissionIdType.USER_TYPE) {
            String userPermissionId = applyPermissionReq.getUserPermissionId();
            if (isUserPermission(applyPermissionReq.getUserOrpid(), userPermissionId)) {
                throw new DomainException(ApplyPermissionErrorCodeEnum.PERMISSION_ALREADY_APPLIED, userPermissionId);
            }
            isPermissionValid = checkUserPermission(applyPermissionReq.getUserOrpid(), userPermissionId);
            if (!isPermissionValid) {
                throw new DomainException(ApplyPermissionErrorCodeEnum.PERMISSION_DENIED, userPermissionId);
            }
        } else if (applyPermissionReq.getType() == PermissionIdType.DEPT_TYPE) {
            List<String> deptPermissionIds = applyPermissionReq.getDeptPermissionId();
            for (String deptPermissionId : deptPermissionIds) {
                if (isUserPermission(applyPermissionReq.getUserOrpid(), deptPermissionId)) {
                    throw new DomainException(ApplyPermissionErrorCodeEnum.PERMISSION_ALREADY_APPLIED, deptPermissionId);
                }
                isPermissionValid = checkDeptPermission(applyPermissionReq.getUserOrpid(), deptPermissionId);
                if (!isPermissionValid) {
                    throw new DomainException(ApplyPermissionErrorCodeEnum.PERMISSION_DENIED, deptPermissionId);
                }
            }
        }
        permissionProInstService.startProcInstApplyPermisson(applyPermissionReq);
    }

    @Override
    public void removePermission(DeletePermissionReq deletePermissionReq) {
        permissionOperationRepository.deletePermission(deletePermissionReq);
        permissionOperationRepository.addRemoveRecord(deletePermissionReq);
    }

    @Override
    public Boolean isUserPermission(String userOrpid, String permissionId) {
        PermissionDo permissionDo = permissionOperationRepository.queryPermission(userOrpid, permissionId);
        if (permissionDo == null) {
            return false;
        }
        return true;
    }

    private boolean checkUserPermission(String userOrpid, String permissionId) {
        return !StringUtils.equals(userOrpid, permissionId);
    }

    private boolean checkDeptPermission(String userOrpid, String deptId) {
        return !StringUtils.equals(userOrpid, deptId);
    }

    @Override
    public List<DeptPermissionResp> queryDeptId(String userOrpid) {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            return deptPermissionReqConverter.doToDto(permissionOperationRepository.queryDeptPermission(userOrpid));
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
    }

    @Override
    public List<AssigneePermissionResp> queryAssigneeId(String userOrpid) {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            return assigneePermissionReqConverter.doToDto(permissionOperationRepository.queryAssigneePermission(userOrpid));
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
    }

    @Override
    public void removeDeptId(String userOrpid, String deptId) {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            permissionOperationRepository.removeDeptId(userOrpid, deptId);
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
    }

    @Override
    public void removeAssigneeId(String userOrpid, String permissionUserId) {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            permissionOperationRepository.removeAssigneeId(userOrpid, permissionUserId);
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
    }

    @Override
    public void removeDashboard(String userOrpid, String permissionUserId) {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            permissionOperationRepository.removeDashboard(userOrpid, permissionUserId);
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
    }

    @Override
    public void addDeptId(String userOrpid, String deptId) {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            if (permissionOperationRepository.isDeptPermission(userOrpid, deptId)) {
                throw new DomainException(PermissionManageErrorCodeEnum.ALREADY_HAS_PERMISSION);
            } else {
                permissionOperationRepository.addDeptId(userOrpid, deptId);
            }
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
    }

    @Override
    public void addAssigneeId(String userOrpid, String permissionUserId) {
        if (modelsAuthorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            if (permissionOperationRepository.isAssigneePermission(userOrpid, permissionUserId)) {
                throw new DomainException(PermissionManageErrorCodeEnum.ALREADY_HAS_PERMISSION);
            } else {
                permissionOperationRepository.addAssigneeId(userOrpid, permissionUserId);
            }
        } else {
            throw new DomainException(PermissionManageErrorCodeEnum.NOT_ADMIN);
        }
    }

    @Override
    public void initUserPermission(String userId) {
        ExternalThreadPool.getInstance().execute(() -> {
            try {
                permissionOperationRepository.insertIfNotExist(userId, userId);
            } catch (Exception e) {
                log.error("init user permission error {}", userId, e);
            }
        });
    }
}
