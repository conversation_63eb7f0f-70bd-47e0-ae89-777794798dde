package com.mi.oa.infra.mibpm.application.procinst.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.AssigneePermissionResp;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserPermissionDo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/08/21 17:03
 */

@Mapper(componentModel = "spring")
public interface AssigneePermissionReqConverter {
    List<AssigneePermissionResp> doToDto(List<UserPermissionDo> userPermissionDo);
}
