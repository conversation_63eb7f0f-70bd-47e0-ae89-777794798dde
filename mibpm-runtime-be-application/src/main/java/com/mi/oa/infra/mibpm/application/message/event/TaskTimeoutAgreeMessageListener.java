package com.mi.oa.infra.mibpm.application.message.event;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.constant.LarkMessageConstants;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.TaskTimeoutEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/13 7:56 PM
 **/
@Slf4j
@Component
public class TaskTimeoutAgreeMessageListener extends AbstractMessageListener<TaskTimeoutEvent> {

    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;

    @Override
    public String identifier() {
        return EventIdentify.TIMEOUT_AGREE.name();
    }

    @Override
    public void process(TaskTimeoutEvent event) {
        log.info("消息模块消费任务超时通过事件，event = {}", event);
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = this.buildLarkMessage(event);
        // 检查消息实例
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        // 发送消息
        larkMessageDomainService.sendMessageCard(larkMessageDo, null);
    }

    /**
     * 构建消息实例对象
     *
     * @param event
     * @return
     */
    public LarkMessageDo buildLarkMessage(TaskTimeoutEvent event) {
        LarkMessageDo.LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 消息接收人
        builder.username(event.getTargetUser());
        builder.templateColor(TemplateColor.BLUE);
        // 获取流程实例
        ProcessInstanceDo instance = historicProcInstRepository.queryHistoricProcInst(event.getProcessInstanceId());
        BpmUser startUser = instance.getStartUser();
        if (Objects.isNull(startUser)) {
            return null;
        }
        buildTitle(builder);

        Content content = buildContent(startUser);
        builder.content(content);
        // 构建流程信息
        buildInstanceInfo(event.getTaskId(), builder, instance, false);
        // 构建按钮
        buildActions(builder);
        builder.eventType(EventIdentify.TIMEOUT_AGREE);
        LarkMessageDo messageDo = builder.build();
        Map<String, String> summary = parseSummary(event.getTaskId());
        content.setSummaries(Arrays.asList(summary));
        return messageDo;
    }

    /**
     * 构建流程标题
     *
     * @param builder
     */
    private void buildTitle(LarkMessageDo.LarkMessageDoBuilder builder) {
        // 构建title
        String title = "审批任务已过期，被系统自动通过";
        String enTitle = "The task has bean approved by system automatically because of timeout";
        builder.title(new I18n(title, enTitle, enTitle));
    }


    /**
     * 构建业务数据区
     *
     * @param user
     * @return
     */
    private Content buildContent(BpmUser user) {

        // 构建content
        return Content.builder()
                .isApprove(false)
                .username(String.format("%s(%s)", user.getDisplayName(), user.getUserName()))
                .deptInfo(String.join("-", buildDeptInfo(user)))
                .build();
    }

    /**
     * 构建按钮区
     *
     * @param builder
     */
    private void buildActions(LarkMessageDo.LarkMessageDoBuilder builder) {

        List<Actions> actions = new ArrayList<>();
        // 填充审批按钮信息

        //设置查看详情按钮
        Actions detail = new Actions();
        detail.setActionKey(LarkMessageConstants.DETAIL);
        actions.add(detail);
        builder.actions(actions);
    }
}
