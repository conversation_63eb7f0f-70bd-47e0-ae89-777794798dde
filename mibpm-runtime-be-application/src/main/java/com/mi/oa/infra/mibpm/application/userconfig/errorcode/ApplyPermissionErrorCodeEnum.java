package com.mi.oa.infra.mibpm.application.userconfig.errorcode;

import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;

/**
 * <AUTHOR>
 * @Date 2024/7/19 16:18
 */
public enum ApplyPermissionErrorCodeEnum implements DomainErrorCode {
    PERMISSION_DENIED(1, "权限申请失败，此%s权限申请不通过"),
    PERMISSION_ALREADY_APPLIED(2, "权限申请失败，此%s权限已存在");
    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    ApplyPermissionErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }
}
