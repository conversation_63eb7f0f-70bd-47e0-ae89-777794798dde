package com.mi.oa.infra.mibpm.application.operation.listener;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateStartSubmitEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 审批任务提交监听器
 *
 * <AUTHOR>
 * @date 2023/5/4 19:51
 */
@Slf4j
@Component
public class OperateStartSubmitHistoryListener extends AbstractOperationListener<OperateStartSubmitEvent> {

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_START_SUBMIT.name();
    }
}
