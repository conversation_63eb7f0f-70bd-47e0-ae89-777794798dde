package com.mi.oa.infra.mibpm.application.userconfig.converter;

import com.mi.oa.infra.mibpm.application.message.dto.req.ApplyPermissionReq;
import com.mi.oa.infra.mibpm.application.message.dto.req.PermissionCallBackReq;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DeptPermissionDo;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2024/7/19 10:48
 */
@Mapper(componentModel = "spring")
public interface ApplyDeptPermissionReqConverter {
    DeptPermissionDo dtoToDo(PermissionCallBackReq permissionCallBackReq);

}
