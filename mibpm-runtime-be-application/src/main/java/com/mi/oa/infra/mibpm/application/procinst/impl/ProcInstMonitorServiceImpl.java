package com.mi.oa.infra.mibpm.application.procinst.impl;

import com.mi.oa.infra.mibpm.application.procinst.converter.ProcInstVariablesConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.ProcessInstanceQueryReqConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.ProcessInstanceRepsConverter;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.HistoricProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcInsVariablesMonitorResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcessInstanceMonitorResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.AdminTerminateProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcInstMonitorQueryReqDto;
import com.mi.oa.infra.mibpm.application.proinst.service.ProcInstMonitorService;
import com.mi.oa.infra.mibpm.application.task.converter.TaskInstConverter;
import com.mi.oa.infra.mibpm.application.task.converter.TaskQueryConverter;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskMonitorQueryReqDto;
import com.mi.oa.infra.mibpm.application.task.dto.req.UpdateAssigneeListReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.UpdateVariablesReq;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskInstanceResp;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.AuthorizeDimension;
import com.mi.oa.infra.mibpm.common.enums.AuthorizeRoleEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.AssigneeChangeEvent;
import com.mi.oa.infra.mibpm.common.event.OperateTerminatedEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.HistoricTaskQueryDto;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceQueryDto;
import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.common.model.UserTaskActivity;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.service.TaskDomainService;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.TaskRepository;
import com.mi.oa.infra.mibpm.sdk.dto.ModelDto;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.AccountAuthorityResp;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: qiuzhipeng
 * @Date: 2022/5/24 14:47
 */
@Service
@Slf4j
public class ProcInstMonitorServiceImpl implements ProcInstMonitorService {

    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    private HistoricTaskRepository historicTaskRepository;
    @Autowired
    private TaskRepository taskRepository;
    @Autowired
    private ProcessInstanceQueryReqConverter processInstanceQueryReqConverter;
    @Autowired
    private ProcessInstanceRepsConverter processInstanceRepsConverter;
    @Autowired
    private TaskInstConverter taskInstConverter;
    @Autowired
    private TaskQueryConverter taskQueryConverter;
    @Autowired
    private ProcInstVariablesConverter procInstVariablesConverter;
    @Autowired
    private ModelsAuthorityRemote authorityRemote;
    @Autowired
    private ModelRemoteService modelRemoteService;
    @Autowired
    private TaskDomainService taskDomainService;
    @Autowired
    private ProcessInstanceDomainService processInstanceDomainService;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private EventPublisher eventPublisher;

    @Override
    public PageModel<HistoricProcInstResp> queryProcessInstanceListPage(ProcInstMonitorQueryReqDto procInstMonitorQueryReqDto,
                                                                        long pageNum, long pageSize) {
        ProcessInstanceQueryDto req = processInstanceQueryReqConverter.dtoToDo(procInstMonitorQueryReqDto);

        if (CollectionUtils.isNotEmpty(procInstMonitorQueryReqDto.getStartUser())) {
            List<BpmUser> bpmUsers = accountRemoteService.listUsers(procInstMonitorQueryReqDto.getStartUser());
            List<String> users = new ArrayList<>();
            for (BpmUser bpmUser : bpmUsers) {
                users.add(bpmUser.getUid());
                users.add(bpmUser.getUserName());
            }
            req.setStartUserIds(users);
        }
        if (StringUtils.isNotBlank(req.getCurrentUserName())) {
            BpmUser user = accountRemoteService.getUser(req.getCurrentUserName());
            if (Objects.isNull(user)) {
                return new PageModel<>(Collections.emptyList(), 0, 0, 0, 0);
            }
            req.setCurrentUserId(user.getUid());
        }
        // 数据过滤
        String userName = IdentityUtil.currentUserName();
        AccountAuthorityResp accountAuth = authorityRemote.queryAccountAuth(userName);
        boolean superAdmin = isSuperAdmin(accountAuth);
        boolean superChecker = isSuperChecker(accountAuth);
        if (!superAdmin && !superChecker) {
            List<String> modelCodes = listUserAuthModelCodes(accountAuth);
            if (CollectionUtils.isEmpty(modelCodes)) {
                return PageModel.build(Collections.emptyList(), pageSize, pageNum, 0);
            }
            if (StringUtils.isNotBlank(req.getModelCode())) {
                if (!modelCodes.contains(req.getModelCode())) {
                    return PageModel.build(Collections.emptyList(), pageSize, pageNum, 0);
                }
            } else {
                req.setInModelCodes(modelCodes);
            }
        }
        req.setUnlimitedTime(true);
        PageModel<ProcessInstanceDo> pageModel = historicProcInstRepository.queryHistoricProcInstPage(req, pageNum,
                pageSize);
        // 填充流程发起人
        pageModel.getList().forEach(instanceDo -> processInstanceDomainService.loadProcessStartUser(instanceDo));
        return PageModel.build(processInstanceRepsConverter.doListToDtoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public ProcessInstanceMonitorResp queryHistoricProcInst(String processInstanceId) {
        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(processInstanceId);
        processInstanceDomainService.loadProcessStartUser(processInstanceDo);
        ProcessInstanceMonitorResp procInstMonitorResp = processInstanceRepsConverter.toProcessInstanceMonitorResp(
                processInstanceDo);
        if (null == procInstMonitorResp) {
            return null;
        }
        List<TaskDo> taskDos = historicTaskRepository.queryHistoricTasksByProcInstId(processInstanceId);
        String userName = IdentityUtil.currentUserName();
        AccountAuthorityResp accountAuthorityResp = authorityRemote.queryAccountAuth(userName);
        processInstanceDomainService.checkProcessInstanceMonitorPermission(processInstanceDo, accountAuthorityResp);
        procInstMonitorResp.setHistoricTasks(taskInstConverter.doToDto(taskDos));
        Map<String, TaskDo> taskDoMap = taskDos.stream().collect(Collectors.toMap(TaskDo::getTaskId, Function.identity()));
        ModelDto modelDto = modelRemoteService.queryByCode(processInstanceDo.getModelCode());
        procInstMonitorResp.getHistoricTasks().forEach(t -> {
            TaskDo taskDo = taskDoMap.get(t.getTaskId());
            TaskLink taskLink = taskDomainService.getTaskDetailLink(taskDo);
            if (!taskLink.getFormType().isBpmFormType()) {
                t.setExtTask(1);
                t.setPcLink(taskLink.getPcLink());
                t.setMobileLink(taskLink.getMobileLink());
            }
        });
        procInstMonitorResp.setAppCode(modelDto.getAppCode());
        procInstMonitorResp.setFromOld(modelDto.getFromOld());
        procInstMonitorResp.setCategoryCode(modelDto.getCategoryCode());
        procInstMonitorResp.setIsChecker(
                isChecker(accountAuthorityResp, procInstMonitorResp.getModelCode(), procInstMonitorResp.getCategoryCode()));
        return procInstMonitorResp;
    }

    @Override
    public ProcessInstanceMonitorResp queryRuntimeProcInstAndExecutions(String processInstanceId) {
        ProcessInstanceDo processInstanceDo = processInstanceDomainService.queryProcessInstance(processInstanceId);
        ProcessInstanceMonitorResp procInstMonitorResp = processInstanceRepsConverter.toProcessInstanceMonitorResp(
                processInstanceDo);
        if (null == procInstMonitorResp) {
            return null;
        }
        List<UserTaskActivity> taskActivities = taskRepository.queryRuntimeActivities(processInstanceId);
        List<TaskInstanceResp> activities = taskActivities.stream().map(i -> {
            TaskInstanceResp task = new TaskInstanceResp();
            task.setTaskName(i.getName());
            task.setTaskDefinitionKey(i.getId());
            return task;
        }).collect(Collectors.toList());
        procInstMonitorResp.setHistoricTasks(activities);
        return procInstMonitorResp;
    }

    @Override
    public PageModel<TaskInstanceResp> queryTaskListPage(TaskMonitorQueryReqDto taskMonitorQueryReqDto, long pageNum, long pageSize) {
        HistoricTaskQueryDto queryEntity = taskQueryConverter.dtoToHistoricTaskQuery(taskMonitorQueryReqDto);
        if (StringUtils.isNotBlank(taskMonitorQueryReqDto.getCurrentUserName())) {
            BpmUser user = accountRemoteService.getUser(taskMonitorQueryReqDto.getCurrentUserName());
            if (Objects.nonNull(user)) {
                queryEntity.setCurrentUserId(user.getUid());
            }
        }
        // 权限过滤
        String userName = IdentityUtil.currentUserName();
        AccountAuthorityResp accountAuth = authorityRemote.queryAccountAuth(userName);
        boolean superAdmin = isSuperAdmin(accountAuth);
        boolean superChecker = isSuperChecker(accountAuth);
        if (!superAdmin && !superChecker) {
            List<String> modelCodes = listUserAuthModelCodes(accountAuth);
            if (CollectionUtils.isEmpty(modelCodes)) {
                return PageModel.build(Collections.emptyList(), pageSize, pageNum, 0);
            }
            if (StringUtils.isNotBlank(queryEntity.getModelCode())) {
                if (!modelCodes.contains(queryEntity.getModelCode())) {
                    return PageModel.build(Collections.emptyList(), pageSize, pageNum, 0);
                }
            } else {
                queryEntity.setModelCodeIn(modelCodes);
            }
        }
        PageModel<TaskDo> taskDoPageModel = historicTaskRepository.queryHistoricTaskPage(queryEntity, pageNum,
                pageSize);
        List<TaskInstanceResp> taskInstanceResps = taskInstConverter.doToDto(taskDoPageModel.getList());
        for (TaskInstanceResp taskInstanceResp : taskInstanceResps) {
            String processDefinitionId = taskInstanceResp.getProcessDefinitionId();
            String modelCode = processDefinitionId.split(":")[0];
            taskInstanceResp.setModelCode(modelCode);
            ModelDto modelDto = modelRemoteService.queryByCode(modelCode);
            taskInstanceResp.setCategoryCode(modelDto.getCategoryCode());
            taskInstanceResp.setIsChecker(
                    isChecker(accountAuth, taskInstanceResp.getModelCode(), taskInstanceResp.getCategoryCode()));
        }
        return PageModel.build(taskInstanceResps, taskDoPageModel.getPageSize(),
                taskDoPageModel.getPageNum(), taskDoPageModel.getTotal());
    }

    @Override
    public List<ProcInsVariablesMonitorResp> queryInstanceVariables(String processInstanceId) {
        ProcessInstanceDo processInstanceDo = ProcessInstanceDo.builder()
                .processInstanceId(processInstanceId).build();
        historicProcInstRepository.loadProcessVariables(processInstanceDo);
        return procInstVariablesConverter.toProcInsVariablesMonitorResp(processInstanceDo);
    }

    @Override
    public void updateInstanceVariables(UpdateVariablesReq updateVariablesReq) {
        log.info("流程监控修改流程变量, req={}, user={}", GsonUtils.toJsonWtihNullField(updateVariablesReq), IdentityUtil.currentUserName());
        Map<String, Object> variables = new HashMap<>(6);
        variables.put(updateVariablesReq.getVariableName(), updateVariablesReq.getValue());

        ProcessInstanceDo processInstanceDo = ProcessInstanceDo.builder()
                .processInstanceId(updateVariablesReq.getProcessInstanceId())
                .processVariables(variables).build();
        processInstanceDomainService.setProcessVariables(processInstanceDo);
    }

    @Override
    public void setAssignees(UpdateAssigneeListReq updateAssigneeListReq) {
        log.info("流程监控修改审批人, req={}, user={}", GsonUtils.toJsonWtihNullField(updateAssigneeListReq),
                IdentityUtil.currentUserName());
        BpmUser operator = IdentityUtil.currentUser();
        List<TaskDo> taskDos = new ArrayList<>();
        Map<String, BpmUser> oldAssignees = new HashMap<>();
        updateAssigneeListReq.getUpdateAssigneeListReq().forEach(req -> {
            TaskDo taskDo = taskDomainService.queryTask(req.getTaskId());
            oldAssignees.put(taskDo.getTaskId(), taskDo.getAssignee());

            if (Objects.nonNull(taskDo) && !req.getUserId().equals(taskDo.getAssignee().getUserName())
                    && !req.getUserId().equals(taskDo.getAssignee().getUid())) {
                // 填充新的处理人
                taskDomainService.fillTaskAssignee(taskDo, req.getUserId());
                taskDomainService.fillTaskOperator(taskDo, operator);
                // 检查修改任务权限
                taskDomainService.checkSetAssigneeTaskPermission(taskDo);
                taskDos.add(taskDo);
                publishChangeEvent(taskDo, operator, taskDo.getAssignee(), req.getComment(), oldAssignees.get(taskDo.getTaskId()));
            }
        });
        // 修改处理人
        taskRepository.setAssignees(taskDos);
    }

    private void publishChangeEvent(TaskDo task, BpmUser operator, BpmUser targetUser, String message, BpmUser oldAssignee) {
        try {
            String comment = "管理员转审";
            String commentEn = "Administrator transfer approver";
            if (StringUtils.isNotBlank(message)) {
                comment += ", 操作理由: " + message;
                commentEn += ", operation reason: " + message;
            }
            AssigneeChangeEvent operateTransferredEvent = AssigneeChangeEvent.builder()
                    .taskId(task.getTaskId())
                    .taskName(task.getTaskName())
                    .taskDefinitionKey(task.getTaskDefinitionKey())
                    .processInstanceId(task.getProcessInstanceId())
                    .processDefinitionId(task.getProcessDefinitionId())
                    .assignee(oldAssignee)
                    .operator(operator)
                    .comment(comment)
                    .commentEn(commentEn)
                    .transferTo(targetUser)
                    .build();
            // 设置事件基础信息
            operateTransferredEvent.setId(task.getTaskName());
            operateTransferredEvent.setIdentifier(EventIdentify.ASSIGNEE_CHANGE.name());
            operateTransferredEvent.setTimestamp(System.currentTimeMillis());
            // 发送审批任务被转审事件
            log.info("修改审批人，发送审批人修改事件，event = {}", operateTransferredEvent);
            eventPublisher.publish(operateTransferredEvent);
        } catch (Exception e) {
            log.error("发送人员变更事件异常", e);
        }
    }

    @Override
    public void terminateProcessInstance(AdminTerminateProcInstReq terminateProcInstReq) {
        log.info("流程监控终止流程, req={}, user={}", GsonUtils.toJsonWtihNullField(terminateProcInstReq),
                IdentityUtil.currentUserName());
        // 查询流程实例（检查是否存在）
        ProcessInstanceDo processInstanceDoDb = processInstanceDomainService
                .queryProcessInstance(terminateProcInstReq.getInstanceId());
        // 填充流程实例的操作人
        processInstanceDomainService.fillProcessInstanceOperator(processInstanceDoDb,
                terminateProcInstReq.getOperator());
        // 查询流程实例下的任务列表
        List<TaskDo> taskDos = taskDomainService.queryTaskList(processInstanceDoDb.getProcessInstanceId());
        // 检查流程实例能否终止
        processInstanceDomainService.checkTerminateProcessInstance(processInstanceDoDb, taskDos);
        // 指定流程实例加载变量
        processInstanceDomainService.loadProcessVariables(processInstanceDoDb);
        // 设置审批操作
        processInstanceDoDb.getProcessVariables().put(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS,
                UserTaskOperation.TERMINATE.getCode());
        // 设置流程变量
        processInstanceDomainService.setProcessVariables(processInstanceDoDb);
        // 批量终止任务
        taskDomainService.terminateTasks(taskDos, terminateProcInstReq.getComment(), null);
        // 终止流程实例
        processInstanceDomainService.terminateProcessInstance(processInstanceDoDb);
        // 构建事件
        OperateTerminatedEvent operateTerminatedEvent = OperateTerminatedEvent.builder()
                .processInstanceId(processInstanceDoDb.getProcessInstanceId())
                .processDefinitionId(processInstanceDoDb.getProcessDefinitionId())
                .operator(IdentityUtil.currentUser())
                .comment(terminateProcInstReq.getComment())
                .build();
        // 设置事件基础信息
        operateTerminatedEvent.setId(processInstanceDoDb.getProcessInstanceId());
        operateTerminatedEvent.setIdentifier(EventIdentify.OPERATE_TERMINATED.name());
        operateTerminatedEvent.setTimestamp(System.currentTimeMillis());
        //发送流程终止事件
        eventPublisher.publish(operateTerminatedEvent);

    }

    private static final String SEPARATOR = ":";

    public List<String> queryDataResource(AccountAuthorityResp accountAuthorityResp, AuthorizeDimension authorizeDimension) {
        if (null == accountAuthorityResp) {
            return Collections.emptyList();
        }
        Map<String, List<String>> dimensionResourceCodeMap = accountAuthorityResp.getDimensionResourceCodeMap();
        if (MapUtils.isEmpty(dimensionResourceCodeMap)) {
            return Collections.emptyList();
        }
        List<String> resourceCodes = dimensionResourceCodeMap.getOrDefault(authorizeDimension.getCode(),
                new ArrayList<>());
        List<String> authList = resourceCodes.stream().map(item -> item.split(SEPARATOR)[1])
                .collect(Collectors.toList());
        List<String> dataSource = new ArrayList<>(authList);
        return dataSource;
    }

    private List<String> listUserAuthModelCodes(AccountAuthorityResp accountAuthorityResp) {
        List<String> ownerModelCodes = queryDataResource(accountAuthorityResp, AuthorizeDimension.BPMN_OWNER);
        List<String> checkerModelCodes = queryDataResource(accountAuthorityResp, AuthorizeDimension.BPMN_CHECKER);
        List<String> categoryCodes = queryDataResource(accountAuthorityResp, AuthorizeDimension.CATE_CHECKER);
        List<ModelDto> modelDtos = modelRemoteService.queryModelsByCategory(categoryCodes);
        Set<String> collect = modelDtos.stream().map(ModelDto::getModelCode).collect(Collectors.toSet());
        HashSet<String> codeSet = new HashSet<>(ownerModelCodes);
        codeSet.addAll(checkerModelCodes);
        codeSet.addAll(collect);
        return new ArrayList<>(codeSet);
    }

    private boolean isChecker(AccountAuthorityResp accountAuthorityResp, String modelCode, String categoryCode) {
        if (isSuperAdmin(accountAuthorityResp)) {
            return false;
        }
        List<String> ownerModelCodes = queryDataResource(accountAuthorityResp, AuthorizeDimension.BPMN_OWNER);
        if (ownerModelCodes.contains(modelCode)) {
            return false;
        }
        if (isSuperChecker(accountAuthorityResp)) {
            return true;
        }
        List<String> categoryCodes = queryDataResource(accountAuthorityResp, AuthorizeDimension.CATE_CHECKER);
        if (categoryCodes.contains(categoryCode)) {
            return true;
        }
        List<String> checkerModelCodes = queryDataResource(accountAuthorityResp, AuthorizeDimension.BPMN_CHECKER);
        if (checkerModelCodes.contains(modelCode)) {
            return true;
        }
        return false;
    }

    private boolean isSuperAdmin(AccountAuthorityResp accountAuthorityResp) {
        if (null != accountAuthorityResp) {
            // 获取账号授权信息
            // 获取含有的角色列表
            List<String> roleCodeList = accountAuthorityResp.getRoleCodeList();
            if (CollectionUtils.isNotEmpty(roleCodeList)) {
                return roleCodeList.contains(AuthorizeRoleEnum.SUPER_ADMIN.getCode());
            }
        }
        return false;
    }

    private boolean isSuperChecker(AccountAuthorityResp accountAuthorityResp) {
        if (null != accountAuthorityResp) {
            // 获取账号授权信息
            // 获取含有的角色列表
            List<String> roleCodeList = accountAuthorityResp.getRoleCodeList();
            if (CollectionUtils.isNotEmpty(roleCodeList)) {
                return roleCodeList.contains(AuthorizeRoleEnum.CHECKER.getCode());
            }
        }
        return false;
    }

}
