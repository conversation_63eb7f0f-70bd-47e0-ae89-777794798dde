package com.mi.oa.infra.mibpm.application.lark.converter;

import com.larksuite.appframework.sdk.core.protocol.client.doc.reps.NewDocMetaResponse;
import com.mi.oa.infra.mibpm.application.lark.dto.resp.DocMetaReps;
import org.mapstruct.Mapper;

/**
 * @author: qiuzhipeng
 * @Date: 2022/6/23 14:37
 */
@Mapper(componentModel = "spring")
public interface DocConverter {

    default DocMetaReps toDocMetaReps(NewDocMetaResponse docMetaResponse) {
        if (docMetaResponse == null) {
            return null;
        }

        DocMetaReps docMetaReps = new DocMetaReps();

        docMetaReps.setData(dataToData(docMetaResponse.getData()));

        return docMetaReps;
    }

    default DocMetaReps.Data dataToData(com.larksuite.appframework.sdk.core.protocol.client.doc.reps.NewDocMetaResponse.Data data) {
        if (data == null) {
            return null;
        }
        NewDocMetaResponse.DocMeta docMeta = data.getDocMetas().get(0);

        DocMetaReps.Data data1 = new DocMetaReps.Data();

        data1.setObjType(docMeta.getDocsType());
        data1.setOwner(docMeta.getOwnerId());
        data1.setTitle(docMeta.getTitle());
        data1.setUrl(docMeta.getUrl());

        return data1;
    }
}
