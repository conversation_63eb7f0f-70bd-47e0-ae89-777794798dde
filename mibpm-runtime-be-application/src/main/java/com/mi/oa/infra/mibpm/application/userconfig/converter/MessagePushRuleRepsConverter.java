package com.mi.oa.infra.mibpm.application.userconfig.converter;

import com.mi.oa.infra.mibpm.application.message.dto.reps.MessagePushRuleReps;
import com.mi.oa.infra.mibpm.domain.userconfig.model.MessagePushRuleConfig;
import org.mapstruct.Mapper;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/22 11:03
 */
@Mapper(componentModel = "spring")
public interface MessagePushRuleRepsConverter {

    MessagePushRuleReps doToDto(MessagePushRuleConfig messagePushRuleConfig);
}
