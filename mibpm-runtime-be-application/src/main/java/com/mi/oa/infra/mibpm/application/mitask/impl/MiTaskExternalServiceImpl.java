package com.mi.oa.infra.mibpm.application.mitask.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.mi.flowable.idm.api.AppInfo;
import com.mi.info.comb.common.x5protocol.core.dto.X5Request;
import com.mi.info.comb.common.x5protocol.core.util.X5ProtocolCoreUtils;
import com.mi.oa.infra.mibpm.application.message.service.MessageService;
import com.mi.oa.infra.mibpm.application.mitask.converter.AppInfoConverter;
import com.mi.oa.infra.mibpm.application.mitask.dto.ExtCardBot;
import com.mi.oa.infra.mibpm.application.mitask.factory.CardBotBuildFactory;
import com.mi.oa.infra.mibpm.application.mitask.service.MiTaskExternalService;
import com.mi.oa.infra.mibpm.application.task.service.ExternalTaskApplicationService;
import com.mi.oa.infra.mibpm.common.constant.LarkMessageConstants;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.common.model.CardBot;
import com.mi.oa.infra.mibpm.common.model.ExternalContent;
import com.mi.oa.infra.mibpm.common.model.MessageResult;
import com.mi.oa.infra.mibpm.domain.mitask.ability.MiTaskExternalAbility;
import com.mi.oa.infra.mibpm.domain.mitask.model.ExternalAppInfoDo;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.mitask.service.MiTaskExternalDomainService;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskMetaRepository;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskRepository;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 * @Description
 */
@Service
@Slf4j
public class MiTaskExternalServiceImpl implements MiTaskExternalService {

    @Autowired
    private MiTaskExternalDomainService miTaskExternalDomainService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private MiTaskMetaRepository miTaskMetaRepository;
    @Autowired
    private CardBotBuildFactory cardBotBuildFactory;
    @Autowired
    private AppInfoConverter appInfoConverter;
    @Autowired
    private MiTaskRepository miTaskRepository;
    @Autowired
    private MiTaskExternalAbility miTaskExternalAbility;
    @Autowired
    private ExternalTaskApplicationService externalTaskApplicationService;

    @Override
    public MessageResult sendExtMessageCard(ExtCardBot extCardBot, boolean isSend) {
        log.info("sendExtMessageCard, extCardBot: {}", extCardBot.toString());
        CardBot cardBot = new CardBot();
        BeanUtils.copyProperties(extCardBot, cardBot);
        cardBot = cardBotBuildFactory.setColor(cardBot);
        cardBot.setSource(LarkMessageConstants.CARD_SOURCE_EXT);
        cardBot.setWebView(false);

        // 保存三方数据
        cardBot.setExtProcessKey(extCardBot.getProcessKey());
        cardBot.setExtInstanceId(extCardBot.getInstanceId());
        cardBot.setExtTaskId(extCardBot.getTaskId());

        // 获取三方业务系统流程信息
        AppInfo extAppInfo = getAppInfoById(extCardBot.getAppId());

        // 参数转化
        cardBot = cardBotBuildFactory.extParamsToBpmParams(extCardBot, cardBot, extAppInfo);

        //获取流程信息
        cardBot = cardBotBuildFactory.setInstanceInfo(extCardBot, cardBot);

        messageService.syncAppBadge(cardBot.getUsername());

        return miTaskExternalDomainService.doSendMessageCard(cardBot, extAppInfo, isSend);
    }

    @Override
    public MessageResult sendExtV2MessageCard(ExtCardBot extCardBot, boolean isSend) {
        log.info("sendExtMessageCard, v2, extCardBot: {}", extCardBot.toString());
        CardBot cardBot = new CardBot();
        BeanUtils.copyProperties(extCardBot, cardBot);

        cardBot.setSource(LarkMessageConstants.CARD_SOURCE_EXT);
        cardBot.setWebView(false);

        cardBot = cardBotBuildFactory.setExtTitle(cardBot);
        cardBot = cardBotBuildFactory.setColor(cardBot);
        cardBot = cardBotBuildFactory.setContent(extCardBot, cardBot);

        //保存三方数据
        cardBot.setExtProcessKey(extCardBot.getProcessKey());
        cardBot.setExtInstanceId(extCardBot.getInstanceId());
        cardBot.setExtTaskId(extCardBot.getTaskId());

        //获取三方业务系统流程信息
        AppInfo extAppInfo = getAppInfoById(extCardBot.getAppId());

        //  参数转化
        cardBot = cardBotBuildFactory.extParamsToBpmParams(extCardBot, cardBot, extAppInfo);

        //获取流程信息
        cardBot = cardBotBuildFactory.setInstanceInfo(extCardBot, cardBot);

        messageService.syncAppBadge(cardBot.getUsername());

        return miTaskExternalDomainService.doSendMessageCard(cardBot, extAppInfo, isSend);
    }

    @Override
    public ExtCardBot dataToExtCardBotBo(String data) {
        String bodyStr = "";
        String appId = "";
        try {
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            X5Request x5data = X5ProtocolCoreUtils.parseX5Request(data, objectMapper);
            bodyStr = x5data.getBody();
            X5Request.Header header = x5data.getHeader();
            appId = header.getAppId();
        } catch (Exception e) {
            log.error("data error, data: {}", data);
            throw new IllegalArgumentException("data error!");
        }
        Gson gson = new Gson();
        ExtCardBot extCardBot = gson.fromJson(bodyStr, ExtCardBot.class);
        extCardBot.setAppId(appId);
        return extCardBot;
    }

    @Override
    public void dataValidate(ExtCardBot extCardBot) {
        Assert.hasText(extCardBot.getProcessKey(), "processKey is not allowed to be empty");
        Assert.hasText(extCardBot.getInstanceId(), "instanceId is not allowed to be empty");
        Assert.hasText(extCardBot.getTaskId(), "taskId is not allowed to be empty");
        Assert.hasText(extCardBot.getTitle(), "title is not allowed to be empty");
        Assert.hasText(extCardBot.getUsername(), "username is not allowed to be empty");

        List<Actions> actions = extCardBot.getActions();
        //Assert.notEmpty(actions, "actions is not allowed to be empty");

        for (Actions action : actions) {
            Assert.isTrue(Actions.ACTION_NAME_MAP.containsKey(action.getActionKey()),
                    "actionKey is Invalid : " + action.getActionKey());

            // 校验url数据
            int urlCount = 0;
            urlCount += StringUtils.isNotEmpty(action.getAndroidUrl()) ? 1 : 0;
            urlCount += StringUtils.isNotEmpty(action.getIosUrl()) ? 1 : 0;
            urlCount += StringUtils.isNotEmpty(action.getPcUrl()) ? 1 : 0;

            Assert.isTrue(urlCount == 0 || urlCount == 3, "按钮链接必须完整或者全部为空!");
        }
    }

    @Override
    public void dataValidateV2(ExtCardBot extCardBot) {
        Assert.hasText(extCardBot.getProcessKey(), "processKey is not allowed to be empty");
        Assert.hasText(extCardBot.getInstanceId(), "instanceId is not allowed to be empty");
        Assert.hasText(extCardBot.getTaskId(), "taskId is not allowed to be empty");

        String username = extCardBot.getUsername();
        String status = extCardBot.getStatus();
        ExternalContent content = extCardBot.getContent();
        String approverName = content.getApproverName();

        if (StringUtils.isEmpty(username) || username.length() == 0) {
            Assert.hasText(approverName, "approverName is not allowed to be empty");
            Assert.hasText(status, "approverName is not allowed to be empty");
        }

        List<Actions> actions = extCardBot.getActions();
        Assert.notEmpty(actions, "actions is not allowed to be empty");

        for (Actions action : actions) {
            Assert.isTrue(Actions.ACTION_NAME_MAP.containsKey(action.getActionKey()),
                    "actionKey is Invalid : " + action.getActionKey());
        }
    }

    @Override
    public Map<String, Object> getResponseMsg(String code, String msg, String messageId) {
        Map<String, Object> res = new HashMap<>();

        Map<String, Object> body = new HashMap<>();
        body.put("code", code);
        body.put("msg", msg);
        Map<String, String> data = new HashMap<>();
        data.put("messageId", messageId);
        body.put("data", data);

        Gson gson = new Gson();
        String bodyJson = gson.toJson(body);
        res.put("body", bodyJson);
        return res;
    }

    @Override
    public AppInfo getAppInfoById(String appId) {
        ExternalAppInfoDo extAppInfo = miTaskMetaRepository.findAppInfoByAppId(appId);
        return appInfoConverter.convertDo(extAppInfo);
    }

    @Override
    public Object doExtCallBackHandler(CardBot cardBotBO, String mibpmUrl, String actionKey,
                                       Map<String, Object> params) {
        log.info("doExtCallBackHandler, cardBot={}", GsonUtils.toJsonWtihNullField(cardBotBO));
        //触发查看详情按钮 不进行回调与更新
        if (LarkMessageConstants.DETAIL.equals(actionKey)) {
            return null;
        }
        AppInfo appInfo = getAppInfoById(cardBotBO.getAppId());
        MiTaskDo extMiTaskDo = miTaskRepository.findMiTaskDoByTaskId(cardBotBO.getExtTaskId());
        if (extMiTaskDo == null) {
            extMiTaskDo = miTaskRepository.findMiTaskDoByTaskId(cardBotBO.getTaskId());
        }

        // 任务未处理，需要回调三方系统
        if (extMiTaskDo != null && (extMiTaskDo.getEndTime() == null || extMiTaskDo.getEndTime().toInstant().toEpochMilli() == 0)) {
            externalTaskApplicationService.completeTaskAndCallback(extMiTaskDo.getTaskId(), "", actionKey,
                    cardBotBO.getUsername(), Boolean.TRUE);
        }

        String cardJson = miTaskExternalAbility.refreshCard(cardBotBO, appInfo);
        messageService.syncAppBadge(cardBotBO.getUsername());
        return cardJson;
    }
}
