package com.mi.oa.infra.mibpm.application.userconfig.converter;

import com.mi.oa.infra.mibpm.application.message.dto.req.CreateMessagePushRuleReq;
import com.mi.oa.infra.mibpm.application.message.dto.req.UpdateMessagePushRuleReq;
import com.mi.oa.infra.mibpm.domain.userconfig.model.MessagePushRuleConfig;
import org.mapstruct.Mapper;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/22 11:03
 */
@Mapper(componentModel = "spring")
public interface MessagePushRuleReqConverter {

    MessagePushRuleConfig dtoToDo(CreateMessagePushRuleReq createMessagePushRuleReq);

    MessagePushRuleConfig dtoToDo(UpdateMessagePushRuleReq updateMessagePushRuleReq);
}
