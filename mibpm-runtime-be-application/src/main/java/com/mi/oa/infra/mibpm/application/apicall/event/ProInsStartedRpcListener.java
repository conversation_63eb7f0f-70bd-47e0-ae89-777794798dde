package com.mi.oa.infra.mibpm.application.apicall.event;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.ProcInstStartedEvent;
import com.mi.oa.infra.mibpm.common.model.ApiCallVariable;
import com.mi.oa.infra.mibpm.flowable.bpmn.BpmnModelService;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.BtdEventCallBack;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventCallBack;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.bpmn.model.StartEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/17 20:50
 */
@Slf4j
@Component
public class ProInsStartedRpcListener extends AbstractRpcListener<ProcInstStartedEvent> {

    @Autowired
    private BpmnModelService bpmnModelService;
    @Autowired
    private BpmnExtensionHelper bpmnExtensionHelper;

    @Override
    public String identifier() {
        return EventIdentify.PROCESS_STARTED.name();
    }

    @Override
    public void process(ProcInstStartedEvent procInstStartedEvent) {
        log.info("服务调用消费流程启动事件，event = {}", procInstStartedEvent);
        // 获取开始节点
        StartEvent startEvent = bpmnModelService.findStartEvent(procInstStartedEvent.getProcessDefId());
        // 获取开始节点包装对象
        EventWrapper eventWrapper = bpmnExtensionHelper.getEventWrapper(startEvent);

        List<EventCallBack> eventCallBacks = eventWrapper.getEventCallBacks();
        if (CollectionUtils.isNotEmpty(eventCallBacks)) {
            List<EventCallBack> processStartedEventCallBacks = new ArrayList<>();
            for (EventCallBack eventCallBack : eventCallBacks) {
                if (eventCallBack.getEventCode().equals(this.getSupportEventCode())) {
                    processStartedEventCallBacks.add(eventCallBack);
                }
            }
            if (CollectionUtils.isNotEmpty(processStartedEventCallBacks)) {
                ApiCallVariable apiCallVariable = this.obtainBpmCallVariable(procInstStartedEvent.getProcessInstanceId(),
                        null, null, null, procInstStartedEvent);

                // 执行调用
                bpmInvokeCalls(eventCallBacks, apiCallVariable);
            }
        }

        List<BtdEventCallBack> btdEventCallBacks = eventWrapper.getBtdEventCallBacks();
        if (CollectionUtils.isNotEmpty(btdEventCallBacks)) {
            btdEventCallBacks = btdEventCallBacks.stream()
                    .filter(e -> e.getEventType().contains(this.getSupportEventCode()))
                    .filter(BtdEventCallBack::isValid)
                    .collect(Collectors.toList());

            // 执行btd调用
            btdInvokeCalls(btdEventCallBacks, procInstStartedEvent.getProcessInstanceId(), null, null, null, procInstStartedEvent);
        }
    }

    @Override
    String getSupportEventCode() {
        return EventIdentify.PROCESS_STARTED.name();
    }

    @Override
    protected boolean isSupportAllOperateEvent() {
        return false;
    }
}
