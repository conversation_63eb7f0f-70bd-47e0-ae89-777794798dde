package com.mi.oa.infra.mibpm.application.auth;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateCarbonCopiedEvent;
import com.mi.oa.infra.mibpm.common.model.FormRelationProcessDataDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/14 14:59
 **/
@Slf4j
@Component
public class TaskCcAuthListener extends AbstractAuthListener<OperateCarbonCopiedEvent> {

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_CC.name();
    }

    @Override
    public void process(OperateCarbonCopiedEvent event) {
        String processInstanceId = event.getProcessInstanceId();
        FormRelationProcessDataDto relationData = queryFormInstData(processInstanceId);
        List<String> formTaskIds = relationData.getTaskIds();
        List<String> modelCodes = relationData.getModelCodes();
        if (CollectionUtils.isNotEmpty(formTaskIds)) {
            Map<String, List<String>> relatedProcTaskMap = mapProcTask(formTaskIds, modelCodes);
            List<String> permissionCodes = genPermissionCodes(relatedProcTaskMap);
            List<String> ccUsers = event.getCcTo();
            if (CollectionUtils.isNotEmpty(ccUsers)) {
                assignDataPermission(permissionCodes, ccUsers);
            }
        }
    }
}
