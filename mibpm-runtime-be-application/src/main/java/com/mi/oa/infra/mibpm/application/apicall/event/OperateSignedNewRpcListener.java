package com.mi.oa.infra.mibpm.application.apicall.event;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateSignedEvent;
import com.mi.oa.infra.mibpm.common.model.ApiCallVariable;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventCall;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventCallBack;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 审批任务新加签事件监听器
 */

@Slf4j
@Service
public class OperateSignedNewRpcListener extends AbstractRpcListener<OperateSignedEvent> {

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_SIGNED_NEW.name();
    }

    @Override
    public void process(OperateSignedEvent event) {
        log.info("服务调用模块消费任务新加签事件，event = {}", event);
        List<EventCall> eventCallBacks = this.getEventCallBack(event.getProcessDefinitionId(),
                event.getTaskDefinitionKey());
        if (CollectionUtils.isEmpty(eventCallBacks)) {
            return;
        }

        List<EventCallBack> bpmEventCall = eventCallBacks.stream().filter(EventCallBack.class::isInstance)
                .map(EventCallBack.class::cast).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bpmEventCall)) {
            ApiCallVariable apiCallVariable = this.obtainBpmCallVariable(event.getProcessInstanceId(),
                    event.getTaskId(), event.getFormData(), event.getOperator(), event);
            // 执行调用
            bpmInvokeCalls(eventCallBacks, apiCallVariable);
        }
        // 执行btd调用
        btdInvokeCalls(eventCallBacks, event.getProcessInstanceId(), event.getTaskId(), event.getFormData(), event.getOperator(), event);
    }

    @Override
    String getSupportEventCode() {
        return UserTaskOperation.SIGNATURE.getCode();
    }
}
