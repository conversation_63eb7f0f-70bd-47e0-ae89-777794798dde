package com.mi.oa.infra.mibpm.application.task.event;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateApprovedEvent;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.remote.sdk.FormRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
/*@Service*/
public class TaskApprovedListener implements EventSubscriber<OperateApprovedEvent> {

    @Autowired
    private FormRemoteService formRemoteService;

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_APPROVED.name();
    }

    @Override
    public void process(OperateApprovedEvent event) {
        log.info("接收到审批任务已同意的事件 event = {}", event);

        if (MapUtils.isNotEmpty(event.getFormData())) {
            // 保存表单数据
           /* formRemoteService.saveFormData(event.getProcessDefinitionId(), event.getProcessInstanceId(), null,
                    event.getTaskDefinitionKey(), event.getFormData());*/
        }
    }

}
