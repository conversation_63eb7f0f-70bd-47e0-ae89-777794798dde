package com.mi.oa.infra.mibpm.application.migrate;

import cn.hutool.core.thread.NamedThreadFactory;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.mi.oa.infra.mibpm.common.enums.SourceEnum;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.MiErpProcessItemMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.MiTaskProcessInstMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiErpProcessItemPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessInstPo;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.MultiInstanceLoopCharacteristics;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

/**
 * @FileName MiTaskFlushSignTypeService
 * @Description
 * <AUTHOR>
 * @date 2025-03-24
 **/
@Slf4j
@Service
public class MiTaskFlushSignTypeService {

    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private MiTaskProcessInstMapper miTaskProcessInstMapper;
    @Autowired
    private ModelMetaRepository modelMetaRepository;
    @Autowired
    private MiErpProcessItemMapper miErpProcessItemMapper;

    // 定义一个成员变量用于存储缓存数据
    private final Cache<String, String> flushCache = Caffeine.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS) // 设置缓存数据的有效时间
            .maximumSize(3000) // 设置缓存的最大容量
            .build();

    private static final String KEY_PREFIX = "approve:processDef:%s";

    private static final List<Long> FAILED_SIGN_TYPE_IDS = new ArrayList<>();
    private static final List<Exception> FAILED_BATCH_IDS = new ArrayList<>();
    private static final List<String> FLUSH_COMPLETE = new ArrayList<>();
    private static final List<String> COMPUTE_NULL_LIST = new ArrayList<>();

    // 新增线程池配置
    private static final int BATCH_SIZE = 50;
    private static final int THREAD_COUNT = 10;
    private static final int MAX_QUEUE_SIZE = 500;
    private ExecutorService executor;
    private final BlockingQueue<IdRange> dataQueue = new LinkedBlockingQueue<>(MAX_QUEUE_SIZE);
    private volatile boolean isProducing = true;
    private final AtomicLong processedCount = new AtomicLong(0);
    private volatile Long currentStartId = 0L;

    // 定义ID范围的任务类
    @Data
    private static class IdRange {
        private final long startId;
        private final long endId;
    }

    @PostConstruct
    public void init() {
        // 配置线程池
        this.executor = new ThreadPoolExecutor(
                THREAD_COUNT, THREAD_COUNT,
                30L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(THREAD_COUNT),
                new NamedThreadFactory("SignType-Consumer-", false),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    public void flushSignType() {
        flushCache.invalidate("Fail-SignType-ID");
        new Thread(this::startProducer, "SignType-Producer-").start();
        startConsumers();
        monitorProgress();
    }

    private void startProducer() {
        try {
            long minId = miTaskProcessInstMapper.minId();
            long maxId = miTaskProcessInstMapper.maxId();
            currentStartId = minId;
            long batchMaxId = 0;
            while (isProducing) {
                if (currentStartId >= maxId) {
                    isProducing = false;
                    break;
                }
                // 计算本批次处理的最大id
                batchMaxId = Math.min(currentStartId + BATCH_SIZE, maxId);
                // 阻塞式插入，控制内存
                dataQueue.put(new IdRange(currentStartId, batchMaxId));
                currentStartId = batchMaxId + 1;
            }
            COMPUTE_NULL_LIST.add(batchMaxId + ":" + maxId);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            isProducing = false;
        }
    }

    private void startConsumers() {
        for (int i = 0; i < THREAD_COUNT; i++) {
            int finalI = i;
            executor.submit(() -> {
                while (isProducing || !dataQueue.isEmpty()) {
                    try {
                        IdRange range = dataQueue.poll(1, TimeUnit.SECONDS);
                        if (range == null) {
                            continue;
                        }
                        List<MiTaskProcessInstPo> batch = miTaskProcessInstMapper.selectMiTaskMainTable(
                                range.getStartId(), range.getEndId());
                        if (batch != null && !batch.isEmpty()) {
                            processBatch(batch);
                        }
                    } catch (InterruptedException e) {
                        FAILED_BATCH_IDS.add(e);
                        flushCache.put("Fail-BATCH-ID", GsonUtils.toJsonFilterNullField(FAILED_BATCH_IDS));
                        log.error("批量更新失败！", e);
                        Thread.currentThread().interrupt();
                    }
                }
                FLUSH_COMPLETE.add(System.currentTimeMillis() + "线程" + finalI + "已完成清洗任务");
            });
        }
    }

    private void processBatch(List<MiTaskProcessInstPo> batch) {
        // 批量计算签名
        for (MiTaskProcessInstPo item : batch) {
            try {
                reviseBPMSource(item);
                String signType = computeUserTaskSignType(item.getProcDefId(), item.getTaskDefKey(), item.getSource());
                if (signType == null) {
                    log.error("signType计算为null！");
                    item.setSignType("compute error");
                } else {
                    item.setSignType(signType);
                }
            } catch (Exception e) {
                log.error("Error computing sign type for id {}, item: {}", item.getId(), item, e);
                FAILED_SIGN_TYPE_IDS.add(item.getId());
                flushCache.put("Fail-SignType-ID", GsonUtils.toJsonFilterNullField(FAILED_SIGN_TYPE_IDS));
                item.setSignType("compute error");
            }
        }

        // 批量更新
        miTaskProcessInstMapper.batchUpdateSignType(batch);

        // 更新进度
        processedCount.addAndGet(batch.size());
    }

    // 新增进度监控方法
    private void monitorProgress() {
        long total = miTaskProcessInstMapper.countMiTaskMainTable();
        new Thread(() -> {
            while (isProducing || !dataQueue.isEmpty()) {
                try {
                    double progress = (processedCount.get() * 100.0) / total;
                    log.info("当前进度: {}% (已处理: {}/总数: {})", progress, processedCount.get(), total);
                    TimeUnit.SECONDS.sleep(30);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    break;
                }
            }
            shutdown();
        }).start();
    }

    public String flushProgress() {
        String value = flushCache.getIfPresent("TotalNum");
        long total;
        if (value != null && !value.isEmpty()) {
            total = Long.parseLong(value);
        } else {
            total = miTaskProcessInstMapper.countMiTaskMainTable();
            flushCache.put("TotalNum", String.valueOf(total));
        }
        double progress = (processedCount.get() * 100.0) / total;
        // 1. 基础进度信息
        String resultProcess = String.format(
                "当前进度: %.2f%% (已处理: %d/总数: %d)",
                progress, processedCount.get(), total
        );
        // 2. 生产者队列信息
        String queueInfo = String.format(
                "生产者队列: %d/%d (最小元素: %s)",
                dataQueue.size(), MAX_QUEUE_SIZE,
                dataQueue.isEmpty() ? "空" : dataQueue.peek()
        );
        // 3. 缓存个数
        String cacheInfo = String.format(
                "缓存个数: %d",
                flushCache.estimatedSize()
        );
        // 4. 生产状态
        String producerInfo = String.format(
                "生产者状态: %s",
                isProducing ? "生产中" : "已停止"
        );
        // 5. 批量失败的异常情况
        String batchFailInfo = String.format(
                "批量失败的异常情况: %s",
                flushCache.getIfPresent("Fail-BATCH-ID")
        );
        // 6. 线程完成清洗任务情况
        String flushCompleteInfo = String.format(
                "线程完成清洗任务情况: %s",
                FLUSH_COMPLETE
        );
        // 7. 计算结果为空的情况
        String computeNullInfo = String.format(
                "计算结果为空的情况: %s",
                COMPUTE_NULL_LIST
        );
        // 8. 线程池状态（需强制转换为 ThreadPoolExecutor）
        if (executor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor pool = (ThreadPoolExecutor) executor;
            String poolInfo = String.format(
                    "线程池: 活跃=%d/最大=%d, 任务队列=%d/%d, 已完成=%d",
                    pool.getActiveCount(),          // 当前活跃线程数
                    pool.getMaximumPoolSize(),      // 最大线程数
                    pool.getQueue().size(),        // 线程池工作队列大小
                    THREAD_COUNT * 10,             // 线程池队列容量
                    pool.getCompletedTaskCount()   // 已完成任务数
            );
            return String.join("    ", resultProcess, queueInfo, cacheInfo, producerInfo, batchFailInfo,
                    computeNullInfo, flushCompleteInfo, poolInfo);
        }
        // 默认返回（非ThreadPoolExecutor情况）
        return String.join("    ", resultProcess, queueInfo, cacheInfo, producerInfo, batchFailInfo, computeNullInfo,
                flushCompleteInfo);
    }

    public void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
                // 再次等待残留任务终止（可选）
                if (!executor.awaitTermination(5, TimeUnit.MINUTES)) {
                    log.warn("线程池未能在合理时间内终止");
                }
            }
        } catch (InterruptedException e) {
            // 恢复中断状态，让上层处理
            Thread.currentThread().interrupt();
            executor.shutdownNow();
        }
    }

    public String getFailId(String key) {
        return GsonUtils.toJsonFilterNullField(FAILED_SIGN_TYPE_IDS);
    }

    /**
     * <AUTHOR>
     * @description 通过procDefId和taskDefKey计算节点审批方式
     * @date 下午5:52 2025/3/21
     * @param processDefinitionId
     * @param taskDefinitionKey
     * @return String
     */
    public String computeUserTaskSignType(String processDefinitionId, String taskDefinitionKey, String source) {
        String cacheKey = String.format(KEY_PREFIX, processDefinitionId);
        // 先从Caffeine缓存中取
        String signTypeMapString = flushCache.getIfPresent(cacheKey);
        if (signTypeMapString != null) {
            Map<String, String> userTaskApproveType = GsonUtils.fromJson(signTypeMapString, Map.class);
            if (userTaskApproveType.containsKey(taskDefinitionKey) && StringUtils.isNotBlank(userTaskApproveType.get(taskDefinitionKey))) {
                return userTaskApproveType.get(taskDefinitionKey);
            }
        }
        // 缓存中没有的话根据流程来源计算审批节点类型
        BpmnModel model;
        try {
            model = repositoryService.getBpmnModel(processDefinitionId);
        } catch (Exception e) {
            try {
                Thread.sleep(10);
            } catch (InterruptedException ex) {
                log.error("线程sleep失败", ex);
                Thread.currentThread().interrupt(); // 重新设置中断状态
            }
            log.warn("bpmModel失败从新计算！");
            model = repositoryService.getBpmnModel(processDefinitionId);
            log.info("bpmModel重新计算结束！");
        }

        String signType = "";
        if (SourceEnum.BPM1_PROCESS.name().equals(source) || SourceEnum.BPM2_PROCESS.name().equals(source)) {
            signType = computeBPM2SignType(model, taskDefinitionKey, cacheKey);
        } else if (SourceEnum.BPM3_PROCESS.name().equals(source)) {
            signType = computeBPM3SignType(model, taskDefinitionKey, cacheKey);
        } else if (SourceEnum.UNIFIED_TODO_EXTERNAL.name().equals(source)) {
            return "";
        }
        return signType;
    }

    /**
    * <AUTHOR>
    * @description BPM3.0流程计算节点审批类型
    * @date 下午4:21 2025/3/25
    * @param model
     * @param taskDefinitionKey
     * @param cacheKey
    * @return String
    */
    private String computeBPM3SignType(BpmnModel model, String taskDefinitionKey, String cacheKey) {
        List<UserTask> userTasks = model.getMainProcess().findFlowElementsOfType(UserTask.class);
        BpmnExtensionHelper bpmnExtensionHelper = SpringContextUtil.getBean(BpmnExtensionHelper.class);
        // 获取流程定义下所有的userTask 并且判定当前审批节点类型
        Map<String, String> signTypeMap = userTasks.stream()
                .collect(Collectors.toMap(
                        UserTask::getId,
                        userTask -> {
                            UserTaskWrapper userTaskWrapper = bpmnExtensionHelper.getUserTaskWrapper(userTask);
                            return userTaskWrapper.getSignType().name();
                        },
                        (existing, replacement) -> replacement
                ));
//        // 创建一个Map用于存储结果
//        Map<String, String> signTypeMap = new HashMap<>();
//        // 遍历userTasks列表
//        for (UserTask userTask : userTasks) {
//            try {
//                // 获取当前userTask的ID
//                String userTaskId = userTask.getId();
//
//                // 通过bpmnExtensionHelper获取UserTaskWrapper实例
//                UserTaskWrapper userTaskWrapper = bpmnExtensionHelper.getUserTaskWrapper(userTask);
//
//                // 获取审批类型并转换为String
//                String signType = userTaskWrapper.getSignType().name();
//
//                // 将ID和对应的审批类型放入map中
//                signTypeMap.put(userTaskId, signType);
//            } catch (Exception e) {
//                // 捕获异常以便于了解具体是哪个userTask导致的问题
//                System.err.println("Error processing user task: " + userTask.getId());
//                e.printStackTrace();
//            }
//        }

        flushCache.put(cacheKey, GsonUtils.toJsonFilterNullField(signTypeMap));
        return signTypeMap.getOrDefault(taskDefinitionKey, "");
    }

    /**
    * <AUTHOR>
    * @description BPM2.0/1.0流程计算节点审批类型
    * @date 下午3:18 2025/3/25
    * @param model
     * @param taskDefinitionKey
     * @param cacheKey
    * @return String
    */
    private String computeBPM2SignType(BpmnModel model, String taskDefinitionKey, String cacheKey) {
        // 获取流程定义下所有的userTask 并且判定当前审批节点类型
        Map<String, String> userTaskApproveType = model.getMainProcess().getFlowElements()
                .stream()
                .filter(UserTask.class::isInstance)
                .map(UserTask.class::cast)
                .collect(Collectors.toMap(UserTask::getId, ut -> getTaskApproveType(ut.getLoopCharacteristics())));
        flushCache.put(cacheKey, GsonUtils.toJsonFilterNullField(userTaskApproveType));
        return userTaskApproveType.getOrDefault(taskDefinitionKey, "");
    }

    /**
     * <AUTHOR>
     * @description 通过UserTask的属性计算节点审批方式
     * @date 下午5:52 2025/3/21
     * @param loopCharacteristics
     * @return String
     */
    private String getTaskApproveType(MultiInstanceLoopCharacteristics loopCharacteristics) {
        if (loopCharacteristics != null) {
            String completionCondition = loopCharacteristics.getCompletionCondition();
            boolean sequential = loopCharacteristics.isSequential();
            // 非会签
            if ("${nrOfCompletedInstances >= 1}".equals(completionCondition)) {
                return "PARALLEL_ONE";
            }
            // 会签
            if ("${status == 'reject' ||  status == 'interrupt' || nrOfActiveInstances == 0}".equals(completionCondition) && !sequential) {
                return "PARALLEL_ALL";
            }
            // 顺签
            if ("${status == 'reject' ||  status == 'interrupt' || nrOfActiveInstances == 0}".equals(completionCondition) && sequential) {
                return "SEQUENTIAL";
            }
        }
        // 其余都算单签
        return "SINGLE";
    }

    /**
    * <AUTHOR>
    * @description 修正审批来源
    * @date 下午4:51 2025/4/21
    * @param item
    * @return
    */
    public void reviseBPMSource(MiTaskProcessInstPo item) {
        if (SourceEnum.BPM3_PROCESS.name().equals(item.getSource())) {
            String processDefinitionId = item.getProcDefId();
            String modelCode = processDefinitionId.split(":")[0];
            int version = Integer.parseInt(processDefinitionId.split(":")[1]);
            ModelMeta modelMeta = modelMetaRepository.queryByModelCode(modelCode);
            if (!modelCode.startsWith("bpmn_") && version <= modelMeta.getMigrationProcDefVersion()) {
                MiErpProcessItemPo miErpProcessItemPo = miErpProcessItemMapper.selectById(modelCode);
                if (miErpProcessItemPo != null) {
                    if (Integer.valueOf(0).equals(miErpProcessItemPo.getUseFreeForm())) {
                        item.setSource(SourceEnum.BPM1_PROCESS.name());
                    } else {
                        item.setSource(SourceEnum.BPM2_PROCESS.name());
                    }
                }
            }
        }
    }
}
