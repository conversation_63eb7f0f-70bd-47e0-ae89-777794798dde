package com.mi.oa.infra.mibpm.application.apicall.event;

import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.ActivityCompletedEvent;
import com.mi.oa.infra.mibpm.common.model.ApiCallVariable;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventCall;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventCallBack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动节点结束
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/17 20:50
 */
@Slf4j
@Component
public class UserTaskActivityCompletedRpcListener extends AbstractRpcListener<ActivityCompletedEvent> {

    @Override
    public String identifier() {
        return EventIdentify.ACTIVITY_COMPLETED.name();
    }

    @Override
    public void process(ActivityCompletedEvent activityCompletedEvent) {
        log.info("服务调用模块消费活动节点结束事件，event = {}", activityCompletedEvent);

        if (!ActivityTypeEnum.USER_TASK.getCode().equals(activityCompletedEvent.getActivityType())) {
            return;
        }
        List<EventCall> eventCallBacks = this.getEventCallBack(activityCompletedEvent.getProcessDefinitionId(),
                activityCompletedEvent.getTaskDefinitionKey());

        if (CollectionUtils.isEmpty(eventCallBacks)) {
            return;
        }

        List<EventCallBack> bpmEventCall = eventCallBacks.stream().filter(e -> e instanceof EventCallBack)
                .map(e -> (EventCallBack) e).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bpmEventCall)) {
            ApiCallVariable apiCallVariable = this.obtainBpmCallVariable(activityCompletedEvent.getProcessInstanceId(),
                    null, activityCompletedEvent.getFormData(), null, activityCompletedEvent);

            // 执行调用
            bpmInvokeCalls(eventCallBacks, apiCallVariable);
        }
        // 执行btd调用
        btdInvokeCalls(eventCallBacks, activityCompletedEvent.getProcessInstanceId(),
                null, activityCompletedEvent.getFormData(), null, activityCompletedEvent);
    }

    @Override
    String getSupportEventCode() {
        return EventIdentify.ACTIVITY_COMPLETED.name();
    }

    @Override
    protected boolean isSupportAllOperateEvent() {
        return false;
    }
}
