package com.mi.oa.infra.mibpm.application.userconfig.errorcode;

import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;

/**
 * <AUTHOR>
 * @Date 2024/08/21 14:35
 */
public enum PermissionManageErrorCodeEnum implements DomainErrorCode {
    NOT_ADMIN(1, "仅限管理员操作"),
    ALREADY_HAS_PERMISSION(2, "已存在该权限");

    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    PermissionManageErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }
}
