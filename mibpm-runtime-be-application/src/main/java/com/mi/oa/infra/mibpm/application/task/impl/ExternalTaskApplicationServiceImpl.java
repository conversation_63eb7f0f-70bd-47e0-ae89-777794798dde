package com.mi.oa.infra.mibpm.application.task.impl;

import com.google.gson.reflect.TypeToken;
import com.mi.flowable.external.api.ExtProcessItem;
import com.mi.flowable.external.api.ExternalService;
import com.mi.flowable.external.api.ProcessDefinitionRepresentation;
import com.mi.flowable.external.api.ProcessInstanceCheckRepresentation;
import com.mi.flowable.external.api.ProcessInstanceRepresentation;
import static com.mi.flowable.external.api.ProcessInstanceRepresentation.REPLACE_MODEL;
import static com.mi.flowable.external.api.ProcessInstanceRepresentation.UPDATE_MODEL;
import com.mi.flowable.external.api.TaskRepresentation;
import com.mi.flowable.rpc.x5.X5Caller;
import com.mi.flowable.rpc.x5.X5Response;
import com.mi.oa.infra.mibpm.application.message.service.MessageService;
import com.mi.oa.infra.mibpm.application.task.converter.MiTaskExtConverter;
import com.mi.oa.infra.mibpm.application.task.service.ExternalTaskApplicationService;
import com.mi.oa.infra.mibpm.common.constant.BpmConstants;
import com.mi.oa.infra.mibpm.common.exception.InfraException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.domain.mitask.factory.MiTaskDoFactory;
import com.mi.oa.infra.mibpm.domain.mitask.model.ExternalAppInfoDo;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskMetaDo;
import com.mi.oa.infra.mibpm.domain.mitask.service.MiTaskMetaDomainService;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.infra.lock.Lockable;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskMetaRepository;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.ExtRuTaskInstMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExtRuTaskInstPo;
import com.mi.oa.infra.mibpm.infra.task.errorcode.TaskInfraErrorCodeEnum;
import com.mi.oa.infra.mibpm.infra.task.repository.HiTaskModelCodeRepository;
import com.mi.oa.infra.mibpm.utils.EmojiFilter;
import com.mi.oa.infra.mibpm.utils.ExternalThreadPool;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.BidiMap;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;
import org.flowable.common.engine.api.FlowableIllegalArgumentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@EnableRetry
public class ExternalTaskApplicationServiceImpl implements ExternalTaskApplicationService {
    public static final BidiMap<String, String> TASK_QUERY_STATUS_MAP = new DualHashBidiMap<>();

    static {
        /*
          bpm任务与三方任务状态映射
         */
        TASK_QUERY_STATUS_MAP.put(BpmConstants.OPERATE_BTU_INAPPROVAL, TaskRepresentation.PENDING);
        TASK_QUERY_STATUS_MAP.put(BpmConstants.OPERATE_BTN_AGREE, TaskRepresentation.APPROVED);
        TASK_QUERY_STATUS_MAP.put(BpmConstants.OPERATE_BTN_REJECT, TaskRepresentation.REJECTED);
        TASK_QUERY_STATUS_MAP.put(BpmConstants.OPERATE_BTN_TRANSFER, TaskRepresentation.TRANSFERRED);
    }

    @Autowired
    private MiTaskMetaRepository miTaskMetaRepository;
    @Autowired
    private MiTaskRepository miTaskRepository;
    @Autowired
    private MiTaskExtConverter miTaskExtConverter;
    @Autowired
    private MiTaskMetaDomainService miTaskMetaDomainService;
    @Autowired
    private MiTaskDoFactory miTaskDoFactory;
    @Autowired
    private ModelMetaRepository modelMetaRepository;
    @Autowired
    private HiTaskModelCodeRepository hiTaskModelCodeRepository;
    @Autowired
    private ExternalService externalService;
    @Autowired
    private ExtRuTaskInstMapper extRuTaskInstMapper;
    @Autowired
    private MessageService messageService;

    @Override
    public void createProcessDefinition(ProcessDefinitionRepresentation processDefinitionRepresentation, String appId) {

        MiTaskMetaDo miTaskMeta = miTaskExtConverter.convert(processDefinitionRepresentation);
        ExternalAppInfoDo extAppInfo = miTaskMetaRepository.findAppInfoByAppId(appId);
        if (extAppInfo == null) {
            throw new FlowableIllegalArgumentException("App id " + appId + " not found");
        }
        miTaskMeta.setTenantId(extAppInfo.getAppCode());
        miTaskMetaDomainService.checkMetaDo(miTaskMeta);
        miTaskMetaRepository.createMiTaskMeta(miTaskMeta);
    }

    @Override
    @Lockable(lockName = "'MONITOR:EXT:INSTANCE:LOCK:' + #instanceId")
    @Transactional(rollbackFor = Exception.class)
    @Retryable(
            value = {MySQLTransactionRollbackException.class,
                    DeadlockLoserDataAccessException.class,
                    SQLIntegrityConstraintViolationException.class},
            maxAttempts = 2,
            backoff = @Backoff(delay = 2000, multiplier = 2)
    )
    public void createProcessInstance(ProcessInstanceRepresentation processInstance, String appId, String instanceId) {
        MiTaskDo miTask = miTaskExtConverter.convert(processInstance);
        ExternalAppInfoDo extAppInfo = miTaskMetaRepository.findAppInfoByAppId(appId);
        if (extAppInfo == null) {
            throw new FlowableIllegalArgumentException("Application not found for appId: " + appId);
        }
        miTask.setTenantId(extAppInfo.getAppCode());
        MiTaskMetaDo miTaskMeta = miTaskMetaDomainService.getByCode(miTask.getModelCode(), extAppInfo.getAppCode());
        if (miTaskMeta == null) {
            log.info("The process_key does not exist, modelCode={}", miTask.getModelCode());
            //throw new FlowableIllegalArgumentException("The process_key does not exist.");
        }
        miTaskMetaDomainService.checkMiTaskDo(miTask);
        // 检查历史任务的结束状态
        MiTaskDo firstMiTask = miTaskRepository.getMiTaskProcInst(miTask.getProcInstId());

        Set<String> taskUsers = miTaskRepository.findTaskByProcInstId(miTask.getProcInstId()).stream()
                .map(MiTaskDo::getAssignee)
                .map(BpmUser::getUserName)
                .collect(Collectors.toSet());

        if (firstMiTask == null) {
            // 初次创建流程实例采用全量替换
            processInstance.setUpdateMode(REPLACE_MODEL);
        } else {
            if (null != firstMiTask.getVersion() && null != miTask.getUpdateTime()) {
                // try 如果库里数据更新时间比发过来的更新时间更晚，那么发过来的相当于过期数据，直接抛弃
                if (firstMiTask.getVersion() > miTask.getVersion()) {
                    return;
                }
            }
        }
        // 根据updateMode参数的不同，分别调用增量更新和全量更新接口
        // 没有明确采用增量更新的默认按照全量替换
        if (null == processInstance.getUpdateMode()) {
            processInstance.setUpdateMode(REPLACE_MODEL);
        }
        if (UPDATE_MODEL.equals(processInstance.getUpdateMode())) {

            if (null != firstMiTask && null != firstMiTask.getUpdateTime() &&
                    firstMiTask.getUpdateTime().isBefore(Instant.ofEpochMilli(processInstance.getUpdateTime()).atZone(ZoneId.systemDefault()))) {
                miTaskDoFactory.buildMiTaskDo(processInstance, miTask);
                miTaskRepository.updateMiTask(miTask, false);
            }
            if (CollectionUtils.isNotEmpty(processInstance.getTaskList())) {
                for (TaskRepresentation taskRepresentation : processInstance.getTaskList()) {
                    MiTaskDo newMiTask = miTaskDoFactory.buildMiTaskDo(processInstance, taskRepresentation);
                    MiTaskDo miTaskDb = miTaskRepository.findMiTaskDoByTaskId(newMiTask.getTaskId());
                    if (null == miTaskDb) {
                        miTaskRepository.createMiTask(newMiTask);
                    } else if (miTaskDb.getUpdateTime() == null
                            || miTaskDb.getUpdateTime().isBefore(newMiTask.getUpdateTime())) {
                        newMiTask.setTaskId(miTaskDb.getTaskId());
                        miTaskRepository.updateMiTask(newMiTask, false);
                    }
                    taskUsers.add(taskRepresentation.getUsername());
                }
            }
        } else if (REPLACE_MODEL.equals(processInstance.getUpdateMode())) {
            // 全量替换，先删后增
            // 首次创建流程跳过清空历史数据的步骤
            // 删除运行时和历史数据（流程实例 + 任务 + 变量）
            if (null != firstMiTask) {
                miTaskRepository.deleteMiTaskByProcInstId(processInstance.getInstanceId());
            }
            firstMiTask = new MiTaskDo();
            miTaskDoFactory.buildMiTaskDo(processInstance, firstMiTask);
            // 创建一条流程级记录
            List<MiTaskDo> miTaskDos = new ArrayList<>();
            miTaskDos.add(firstMiTask);
            if (CollectionUtils.isNotEmpty(processInstance.getTaskList())) {
                Map<String, TaskRepresentation> taskMap = new HashMap<>();
                for (TaskRepresentation task : processInstance.getTaskList()) {
                    // 如果Map中不存在该taskId，或者当前task的updateTime更大，则更新Map中的记录
                    if (!taskMap.containsKey(task.getTaskId()) || task.getUpdateTime() > taskMap.get(task.getTaskId()).getUpdateTime()) {
                        taskMap.put(task.getTaskId(), task);
                    }
                }
                processInstance.setTaskList(new ArrayList<>(taskMap.values()));
                for (TaskRepresentation taskRepresentation : processInstance.getTaskList()) {
                    MiTaskDo miTaskDo = miTaskDoFactory.buildMiTaskDo(processInstance, taskRepresentation);
                    miTaskDos.add(miTaskDo);

                    taskUsers.add(taskRepresentation.getUsername());
                }
            }
            miTaskRepository.createMiTaskBatch(miTaskDos);
        }

        taskUsers.forEach(user -> messageService.syncAppBadge(user));
    }

    @Override
    public void checkProcessInstance(ProcessInstanceCheckRepresentation processDefinitionRepresentation, String appId) {
        MiTaskDo miTaskMeta = miTaskExtConverter.convert(processDefinitionRepresentation);
        ExternalAppInfoDo extAppInfo = miTaskMetaRepository.findAppInfoByAppId(appId);
        if (extAppInfo == null || miTaskMeta == null) {
            throw new FlowableIllegalArgumentException("Application not found for appId: " + appId);
        }
        extAppInfo.setTenantId(extAppInfo.getId());
        miTaskMetaDomainService.checkMiTaskDoDiff(processDefinitionRepresentation);
    }

    @Override
    public void completeTaskAndCallback(String taskId, String comment, String statusOrOperation, String approveUser,
                                                      boolean needCallback) {
        String filteredComment = EmojiFilter.filterEmoji(comment);
        // 从miTask查询统一待办信息
        MiTaskDo miTaskDo = miTaskRepository.findMiTaskDoByTaskId(taskId);
        if (miTaskDo == null) {
            throw new InfraException(TaskInfraErrorCodeEnum.TASK_NOT_EXISTS, taskId);
        }
        ModelMeta modelMeta = modelMetaRepository.queryByModelCode(miTaskDo.getModelCode());
        if (modelMeta == null) {
            throw new RuntimeException("ModelCode can not find in ModelMeta: " + miTaskDo.getModelCode());
        }
        ExternalAppInfoDo extAppInfo = miTaskMetaRepository.findAppInfoByAppCode(modelMeta.getAppCode());
        if (extAppInfo == null) {
            throw new RuntimeException("ExtAppCode is illegal: " + modelMeta.getAppCode());
        }
        String processKey = miTaskDo.getModelCode();
        ExtProcessItem extProcessItem = externalService.createProcessItemQuery()
                .processKey(processKey).singleResult();
        if (extProcessItem != null) {
            processKey = extProcessItem.getDefKey();
        }
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("processKey", processKey);
        bodyMap.put("instanceId", miTaskDo.getProcInstId());
        bodyMap.put("taskId", miTaskDo.getTaskId());
        bodyMap.put("username", miTaskDo.getAssignee().getUserName());
        bodyMap.put("action", mapOperationToStatus(statusOrOperation));
        bodyMap.put("comment", filteredComment);

        log.info("Callback external request url = {}, appId = {}, body = {}", extAppInfo.getSystemUrl(),
                extAppInfo.getCallbackAppId(), GsonUtils.toJsonFilterNullField(bodyMap));

        X5Response<Object> response;
        // 处理任务，needCallback为false代表是2.0转发来的，不需要再次回调三方接口。true代表是 卡片或者3.0 的同意接口来的，需要回调三方
        if (needCallback) {
            // 回调第三方流程服务，同步任务处理结果
            X5Caller httpClient = new X5Caller(extAppInfo.getCallbackAppId(),
                    extAppInfo.getCallbackAppKey(), extAppInfo.getSystemUrl());
            try {
                response = httpClient.call("", bodyMap, new TypeToken<X5Response<Object>>() {});
                log.info("Callback external response success: {}", GsonUtils.toJsonWtihNullField(response));
            } catch (IOException e) {
                throw new RuntimeException("Callback external call error: " + e.getMessage());
            }
            if (response.getHeader() == null) {
                throw new RuntimeException("Callback external response is null");
            }
            if (!(Integer.valueOf(1).equals(response.getCode()) || Integer.valueOf(200).equals(response.getCode()))) {
                throw new RuntimeException("Callback external response error! Code: " + response.getCode() +
                        ", Msg: " + response.getMsg() + ", Header: " + response.getHeader());
            }
        }
        // 回调统一待办接口成功后更新miTask
        miTaskDo.setEndTime(ZonedDateTime.now());
        miTaskDo.setOperation(UserTaskOperation.findByCode(mapStatusToOperation(statusOrOperation)));
        miTaskDo.setComment(filteredComment);
        miTaskRepository.updateMiTask(miTaskDo);
        // 统一待办已办分类写入，将统一待办modelCode写入cate表的CATEGORY_字段
        hiTaskModelCodeRepository.saveHiTaskInstanceModelCodeRelation(miTaskDo.getAssignee(),
                miTaskDo.getModelCode());
        // 异步调用1.0/2.0的统一代办各表完成逻辑
        asyncCompleteInBPM2ExternalTask(miTaskDo.getTaskId());
    }

    private String mapOperationToStatus(String status) {
        if (TASK_QUERY_STATUS_MAP.containsValue(status)) {
            return status;
        }
        return TASK_QUERY_STATUS_MAP.get(status);
    }

    private String mapStatusToOperation(String operation) {
        if (TASK_QUERY_STATUS_MAP.containsKey(operation)) {
            return operation;
        }
        return TASK_QUERY_STATUS_MAP.getKey(operation);
    }

    // 异步执行2.0的统一待办任务完成逻辑
    public void asyncCompleteInBPM2ExternalTask(String taskId) {
        ExternalThreadPool.getInstance().execute(() -> {
            // 旧逻辑用主键id完成的
            List<ExtRuTaskInstPo> extRuTaskInstPos = extRuTaskInstMapper.selectByTaskKey(taskId);
            if (extRuTaskInstPos != null && extRuTaskInstPos.size() == 1) {
                externalService.complete(extRuTaskInstPos.get(0).getId());
            } else {
                log.error("Complete BPM2 external error! TaskId: {}, Ext Ru Table Result: {}",
                        taskId, extRuTaskInstPos);
            }
        });
    }
}


