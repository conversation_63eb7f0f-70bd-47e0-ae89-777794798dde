package com.mi.oa.infra.mibpm.application.task.converter;

import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskAttribute;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.ApprovalStrategy;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.sdk.dto.ModelDto;
import org.flowable.bpmn.model.MultiInstanceLoopCharacteristics;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/23 14:26
 */
public class TaskDetailRepsBuilder {

    private final TaskDetailResp taskDetailReps;
    private TaskDo taskDo;
    private ProcessInstanceDo processInstanceDo;
    private List<TaskDo> historicTasks;
    private List<UserTaskOperation> userTaskOperations;
    private Map<String, Object> formDef;
    private Map<String, Object> formData;
    private TaskDetailResp.Form form;
    private Map<String, Object> variables;
    private Integer oldType;
    private String pinStatus;
    private List<ApprovalStrategy> strategyList;

    private Boolean isVetoUser;

    private ModelDto modelDto;

    public TaskDetailRepsBuilder() {
        this.taskDetailReps = new TaskDetailResp();
    }

    public TaskDetailRepsBuilder taskDo(TaskDo taskDo) {
        this.taskDo = taskDo;
        return this;
    }

    public TaskDetailRepsBuilder processInstanceDo(ProcessInstanceDo processInstanceDo) {
        this.processInstanceDo = processInstanceDo;
        return this;
    }

    public TaskDetailRepsBuilder historicTasks(List<TaskDo> historicTasks) {
        this.historicTasks = historicTasks;
        return this;
    }

    public TaskDetailRepsBuilder userTaskOperation(List<UserTaskOperation> userTaskOperations) {
        this.userTaskOperations = userTaskOperations;
        return this;
    }

    public TaskDetailRepsBuilder variables(Map<String, Object> variables) {
        this.variables = variables;
        return this;
    }

    public TaskDetailRepsBuilder formDef(Map<String, Object> formDef) {
        this.formDef = formDef;
        return this;
    }

    public TaskDetailRepsBuilder formData(Map<String, Object> formData) {
        this.formData = formData;
        return this;
    }

    public TaskDetailRepsBuilder oldType(Integer oldType) {
        this.oldType = oldType;
        return this;
    }

    public TaskDetailRepsBuilder form(TaskDetailResp.Form form) {
        this.form = form;
        return this;
    }

    public TaskDetailRepsBuilder pinStatus(String pinStatus) {
        this.pinStatus = pinStatus;
        return this;
    }

    public TaskDetailRepsBuilder strategyList(List<ApprovalStrategy> strategyList) {
        this.strategyList = strategyList;
        return this;
    }

    public TaskDetailRepsBuilder modelDto(ModelDto modelDto) {
        this.modelDto = modelDto;
        return this;
    }

    public TaskDetailRepsBuilder isVetoUser(Boolean isVetoUser) {
        this.isVetoUser = isVetoUser;
        return this;
    }

    public TaskDetailResp build() {
        if (Objects.nonNull(taskDo)) {
            this.taskDetailReps.setTaskId(taskDo.getTaskId());
            this.taskDetailReps.setTaskName(taskDo.getTaskName());
            this.taskDetailReps.setTaskDefinitionId(taskDo.getTaskDefinitionId());
            this.taskDetailReps.setTaskDefinitionKey(taskDo.getTaskDefinitionKey());
            this.taskDetailReps.setPriority(taskDo.getPriority());
            this.taskDetailReps.setAssignee(taskDo.getAssignee());
            this.taskDetailReps.setCandidates(taskDo.getCandidates());
            this.taskDetailReps.setScopeId(taskDo.getScopeId());
            this.taskDetailReps.setCreateTime(taskDo.getCreateTime());
            this.taskDetailReps.setEndTime(taskDo.getEndTime());
            this.taskDetailReps.setDueDate(taskDo.getDueDate());
            this.taskDetailReps.setIsVetoUser(taskDo.getIsVetoUser());
            UserTaskWrapper userTaskWrapper = taskDo.getUserTaskWrapper();
            if (null != userTaskWrapper) {
                this.taskDetailReps.setDisableAppOperation(userTaskWrapper.getDisableAppOperation());
                this.taskDetailReps.setSignType(userTaskWrapper.getSignType());
            }
        }
        if (Objects.nonNull(processInstanceDo)) {
            this.taskDetailReps.setProcessDefinitionId(processInstanceDo.getProcessDefinitionId());
            this.taskDetailReps.setProcessInstanceId(processInstanceDo.getProcessInstanceId());
            this.taskDetailReps.setModelCode(processInstanceDo.getModelCode());
            this.taskDetailReps.setProcessInstanceStartUser(processInstanceDo.getStartUser());
            this.taskDetailReps.setProcessInstanceName(processInstanceDo.getProcessInstanceName());
            this.taskDetailReps.setBusinessKey(processInstanceDo.getBusinessKey());
            this.taskDetailReps.setCategoryCode(processInstanceDo.getCategoryCode());
            Map<String, Object> processVariables = processInstanceDo.getProcessVariables();
            if (Objects.nonNull(processInstanceDo.getProcessInstanceStatus())) {
                this.taskDetailReps.setProcessInstanceStatus(processInstanceDo.getProcessInstanceStatus());
            } else {
                String status = (String) processVariables.get(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS);
                ProcessInstanceStatus processInstanceStatus = ProcessInstanceStatus.getStatus(status,
                        processInstanceDo.getEndTime());
                this.taskDetailReps.setProcessInstanceStatus(processInstanceStatus);
            }
        }
        if (Objects.nonNull(historicTasks)) {
            List<TaskDetailResp.Task> taskList = historicTasks.stream()
                    .map(this::buildTask).collect(Collectors.toList());
            this.taskDetailReps.setTaskList(taskList);
        }
        if (Objects.nonNull(userTaskOperations)) {
            TaskDetailResp.TaskConfig taskConfig = new TaskDetailResp.TaskConfig();
            taskConfig.setOperationList(this.userTaskOperations);
            if (null != this.variables) {
                taskConfig.setVariables(this.variables);
            }
            if (null != this.strategyList) {
                taskConfig.setStrategyList(this.strategyList);
            }
            if (Objects.nonNull(taskDo) && taskDo.getUserTaskWrapper() != null) {
                taskConfig.setRequiredComments(taskDo.getUserTaskWrapper().getRequiredComments());
            }
            this.taskDetailReps.setTaskConfig(taskConfig);
        }
        if (Objects.nonNull(formDef) || Objects.nonNull(formData)) {
            TaskDetailResp.Form form = new TaskDetailResp.Form();
            form.setSchema(this.formDef);
            form.setData(this.formData);
            this.taskDetailReps.setForm(form);
        }
        if (Objects.nonNull(form)) {
            this.taskDetailReps.setForm(form);
        }
        if (Objects.nonNull(modelDto)) {
            taskDetailReps.setModelName(modelDto.getName());
            taskDetailReps.setModelEnName(modelDto.getEnName());
        }
        taskDetailReps.setOldType(oldType);
        taskDetailReps.setPinStatus(pinStatus);
        return this.taskDetailReps;
    }

    @NotNull
    private TaskDetailResp.Task buildTask(TaskDo task) {
        TaskDetailResp.Task historicTask = new TaskDetailResp.Task();
        historicTask.setTaskId(task.getTaskId());
        historicTask.setTaskName(task.getTaskName());
        historicTask.setTaskDefinitionId(task.getTaskDefinitionId());
        historicTask.setTaskDefinitionKey(task.getTaskDefinitionKey());
        historicTask.setPriority(task.getPriority());
        historicTask.setAssignee(task.getAssignee());
        historicTask.setScopeId(task.getScopeId());
        historicTask.setCreateTime(task.getCreateTime());
        historicTask.setEndTime(task.getEndTime());
        historicTask.setDueDate(task.getDueDate());
        historicTask.setPredict(task.isPredict());
        historicTask.setActivityType(task.getActivityType());
        historicTask.setReviewed(task.isReviewed());
        historicTask.setCreateUser(task.getCreateUser());
        TaskAttribute taskAttribute = task.getTaskAttribute();
        if (Objects.nonNull(taskAttribute)) {
            historicTask.setOperation(task.getTaskAttribute().getOperation());
            historicTask.setComment(task.getTaskAttribute().getComment());
            historicTask.setClient(task.getTaskAttribute().getClient());
        }
        if (Objects.nonNull(task.getUserTaskWrapper())) {
            UserTaskSignType signType = task.getUserTaskWrapper().getSignType();
            if (null == signType && null != task.getUserTaskWrapper().getUserTask()) {
                signType = getTaskApproveType(
                        task.getUserTaskWrapper().getUserTask().getLoopCharacteristics());
            }
            historicTask.setSignType(signType);
        }
        return historicTask;
    }

    /**
     * 老流程专用
     * 根据multiInstanceLoopCharacteristics completionCondition 判定节点审批类型
     *
     * @param loopCharacteristics
     * @return
     */
    private UserTaskSignType getTaskApproveType(MultiInstanceLoopCharacteristics loopCharacteristics) {

        if (loopCharacteristics == null) {
            return null;
        }

        String completionCondition = loopCharacteristics.getCompletionCondition();
        boolean sequential = loopCharacteristics.isSequential();

        // 单签
        if ("".equals(completionCondition)) {
            return UserTaskSignType.SINGLE;
        }
        //非会签
        if ("${nrOfCompletedInstances >= 1}".equals(completionCondition)) {
            return UserTaskSignType.PARALLEL_ONE;
        }
        //会签
        if ("${status == 'rollbackTask' ||  status == 'interrupt' || nrOfActiveInstances == 0}".equals(
                completionCondition) && !sequential) {
            return UserTaskSignType.PARALLEL_ALL;
        }

        //顺签
        if ("${status == 'rollbackTask' ||  status == 'interrupt' || nrOfActiveInstances == 0}".equals(
                completionCondition) && sequential) {
            return UserTaskSignType.SEQUENTIAL;
        }
        return null;
    }

    public TaskDo getTaskDo() {
        return taskDo;
    }

    public ProcessInstanceDo getProcessInstanceDo() {
        return processInstanceDo;
    }

    public List<TaskDo> getHistoricTasks() {
        return historicTasks;
    }

    public List<UserTaskOperation> getUserTaskOperations() {
        return userTaskOperations;
    }

    public Map<String, Object> getFormDef() {
        return formDef;
    }

    public Map<String, Object> getFormData() {
        return formData;
    }

    public TaskDetailResp.Form getForm() {
        return form;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public Integer getOldType() {
        return oldType;
    }
}
