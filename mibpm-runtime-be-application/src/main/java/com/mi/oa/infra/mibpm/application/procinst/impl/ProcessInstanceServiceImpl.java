package com.mi.oa.infra.mibpm.application.procinst.impl;

import com.mi.oa.infra.mibpm.application.process.dto.resp.ProcessResp;
import com.mi.oa.infra.mibpm.application.process.service.ProcessService;
import com.mi.oa.infra.mibpm.application.procinst.converter.ProcessInstanceCreateReqConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.ProcessInstanceRecallReqConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.ProcessInstanceRepsConverter;
import com.mi.oa.infra.mibpm.application.procinst.converter.ProcessInstanceTerminateReqConverter;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.BpmOrgDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.CategoryResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ModelMetaDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcessInstanceResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceCreateReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceRecallReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceTerminateReq;
import com.mi.oa.infra.mibpm.application.proinst.service.ProcessInstanceService;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.AuthorizeDimension;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateRecalledEvent;
import com.mi.oa.infra.mibpm.common.event.OperateTerminatedEvent;
import com.mi.oa.infra.mibpm.common.event.SubmitTaskAutoCompleteEvent;
import com.mi.oa.infra.mibpm.common.model.BpmOrg;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.service.TaskDomainService;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.infra.category.entity.CategoryDto;
import com.mi.oa.infra.mibpm.infra.category.repository.CategoryRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ProcessInstanceRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.infra.remote.sdk.OrgRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.PredictRemoteService;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.organization.rep.OrgVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/9 18:26
 */
@Service
@Slf4j
public class ProcessInstanceServiceImpl implements ProcessInstanceService {

    @Autowired
    private ProcessInstanceCreateReqConverter processInstanceCreateReqConverter;
    @Autowired
    private ProcessInstanceTerminateReqConverter processInstanceTerminateReqConverter;
    @Autowired
    private ProcessInstanceRecallReqConverter processInstanceRecallReqConverter;
    @Autowired
    private ProcessInstanceRepsConverter processInstanceRepsConverter;
    @Autowired
    private ProcessInstanceDomainService processInstanceDomainService;
    @Autowired
    private ProcessInstanceRepository processInstanceRepository;
    @Autowired
    private EventPublisher eventPublisher;
    @Autowired
    private TaskDomainService taskDomainService;
    @Autowired
    private PredictRemoteService predictRemoteService;
    @Autowired
    private ModelsAuthorityRemote authorityRemote;
    @Autowired
    private ProcessService processService;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private OrgRemoteService orgRemoteService;
    @Autowired
    private CategoryRepository categoryRepository;

    @Override
    public ProcessInstanceResp startProcessInstance(ProcessInstanceCreateReq processInstanceCreateReq) {
        // dto -> do
        ProcessInstanceDo processInstanceDo = processInstanceCreateReqConverter.dtoToDo(
                processInstanceCreateReq);
        // 填充将要创建的流程实例
        processInstanceDomainService.fillStartProcessInstance(processInstanceDo);
        // 填充流程实例变量
        processInstanceDomainService.fillProcessVariables(processInstanceDo, processInstanceCreateReq.getFormData());
        // 填充流程实例名称
        processInstanceDomainService.fillProcessInstanceName(processInstanceDo, processInstanceCreateReq.getFormData());
        // 填充任务实例的各任务节点的处理人
        processInstanceDomainService.fillProcessInstanceTaskAssignee(processInstanceDo,
                processInstanceCreateReq.getTaskAssignees());
        // 校验流程实例发起权限等
        processInstanceDomainService.checkStartProcessInstance(processInstanceDo, processInstanceCreateReq.getProcessDefinitionVersion());
        // 启动流程实例
        processInstanceDomainService.startProcessInstance(processInstanceDo);
        // 设置事件基础信息
        SubmitTaskAutoCompleteEvent submitTaskAutoCompleteEvent = SubmitTaskAutoCompleteEvent.builder()
                .processDefinitionId(processInstanceDo.getProcessDefinitionId())
                .processInstanceId(processInstanceDo.getProcessInstanceId())
                .businessKey(processInstanceDo.getBusinessKey())
                .formData(processInstanceCreateReq.getFormData())
                .comment(processInstanceCreateReq.getComment())
                .build();
        submitTaskAutoCompleteEvent.setId(processInstanceDo.getProcessInstanceId());
        submitTaskAutoCompleteEvent.setIdentifier(EventIdentify.SUBMIT_TASK_AUTO_COMPLETE.name());
        submitTaskAutoCompleteEvent.setTimestamp(System.currentTimeMillis());
        // 发送自动跳过发起任务事件
        log.info("publish submit task auto-complete event");
        eventPublisher.publish(submitTaskAutoCompleteEvent);
        // do -> dto
        return processInstanceRepsConverter.doToDto(processInstanceDo);
    }

    @Override
    public void terminateProcessInstance(ProcessInstanceTerminateReq processInstanceTerminateReq) {
        // dto -> do
        ProcessInstanceDo processInstanceDo = processInstanceTerminateReqConverter.dtoToDo(processInstanceTerminateReq);
        // 查询流程实例（检查是否存在）
        ProcessInstanceDo processInstanceDoDb = processInstanceDomainService.queryProcessInstance(
                processInstanceDo.getProcessInstanceId());
        // 填充流程实例的操作人
        processInstanceDomainService.fillProcessInstanceOperator(processInstanceDoDb,
                processInstanceTerminateReq.getOperator());
        // 终止对应的任务 应在终止实例前
        List<TaskDo> taskDos = taskDomainService.queryTaskList(processInstanceDo.getProcessInstanceId());
        // 检查流程实例能否终止
        processInstanceDomainService.checkTerminateProcessInstance(processInstanceDoDb, taskDos);
        // 设置流程变量
        processInstanceDomainService.loadProcessVariables(processInstanceDoDb);
        Map<String, Object> processVariables = processInstanceDoDb.getProcessVariables();
        processVariables.put(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS, UserTaskOperation.TERMINATE.getCode());
        processInstanceDomainService.setProcessVariables(processInstanceDoDb);
        // 批量终止任务
        taskDomainService.terminateTasks(taskDos, processInstanceTerminateReq.getComment(),
                processInstanceTerminateReq.getAutoOperationType());
        // 终止流程实例
        processInstanceDomainService.terminateProcessInstance(processInstanceDoDb);

        // 构建事件
        OperateTerminatedEvent operateTerminatedEvent = OperateTerminatedEvent.builder()
                .processInstanceId(processInstanceDoDb.getProcessInstanceId())
                .processDefinitionId(processInstanceDoDb.getProcessDefinitionId())
                .operator(processInstanceTerminateReq.getOperator())
                .comment(processInstanceTerminateReq.getComment())
                .autoOperationType(processInstanceTerminateReq.getAutoOperationType())
                .build();
        // 设置事件基础信息
        operateTerminatedEvent.setId(processInstanceDo.getProcessInstanceId());
        operateTerminatedEvent.setIdentifier(EventIdentify.OPERATE_TERMINATED.name());
        operateTerminatedEvent.setTimestamp(System.currentTimeMillis());
        //发送流程终止事件
        eventPublisher.publish(operateTerminatedEvent);
    }


    @Override
    public void recallProcessInstance(ProcessInstanceRecallReq processInstanceRecallReq) {
        // dto -> do
        ProcessInstanceDo processInstanceDo = processInstanceRecallReqConverter.dtoToDo(processInstanceRecallReq);
        // 查询流程实例（检查是否存在）
        ProcessInstanceDo processInstanceDoDb = processInstanceDomainService.queryProcessInstance(
                processInstanceDo.getProcessInstanceId());
        // 填充流程实例的操作人
        processInstanceDomainService.fillProcessInstanceOperator(processInstanceDoDb,
                processInstanceRecallReq.getOperator());
        // 检查流程实例能否撤回
        processInstanceDomainService.checkProcessInstanceRecall(processInstanceDoDb);
        // 撤回对应的任务
        List<TaskDo> taskDos = taskDomainService.queryTaskList(processInstanceDoDb.getProcessInstanceId());
        // 批量终止任务
        taskDomainService.recallTasks(taskDos, processInstanceRecallReq.getComment());
        // 撤回流程实例
        processInstanceDomainService.recall(processInstanceDoDb);

        // 构建事件
        OperateRecalledEvent operateRecalledEvent = OperateRecalledEvent.builder()
                .processInstanceId(processInstanceDoDb.getProcessInstanceId())
                .processDefinitionId(processInstanceDoDb.getProcessDefinitionId())
                .operator(processInstanceRecallReq.getOperator())
                .comment(processInstanceRecallReq.getComment())
                .build();
        // 设置事件基础信息
        operateRecalledEvent.setId(processInstanceDo.getProcessInstanceId());
        operateRecalledEvent.setIdentifier(EventIdentify.OPERATE_RECALLED.name());
        operateRecalledEvent.setTimestamp(System.currentTimeMillis());
        //发送流程撤回事件
        eventPublisher.publish(operateRecalledEvent);
        // 发送流程预测事件
        predictRemoteService.sendPredictMessage(processInstanceDo);
    }

    @Override
    public ProcessInstanceResp queryProcessInstance(String processInstanceId) {
        ProcessInstanceDo processInstanceDo = processInstanceDomainService.queryProcessInstance(
                processInstanceId);
        return processInstanceRepsConverter.doToDto(processInstanceDo);
    }

    @Override
    public byte[] genProcessDiagram(String processInstanceId) {
        return processInstanceRepository.genProcessDiagram(processInstanceId);
    }

    @Override
    public Boolean checkStartProInsAuth(String modelCode, BpmUser bpmUser) {
        // 若是超级管理员则允许提交
        if (authorityRemote.isSuperAdmin(bpmUser.getUserName())) {
            return true;
        }
        //
        ProcessResp processResp = processService.queryOneProcess(modelCode);
        if (null != processResp && null != processResp.getVisibleConfig() && null != processResp.getVisibleConfig().getDepartments()
                && processResp.getVisibleConfig().getDepartments().contains("MI")) {
            return true;
        }
        // 检查是否为可提交者
        List<String> authModelCodes = authorityRemote.queryDataResource(bpmUser.getUserName(), AuthorizeDimension.BPMN_USER);
        if (CollectionUtils.isNotEmpty(authModelCodes)) {
            return authModelCodes.contains(modelCode);
        }
        return false;
    }

    @Override
    public ModelMetaDto getModelMeta(String modelCode) {
        ProcessResp processResp = processService.queryOneProcess(modelCode);
        if (null != processResp) {
            List<String> userList = new ArrayList<>();
            List<String> owners = processResp.getOwners();
            if (CollectionUtils.isNotEmpty(owners)) {
                userList.addAll(owners);
            }
            List<String> businessOwner = processResp.getBusinessOwner();
            if (CollectionUtils.isNotEmpty(businessOwner)) {
                userList.addAll(businessOwner);
            }
            ModelMetaDto meta = processInstanceRepsConverter.toDto(processResp);
            if (StringUtils.isNotEmpty(processResp.getOwnerDept())) {
                List<OrgVO> voList = orgRemoteService.listOrg(processResp.getOwnerDept());
                List<BpmOrgDto> collect = voList.stream().map(o -> BpmOrgDto.builder().deptName(o.getOrgName()).deptId(o.getOrgCode()).build()).collect(Collectors.toList());
                BpmOrg build =
                        BpmOrg.builder().orgId(processResp.getOwnerDept()).fullOrgDesc(GsonUtils.toJsonFilterNullField(collect)).build();
                meta.setOwnerDept(build);
            }
            if (CollectionUtils.isNotEmpty(userList)) {
                List<BpmUser> bpmUsers = accountRemoteService.listUsers(userList);
                Map<String, BpmUser> userMap = bpmUsers.stream().collect(Collectors.toMap(i -> i.getUserName(), i -> i, (k1, k2) -> k1));
                if (CollectionUtils.isNotEmpty(owners)) {
                    List<BpmUser> ownerUsers = owners.stream().map(userMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                    meta.setOwners(ownerUsers);
                }
                if (CollectionUtils.isNotEmpty(businessOwner)) {
                    List<BpmUser> businessOwnerUsers =
                            businessOwner.stream().map(userMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                    meta.setBusinessOwner(businessOwnerUsers);
                }
            }
            if (StringUtils.isNotBlank(meta.getCategoryCode())) {
                List<CategoryDto> all = categoryRepository.findAll();
                Map<String, CategoryDto> collect = all.stream().collect(Collectors.toMap(i -> i.getCode(), i -> i, (k1, k2) -> k1));
                CategoryDto categoryDto = collect.get(meta.getCategoryCode());
                CategoryResp cate = new CategoryResp();
                cate.setCategoryName(categoryDto.getName());
                cate.setCategoryEnName(categoryDto.getEnName());
                cate.setCategoryCode(categoryDto.getCode());
                meta.setCategory(cate);
                if (StringUtils.isNotBlank(categoryDto.getParentCode())) {
                    CategoryDto parentCate = collect.get(categoryDto.getParentCode());
                    if (Objects.nonNull(parentCate)) {
                        CategoryResp parentCateResp = new CategoryResp();
                        parentCateResp.setCategoryName(parentCate.getName());
                        parentCateResp.setCategoryEnName(parentCate.getEnName());
                        parentCateResp.setCategoryCode(parentCate.getCode());
                        parentCateResp.setChild(cate);
                        meta.setCategory(parentCateResp);
                    }
                }
            }
            return meta;
        }
        return null;
    }
}
