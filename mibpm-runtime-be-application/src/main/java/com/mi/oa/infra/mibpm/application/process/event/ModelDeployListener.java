package com.mi.oa.infra.mibpm.application.process.event;

import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.deploy.entity.DeployEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = "${rocketmq.task-predict-deploy-group}", topic = "${rocketmq.task-predict-deploy-topic}", consumeMode = ConsumeMode.CONCURRENTLY, messageModel = MessageModel.BROADCASTING)
public class ModelDeployListener implements RocketMQListener<DeployEvent> {

    @Autowired
    private ModelRemoteService modelRemoteService;

    @Override
    public void onMessage(DeployEvent deployEvent) {
        // 记录接收到的事件
        log.info("receive model deploy event: {}", deployEvent);

        // 清除指定应用的模型缓存
        if (deployEvent == null) {
            return;
        }

        modelRemoteService.clearModelCacheByAppCode(deployEvent.getAppCode());
        modelRemoteService.clearModelCacheByCode(deployEvent.getProcessKey());
    }

}
