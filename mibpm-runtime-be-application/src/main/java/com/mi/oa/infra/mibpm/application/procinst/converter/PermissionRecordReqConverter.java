package com.mi.oa.infra.mibpm.application.procinst.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.PermissionRecordsDto;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionRecordsDo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/18 10:55
 */
@Mapper(componentModel = "spring")
public interface PermissionRecordReqConverter {

    List<PermissionRecordsDto> doToDto(List<PermissionRecordsDo> doList);
}
