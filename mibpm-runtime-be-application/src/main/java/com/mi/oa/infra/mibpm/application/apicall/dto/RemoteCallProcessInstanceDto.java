package com.mi.oa.infra.mibpm.application.apicall.dto;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * 流程实例
 *
 * <AUTHOR>
 * @date 2022/05/18 16:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCallProcessInstanceDto {

    /**
     * 分类编码
     */
    protected String categoryCode;
    /**
     * 模型编码
     */
    protected String modelCode;
    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
    /**
     * 流程定义名称
     */
    protected String processDefinitionName;
    /**
     * 流程定义版本
     */
    protected Integer processDefinitionVersion;
    /**
     * 业务唯一编码
     */
    protected String businessKey;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;
    /**
     * 流程实例名称
     */
    protected String processInstanceName;

    /**
     * 流程状态
     */
    protected ProcessInstanceStatus processInstanceStatus;
    /**
     * 描述
     */
    protected String description;
    /**
     * 流程开始时间
     */
    protected ZonedDateTime startTime;
    /**
     * 流程结束时间
     */
    protected ZonedDateTime endTime;
    /**
     * 发起人
     */
    protected String startUserId;
    /**
     * 发起人
     */
    protected RemoteCallBpmUserDto startUser;
    /**
     * 当前操作人
     */
    protected RemoteCallBpmUserDto operator;
}
