package com.mi.oa.infra.mibpm.application.userconfig.converter;

import com.mi.oa.infra.mibpm.application.message.dto.req.PermissionCallBackReq;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserPermissionDo;
import org.mapstruct.Mapper;

/**
 * @<PERSON> lixinghan
 * @Date 2024/7/19 10:44
 */
@Mapper(componentModel = "spring")
public interface ApplyUserPermissionReqConverter {
   UserPermissionDo dtoToDo(PermissionCallBackReq permissionCallBackReq);
}
