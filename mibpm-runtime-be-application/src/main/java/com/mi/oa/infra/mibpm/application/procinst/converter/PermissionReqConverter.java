package com.mi.oa.infra.mibpm.application.procinst.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RequirePermissionDto;
import com.mi.oa.infra.mibpm.domain.procinst.model.PermissionDo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 18:19
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface PermissionReqConverter {

    List<RequirePermissionDto> doToDto(List<PermissionDo> doList);
}
